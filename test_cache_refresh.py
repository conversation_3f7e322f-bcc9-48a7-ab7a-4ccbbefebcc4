#!/usr/bin/env python3
"""
Test script to force refresh configuration cache
"""

import sys
import os

# Add the shared directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'azure-csv-processing', 'shared'))

def test_cache_refresh():
    """Test cache refresh functionality"""
    print("=== Testing Cache Refresh ===")
    
    try:
        from azure_config import refresh_configuration, Config, get_job_config
        
        print("1. Forcing configuration cache refresh...")
        refresh_configuration()
        
        print("2. Testing refreshed configuration:")
        job_config = get_job_config('FIMASTSALES_')
        if job_config:
            print(f"   ValidationRules raw: {repr(job_config.get('ValidationRules', ''))}")
            print(f"   TransformationRules raw: {repr(job_config.get('TransformationRules', ''))}")
        
        print("3. Testing Config class with refreshed data:")
        config = Config('FIMASTSALES_')
        print(f"   Validation Rules: {config.validation_rules}")
        print(f"   Transformation Rules: {config.transformation_rules}")
        
        print("\n✅ Cache refresh test completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Cache refresh test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_cache_refresh()
    sys.exit(0 if success else 1)
