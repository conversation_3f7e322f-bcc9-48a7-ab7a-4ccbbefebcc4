#!/usr/bin/env python3
"""
Test script for azure-ftp-etl dynamic date format loading
Tests the get_date_format() function with various scenarios
"""

import sys
import os

# Add azure-ftp-etl shared directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'azure-ftp-etl', 'shared'))

def test_date_format_loading():
    """Test the dynamic date format loading functionality"""
    print("=" * 60)
    print("TESTING AZURE-FTP-ETL DATE FORMAT LOADING")
    print("=" * 60)
    
    try:
        # Import the function
        from azure_config import get_date_format, get_date_format_source, DATE_FORMAT
        
        print(f"✓ Successfully imported get_date_format function")
        print(f"✓ Hardcoded fallback DATE_FORMAT: '{DATE_FORMAT}'")
        print()
        
        # Test 1: Load date format (will try table first, then fallback)
        print("TEST 1: Loading date format...")
        print("-" * 40)
        
        current_date_format = get_date_format()
        source = get_date_format_source()
        
        print(f"✓ Date format loaded: '{current_date_format}'")
        print(f"✓ Source: {source}")
        print()
        
        # Test 2: Validate format
        print("TEST 2: Validating format...")
        print("-" * 40)
        
        import re
        pattern = r'^\d{2}-\d{2}-\d{2}$'
        is_valid = bool(re.match(pattern, current_date_format))
        
        print(f"✓ Format validation (MM-dd-yy): {is_valid}")
        if is_valid:
            print(f"✓ Format '{current_date_format}' matches MM-dd-yy pattern")
        else:
            print(f"❌ Format '{current_date_format}' does NOT match MM-dd-yy pattern")
        print()
        
        # Test 3: Test caching (second call should use cache)
        print("TEST 3: Testing caching...")
        print("-" * 40)
        
        second_call = get_date_format()
        print(f"✓ Second call result: '{second_call}'")
        print(f"✓ Results match: {current_date_format == second_call}")
        print()
        
        # Test 4: Show configuration summary
        print("TEST 4: Configuration Summary")
        print("-" * 40)
        
        print(f"Current Date Format: '{current_date_format}'")
        print(f"Configuration Source: {source}")
        print(f"Fallback Value: '{DATE_FORMAT}'")
        
        if source == "table":
            print("✅ SUCCESS: Using date format from Azure Table Storage")
        elif source == "static":
            print("⚠️  FALLBACK: Using hardcoded date format (table unavailable or invalid)")
        
        print()
        print("=" * 60)
        print("DATE FORMAT LOADING TEST COMPLETED")
        print("=" * 60)
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the correct directory")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sftp_downloader_integration():
    """Test that sftp_downloader can use the new function"""
    print("\n" + "=" * 60)
    print("TESTING SFTP_DOWNLOADER INTEGRATION")
    print("=" * 60)
    
    try:
        from sftp_downloader import build_file_inventory
        from azure_config import get_date_format
        
        print("✓ Successfully imported sftp_downloader functions")
        
        # Test that build_file_inventory can get date format
        current_date = get_date_format()
        print(f"✓ Date format for inventory building: '{current_date}'")
        
        # Mock test - just verify the function can be called
        print("✓ Integration test passed - functions can access date format")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

if __name__ == "__main__":
    print("Starting azure-ftp-etl date format tests...")
    
    success1 = test_date_format_loading()
    success2 = test_sftp_downloader_integration()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED!")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED!")
        sys.exit(1)
