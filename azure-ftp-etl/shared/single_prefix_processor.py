import tempfile
import os
import sys
import time
import logging
from typing import Dict, List, Optional

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from azure_config import (
    blob_service_client,
    COMPRESSED_CONTAINER,
    DECOMPRESSED_CONTAINER,
    get_file_prefixes,
    get_prefix_source,
    container_for_compressed,
    container_for_decompressed,
    get_source_configs,  # Phase 3: New config support
    build_paths_non_dealer,  # Phase 3: New path building
    build_paths_dealer,  # Phase 5: Dealer path building
)
from sftp_downloader import list_remote_files_once, build_file_inventory, download_files
from file_decompressor import decompress_files

# Simple per-process cache to avoid repeated container creation attempts in one run
_container_checked = set()

def upload_to_blob(file_path, container_name, blob_name, prefix_id=None):
    """Upload file to blob storage (with non-intrusive logging and prefix identification)"""
    try:
        # Create container if it doesn't exist (only once per run)
        if container_name not in _container_checked:
            try:
                container_client = blob_service_client.get_container_client(container_name)
                container_client.create_container()
                log_prefix = f"[BLOB-{prefix_id}]" if prefix_id else "[BLOB]"
                logging.info(f"{log_prefix} Created container {container_name}")
            except Exception as container_error:
                if "ContainerAlreadyExists" in str(container_error):
                    log_prefix = f"[BLOB-{prefix_id}]" if prefix_id else "[BLOB]"
                    logging.info(f"{log_prefix} Container {container_name} already exists")
                else:
                    log_prefix = f"[BLOB-{prefix_id}]" if prefix_id else "[BLOB]"
                    logging.info(f"{log_prefix} Container creation info: {container_error}")
            _container_checked.add(container_name)

        size_bytes = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        log_prefix = f"[BLOB-{prefix_id}]" if prefix_id else "[BLOB]"
        
        if size_bytes < 1024 * 1024:
            logging.info(f"{log_prefix} Uploading {blob_name} to {container_name} ({size_bytes} bytes)")
        else:
            size_mb = size_bytes / (1024 * 1024)
            logging.info(f"{log_prefix} Uploading {blob_name} to {container_name} ({size_mb:.2f} MB)")
        
        t0 = time.time()
        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)
        with open(file_path, "rb") as data:
            blob_client.upload_blob(data, overwrite=True)
        dt = time.time() - t0
        logging.info(f"{log_prefix} ✓ Uploaded: {blob_name} in {dt:.2f}s")
        return True
    except Exception as e:
        log_prefix = f"[BLOB-{prefix_id}]" if prefix_id else "[BLOB]"
        logging.error(f"{log_prefix} Upload error for {blob_name}: {e}")
        return False


# ============================================================================
# PHASE 2: Dual Upload Functions
# ============================================================================

def upload_to_blob_new_path(file_path, blob_path, prefix_id=None):
    """Upload file to blob storage using new path-based approach (single container).

    Args:
        file_path: Local file path to upload
        blob_path: Full blob path within LANDING_CONTAINER (e.g., "RO_Time/AO_RO_TIME/ao_ro_time-compressed/file.zip")
        prefix_id: Optional prefix identifier for logging

    Returns:
        bool: True if successful, False otherwise
    """
    from azure_config import LANDING_CONTAINER

    try:
        # Create landing container if it doesn't exist (only once per run)
        if LANDING_CONTAINER not in _container_checked:
            try:
                container_client = blob_service_client.get_container_client(LANDING_CONTAINER)
                container_client.create_container()
                log_prefix = f"[BLOB-{prefix_id}]" if prefix_id else "[BLOB]"
                logging.info(f"{log_prefix} Created landing container {LANDING_CONTAINER}")
            except Exception as container_error:
                if "ContainerAlreadyExists" in str(container_error):
                    log_prefix = f"[BLOB-{prefix_id}]" if prefix_id else "[BLOB]"
                    logging.info(f"{log_prefix} Landing container {LANDING_CONTAINER} already exists")
                else:
                    log_prefix = f"[BLOB-{prefix_id}]" if prefix_id else "[BLOB]"
                    logging.info(f"{log_prefix} Landing container creation info: {container_error}")
            _container_checked.add(LANDING_CONTAINER)

        size_bytes = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        log_prefix = f"[BLOB-{prefix_id}]" if prefix_id else "[BLOB]"

        if size_bytes < 1024 * 1024:
            logging.info(f"{log_prefix} Uploading to new path: {blob_path} ({size_bytes} bytes)")
        else:
            size_mb = size_bytes / (1024 * 1024)
            logging.info(f"{log_prefix} Uploading to new path: {blob_path} ({size_mb:.2f} MB)")

        t0 = time.time()
        blob_client = blob_service_client.get_blob_client(container=LANDING_CONTAINER, blob=blob_path)
        with open(file_path, "rb") as data:
            blob_client.upload_blob(data, overwrite=True)
        dt = time.time() - t0
        logging.info(f"{log_prefix} ✓ Uploaded to new path: {blob_path} in {dt:.2f}s")
        return True
    except Exception as e:
        log_prefix = f"[BLOB-{prefix_id}]" if prefix_id else "[BLOB]"
        logging.error(f"{log_prefix} New path upload error for {blob_path}: {e}")
        return False


def upload_to_blob_dual(file_path, container_name, blob_name, new_blob_path=None, prefix_id=None):
    """Upload file with migration-aware capability.

    Phase 5: Switches to new-paths-only mode when USE_OLD_CONTAINERS=False.

    Args:
        file_path: Local file path to upload
        container_name: Old container name (for backward compatibility)
        blob_name: Blob name in old container
        new_blob_path: New blob path for LANDING_CONTAINER
        prefix_id: Optional prefix identifier for logging

    Returns:
        dict: {"old": bool, "new": bool} indicating success of each upload
    """
    from azure_config import USE_NEW_PATHS, USE_OLD_CONTAINERS

    results = {"old": False, "new": False}
    log_prefix = f"[BLOB-{prefix_id}]" if prefix_id else "[BLOB]"

    # Phase 5: Check if we should use old containers
    if USE_OLD_CONTAINERS:
        # Upload to old container
        results["old"] = upload_to_blob(file_path, container_name, blob_name, prefix_id)
    else:
        # Phase 5: Skip old container upload
        results["old"] = True  # Mark as "successful" to maintain compatibility
        logging.info(f"{log_prefix} Skipping old container upload (USE_OLD_CONTAINERS=False)")

    # Upload to new path if enabled and path provided
    if USE_NEW_PATHS and new_blob_path:
        results["new"] = upload_to_blob_new_path(file_path, new_blob_path, prefix_id)

        if USE_OLD_CONTAINERS:
            # Dual upload mode
            if results["old"] and results["new"]:
                logging.info(f"{log_prefix} ✓ Dual upload successful (old + new)")
            elif results["old"]:
                logging.info(f"{log_prefix} ⚠ Partial success (old only)")
            elif results["new"]:
                logging.info(f"{log_prefix} ⚠ Partial success (new only)")
            else:
                logging.error(f"{log_prefix} ❌ Dual upload failed (both failed)")
        else:
            # New-paths-only mode
            if results["new"]:
                logging.info(f"{log_prefix} ✓ New path upload successful")
            else:
                logging.error(f"{log_prefix} ❌ New path upload failed")
    else:
        if not USE_NEW_PATHS:
            logging.info(f"{log_prefix} ✓ Old path only (USE_NEW_PATHS=False)")
        elif not new_blob_path:
            logging.warning(f"{log_prefix} ⚠ No new_blob_path provided")
        else:
            logging.info(f"{log_prefix} ✓ Upload handled")

    return results


# ============================================================================
# PHASE 3: Non-Dealer Path Logic
# ============================================================================

def get_prefix_config(prefix: str) -> dict:
    """Get configuration for a specific prefix.

    Returns:
        dict: Config with folder, dealer_prefix, etc. or None if not found
    """
    try:
        configs = get_source_configs()
        for config in configs:
            if config["file_prefix"] == prefix:
                return config
        # Fallback for static prefixes - use prefix name as folder
        folder_name = prefix.rstrip('_').upper()  # Remove trailing underscore and uppercase
        return {
            "folder": folder_name,
            "dealer_prefix": False,
            "file_prefix": prefix,
            "enabled": True
        }
    except Exception as e:
        logging.warning(f"[CFG] Failed to get config for prefix {prefix}: {e}")
        folder_name = prefix.rstrip('_').upper()  # Remove trailing underscore and uppercase
        return {
            "folder": folder_name,
            "dealer_prefix": False,
            "file_prefix": prefix,
            "enabled": True
        }


def get_dealer_compound_config(compound_name: str) -> dict:
    """Get configuration for a dealer compound by finding its parent config.

    Args:
        compound_name: Dealer compound name (e.g., "AO_RO_TIME")

    Returns:
        dict: Config with folder, dealer_prefix=True, etc. or None if not found
    """
    try:
        from sftp_downloader import extract_family_suffix

        # Extract family suffix from compound (e.g., "AO_RO_TIME" -> "RO_TIME")
        family_suffix = extract_family_suffix(compound_name)

        # Find the dealer config that matches this family
        configs = get_source_configs()
        for config in configs:
            if config["dealer_prefix"]:
                config_family = extract_family_suffix(config["file_prefix"])
                if config_family == family_suffix:
                    # Return a modified config for this specific compound
                    return {
                        "folder": config["folder"],
                        "dealer_prefix": True,
                        "file_prefix": compound_name,  # Use compound name as prefix
                        "enabled": True,
                        "parent_config": config  # Keep reference to original
                    }

        # Fallback: treat as non-dealer
        logging.warning(f"[CFG] No dealer config found for compound {compound_name}, treating as non-dealer")
        folder_name = compound_name.rstrip('_').upper()  # Remove trailing underscore and uppercase
        return {
            "folder": folder_name,
            "dealer_prefix": False,
            "file_prefix": compound_name,
            "enabled": True
        }

    except Exception as e:
        logging.warning(f"[CFG] Failed to get dealer compound config for {compound_name}: {e}")
        folder_name = compound_name.rstrip('_').upper()  # Remove trailing underscore and uppercase
        return {
            "folder": folder_name,
            "dealer_prefix": False,
            "file_prefix": compound_name,
            "enabled": True
        }


def process_single_prefix(
    prefix: str,
    file_inventory: Optional[Dict[str, List[str]]] = None,
    date_override: Optional[str] = None,
    process_all: bool = False,
    temp_base_dir: Optional[str] = None,
    prefix_id: Optional[str] = None,
    dealer_config: Optional[Dict] = None
) -> Dict:
    """Process a single prefix through the complete ETL pipeline

    Args:
        prefix: The file prefix to process (e.g., 'FIMASTSALES_')
        file_inventory: Optional pre-built inventory from build_file_inventory()
        date_override: Optional custom date (None = use get_date_format())
        process_all: If True, ignore date filtering
        temp_base_dir: Optional base temp directory (None = create new)
        prefix_id: Optional identifier for logging (e.g., 'THREAD-1')
        dealer_config: Optional dealer config when processing dealer compounds

    Returns:
        Dict with processing results and timing information
    """
    start_time = time.time()
    log_prefix = f"[PREFIX-{prefix_id}]" if prefix_id else f"[PREFIX-{prefix}]"
    
    logging.info(f"{log_prefix} Starting single-prefix processing for: {prefix}")
    logging.info(f"{log_prefix} Parameters: date_override={date_override}, process_all={process_all}")

    # Phase 5: Get configuration for this prefix
    if dealer_config:
        # Processing a dealer compound - use the parent dealer config
        is_dealer_prefix = True
        folder = dealer_config["folder"]
        logging.info(f"{log_prefix} Dealer compound detected: {prefix} (parent folder='{folder}') - using dealer path structure")
    else:
        # Regular prefix processing
        prefix_config = get_prefix_config(prefix)
        is_dealer_prefix = prefix_config["dealer_prefix"]
        folder = prefix_config["folder"]

        if is_dealer_prefix:
            logging.info(f"{log_prefix} Dealer-aware prefix detected: {prefix} (folder='{folder}') - using dealer path structure")
        else:
            logging.info(f"{log_prefix} Non-dealer prefix detected: {prefix} (folder='{folder}') - using non-dealer path structure")

    result = {
        "prefix": prefix,
        "status": "success",
        "downloaded": 0,
        "compressed_uploaded": 0,
        "decompressed": 0,
        "decompressed_uploaded": 0,
        "duration_s": 0,
        "error": None
    }
    
    try:
        # Create isolated temp directory for this prefix
        if temp_base_dir:
            prefix_temp_dir = os.path.join(temp_base_dir, f"prefix_{prefix.replace('_', '')}")
            os.makedirs(prefix_temp_dir, exist_ok=True)
            cleanup_temp = False
        else:
            prefix_temp_dir = tempfile.mkdtemp(prefix=f"etl_{prefix.replace('_', '')}_")
            cleanup_temp = True
        
        logging.info(f"{log_prefix} Using temp directory: {prefix_temp_dir}")
        
        compressed_dir = os.path.join(prefix_temp_dir, "compressed")
        decompressed_dir = os.path.join(prefix_temp_dir, "decompressed")
        
        os.makedirs(compressed_dir, exist_ok=True)
        os.makedirs(decompressed_dir, exist_ok=True)
        
        # Step 1: Download files for this prefix
        logging.info(f"{log_prefix} Step 1: Downloading files for prefix {prefix}...")
        t1 = time.time()
        
        if file_inventory and prefix in file_inventory:
            # Use pre-built inventory (optimized path)
            prefix_inventory = {prefix: file_inventory[prefix]}
            downloaded_files = download_files(compressed_dir, prefix_inventory)
            logging.info(f"{log_prefix} Used pre-built inventory: {len(file_inventory[prefix])} files available")
        else:
            # Build inventory for this prefix only (fallback path)
            logging.info(f"{log_prefix} Building inventory for single prefix (fallback mode)")
            remote_files = list_remote_files_once()
            prefix_inventory = build_file_inventory(
                remote_files, 
                prefixes=[prefix], 
                date_filter=date_override, 
                process_all=process_all
            )
            downloaded_files = download_files(compressed_dir, prefix_inventory)
        
        result["downloaded"] = len(downloaded_files)
        t1_duration = time.time() - t1
        logging.info(f"{log_prefix} Step 1 complete in {t1_duration:.2f}s → {len(downloaded_files)} files")
        
        if len(downloaded_files) == 0:
            logging.info(f"{log_prefix} No files to process for prefix {prefix}")
            result["duration_s"] = time.time() - start_time
            return result
        
        # Step 2: Upload compressed files to blob
        logging.info(f"{log_prefix} Step 2: Uploading compressed files...")
        t2 = time.time()
        compressed_uploaded = 0
        
        for file_path in downloaded_files:
            filename = os.path.basename(file_path)
            container_name = container_for_compressed(prefix)

            # Phase 5: Use new paths for both dealer and non-dealer
            new_blob_path = None
            if is_dealer_prefix:
                # Dealer compound: use dealer path structure
                paths = build_paths_dealer(folder, prefix)
                new_blob_path = f"{paths['compressed_dir']}/{filename}"
                logging.info(f"{log_prefix} Dealer compound upload path: {new_blob_path}")
            else:
                # Non-dealer: use non-dealer path structure
                paths = build_paths_non_dealer(folder, prefix)
                new_blob_path = f"{paths['compressed_dir']}/{filename}"
                logging.info(f"{log_prefix} Non-dealer upload path: {new_blob_path}")

            upload_results = upload_to_blob_dual(file_path, container_name, filename, new_blob_path=new_blob_path, prefix_id=prefix_id)
            # Phase 5: Count success based on new path upload (primary) or old path (fallback)
            if upload_results["new"] or upload_results["old"]:
                compressed_uploaded += 1
        
        result["compressed_uploaded"] = compressed_uploaded
        t2_duration = time.time() - t2
        logging.info(f"{log_prefix} Step 2 complete in {t2_duration:.2f}s → {compressed_uploaded}/{len(downloaded_files)} uploaded")
        
        # Step 3: Decompress files
        logging.info(f"{log_prefix} Step 3: Decompressing files...")
        t3 = time.time()
        decompressed_files = decompress_files(compressed_dir, decompressed_dir)
        result["decompressed"] = len(decompressed_files)
        t3_duration = time.time() - t3
        logging.info(f"{log_prefix} Step 3 complete in {t3_duration:.2f}s → {len(decompressed_files)} files")
        
        # Step 4: Upload decompressed files to blob
        logging.info(f"{log_prefix} Step 4: Uploading decompressed files...")
        t4 = time.time()
        decompressed_uploaded = 0
        
        for file_path in decompressed_files:
            filename = os.path.basename(file_path)
            container_name = container_for_decompressed(prefix)

            # Phase 5: Use new paths for both dealer and non-dealer
            new_blob_path = None
            if is_dealer_prefix:
                # Dealer compound: use dealer path structure
                paths = build_paths_dealer(folder, prefix)
                new_blob_path = f"{paths['decompressed_dir']}/{filename}"
                logging.info(f"{log_prefix} Dealer compound upload path: {new_blob_path}")
            else:
                # Non-dealer: use non-dealer path structure
                paths = build_paths_non_dealer(folder, prefix)
                new_blob_path = f"{paths['decompressed_dir']}/{filename}"
                logging.info(f"{log_prefix} Non-dealer upload path: {new_blob_path}")

            upload_results = upload_to_blob_dual(file_path, container_name, filename, new_blob_path=new_blob_path, prefix_id=prefix_id)
            # Phase 5: Count success based on new path upload (primary) or old path (fallback)
            if upload_results["new"] or upload_results["old"]:
                decompressed_uploaded += 1
        
        result["decompressed_uploaded"] = decompressed_uploaded
        t4_duration = time.time() - t4
        logging.info(f"{log_prefix} Step 4 complete in {t4_duration:.2f}s → {decompressed_uploaded}/{len(decompressed_files)} uploaded")
        
        # Cleanup temp directory if we created it
        if cleanup_temp:
            try:
                import shutil
                shutil.rmtree(prefix_temp_dir)
                logging.info(f"{log_prefix} Cleaned up temp directory")
            except Exception as e:
                logging.warning(f"{log_prefix} Failed to cleanup temp directory: {e}")
        
        result["duration_s"] = time.time() - start_time
        logging.info(f"{log_prefix} ✓ Single-prefix processing complete in {result['duration_s']:.2f}s")
        logging.info(f"{log_prefix} Results: {result['downloaded']} downloaded, {result['compressed_uploaded']} compressed uploaded, {result['decompressed']} decompressed, {result['decompressed_uploaded']} decompressed uploaded")
        
        return result
        
    except Exception as e:
        result["status"] = "error"
        result["error"] = str(e)
        result["duration_s"] = time.time() - start_time
        logging.error(f"{log_prefix} ❌ Single-prefix processing failed after {result['duration_s']:.2f}s: {e}")
        return result
