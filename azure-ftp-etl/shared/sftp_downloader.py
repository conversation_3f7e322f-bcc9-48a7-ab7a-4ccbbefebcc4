import paramiko
import os
import sys
import time
import logging
from datetime import datetime

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from azure_config import SFTP_CONFIG, get_date_format, get_file_prefixes, get_prefix_source

def list_remote_files_once():
    """List remote SFTP directory once and return all files (optimization for parallel processing)"""
    start_ts = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
    logging.info(f"[SFTP-INVENTORY] Starting single directory listing at {start_ts}")

    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        t0 = time.time()
        logging.info(f"[SFTP-INVENTORY] Connecting to server: {SFTP_CONFIG['hostname']}:{SFTP_CONFIG.get('port', 22)}")
        ssh.connect(**SFTP_CONFIG)
        sftp = ssh.open_sftp()
        logging.info(f"[SFTP-INVENTORY] Connected in {time.time() - t0:.2f}s")

        # Single directory listing - this is the expensive operation we want to do only once
        list_t0 = time.time()
        files = sftp.listdir('.')
        list_dt = time.time() - list_t0
        logging.info(f"[SFTP-INVENTORY] Listed directory in {list_dt:.2f}s (total entries: {len(files)})")

        return files

    except Exception as e:
        logging.error(f"[SFTP-INVENTORY] Failed to list remote directory: {e}")
        raise
    finally:
        try:
            sftp.close()
            ssh.close()
            logging.info("[SFTP-INVENTORY] Connection closed")
        except:
            pass

def build_file_inventory(remote_files, prefixes=None, date_filter=None, process_all=False):
    """Build inventory of files organized by prefix from remote file listing"""
    start_time = time.time()

    if prefixes is None:
        prefixes = get_file_prefixes()

    if date_filter is None:
        date_filter = get_date_format()

    logging.info(f"[SFTP-INVENTORY] Building inventory for {len(prefixes)} prefixes")
    logging.info(f"[SFTP-INVENTORY] Source: {len(remote_files)} remote files")
    logging.info(f"[SFTP-INVENTORY] Filter: date={date_filter}, process_all={process_all}")

    inventory = {}
    total_files = 0

    for prefix_idx, prefix in enumerate(prefixes, 1):
        prefix_start = time.time()

        if process_all:
            # Match any file starting with prefix and ending with .csv.zip
            matching_files = [f for f in remote_files if f.startswith(prefix) and f.lower().endswith('.csv.zip')]
            filter_desc = "all files"
        else:
            # Match files with specific date pattern: {PREFIX}{DATE}_{TIME}.csv.zip
            matching_files = [f for f in remote_files if f.startswith(f"{prefix}{date_filter}_") and f.lower().endswith('.csv.zip')]
            filter_desc = f"date {date_filter}"

        inventory[prefix] = matching_files
        total_files += len(matching_files)

        prefix_dt = time.time() - prefix_start
        logging.info(f"[SFTP-INVENTORY] [{prefix_idx}/{len(prefixes)}] {prefix}: {len(matching_files)} files ({filter_desc}) in {prefix_dt:.3f}s")

    duration = time.time() - start_time
    logging.info(f"[SFTP-INVENTORY] ✓ Inventory complete: {total_files} total files across {len(prefixes)} prefixes in {duration:.3f}s")

    # Summary by prefix
    if total_files > 0:
        logging.info(f"[SFTP-INVENTORY] File distribution:")
        for prefix, files in inventory.items():
            if len(files) > 0:
                logging.info(f"[SFTP-INVENTORY]   {prefix}: {len(files)} files")

    return inventory

def download_files(target_dir, file_inventory=None):
    """Download files from SFTP to target directory (with non-intrusive logging)

    Args:
        target_dir: Directory to download files to
        file_inventory: Optional dict[prefix] -> [files] from build_file_inventory()
                       If provided, skips directory listing and uses pre-built inventory
    """
    start_ts = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
    logging.info(f"[SFTP] Start download process at {start_ts} → target_dir={target_dir}")

    if file_inventory:
        logging.info(f"[SFTP] Using pre-built file inventory (optimization enabled)")
        return _download_from_inventory(target_dir, file_inventory)
    else:
        logging.info(f"[SFTP] Using traditional discovery mode (no inventory provided)")
        return _download_with_discovery(target_dir)

def _download_from_inventory(target_dir, file_inventory):
    """Download files using pre-built inventory (optimized path)"""
    start_time = time.time()
    downloaded_files = []
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    # Calculate totals for logging
    total_files = sum(len(files) for files in file_inventory.values())
    total_prefixes = len(file_inventory)

    logging.info(f"[SFTP-INVENTORY] Starting inventory-based downloads")
    logging.info(f"[SFTP-INVENTORY] Target: {total_files} files across {total_prefixes} prefixes")

    try:
        # Connection timing
        connect_t0 = time.time()
        logging.info(f"[SFTP-INVENTORY] Connecting to server: {SFTP_CONFIG['hostname']}:{SFTP_CONFIG.get('port', 22)}")
        ssh.connect(**SFTP_CONFIG)
        sftp = ssh.open_sftp()
        connect_dt = time.time() - connect_t0
        logging.info(f"[SFTP-INVENTORY] ✓ Connected in {connect_dt:.2f}s")

        # Download timing per prefix
        download_start = time.time()
        files_downloaded = 0
        total_bytes = 0

        for prefix_idx, (prefix, files) in enumerate(file_inventory.items(), 1):
            prefix_start = time.time()
            prefix_bytes = 0

            logging.info(f"[SFTP-INVENTORY] [{prefix_idx}/{total_prefixes}] Processing prefix: {prefix} ({len(files)} files)")

            for file_idx, filename in enumerate(files, 1):
                file_start = time.time()
                local_path = os.path.join(target_dir, filename)

                logging.info(f"[SFTP-INVENTORY] [{prefix_idx}/{total_prefixes}] [{file_idx}/{len(files)}] Downloading: {filename}")
                sftp.get(filename, local_path)

                # File stats
                size_bytes = os.path.getsize(local_path) if os.path.exists(local_path) else 0
                file_dt = time.time() - file_start
                prefix_bytes += size_bytes
                total_bytes += size_bytes
                files_downloaded += 1

                if size_bytes < 1024 * 1024:
                    logging.info(f"[SFTP-INVENTORY] ✓ Downloaded {filename} ({size_bytes} bytes) in {file_dt:.2f}s")
                else:
                    size_mb = size_bytes / (1024 * 1024)
                    logging.info(f"[SFTP-INVENTORY] ✓ Downloaded {filename} ({size_mb:.2f} MB) in {file_dt:.2f}s")

                downloaded_files.append(local_path)

            # Prefix summary
            prefix_dt = time.time() - prefix_start
            prefix_mb = prefix_bytes / (1024 * 1024) if prefix_bytes > 0 else 0
            logging.info(f"[SFTP-INVENTORY] ✓ Prefix {prefix} complete: {len(files)} files, {prefix_mb:.2f} MB in {prefix_dt:.2f}s")

        # Overall download summary
        download_dt = time.time() - download_start
        total_mb = total_bytes / (1024 * 1024) if total_bytes > 0 else 0
        avg_speed = total_mb / download_dt if download_dt > 0 else 0

        logging.info(f"[SFTP-INVENTORY] ✓ All downloads complete: {files_downloaded} files, {total_mb:.2f} MB in {download_dt:.2f}s")
        logging.info(f"[SFTP-INVENTORY] Average download speed: {avg_speed:.2f} MB/s")

        return downloaded_files

    except Exception as e:
        duration = time.time() - start_time
        logging.error(f"[SFTP-INVENTORY] ❌ Download failed after {duration:.2f}s: {e}")
        raise
    finally:
        try:
            sftp.close()
            logging.info("[SFTP-INVENTORY] SFTP session closed")
        except:
            pass
        try:
            ssh.close()
            logging.info("[SFTP-INVENTORY] SSH connection closed")
        except:
            pass

        total_duration = time.time() - start_time
        logging.info(f"[SFTP-INVENTORY] Total inventory-based download duration: {total_duration:.2f}s")

def _download_with_discovery(target_dir):
    """Download files using traditional discovery mode (original logic)"""
    downloaded_files = []
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        t0 = time.time()
        logging.info(f"[SFTP] Connecting to server: {SFTP_CONFIG['hostname']}:{SFTP_CONFIG.get('port', 22)}")
        ssh.connect(**SFTP_CONFIG)
        sftp = ssh.open_sftp()
        logging.info(f"[SFTP] Connected in {time.time() - t0:.2f}s")

        # Single directory listing to reduce repeated scans
        list_t0 = time.time()
        files = sftp.listdir('.')
        list_dt = time.time() - list_t0
        logging.info(f"[SFTP] Listed directory in {list_dt:.2f}s (total entries: {len(files)})")

        prefixes = get_file_prefixes()
        logging.info(f"[CFG] Prefix source: {get_prefix_source()} (count={len(prefixes)})")
        for prefix in prefixes:
            logging.info(f"[SFTP] Looking for files with prefix: {prefix}")
            # Pattern: {PREFIX}{DATE}_{TIME}.csv.zip
            matching_files = [f for f in files if f.startswith(f"{prefix}{DATE_FORMAT}_") and f.lower().endswith('.csv.zip')]
            logging.info(f"[SFTP] Found {len(matching_files)} matching files for {prefix} (process_all=False)")

            for filename in matching_files:
                local_path = os.path.join(target_dir, filename)
                logging.info(f"[SFTP] Downloading: {filename}")
                dl_t0 = time.time()
                sftp.get(filename, local_path)
                dt = time.time() - dl_t0
                size_bytes = os.path.getsize(local_path) if os.path.exists(local_path) else 0
                if size_bytes < 1024 * 1024:
                    logging.info(f"[SFTP] ✓ Downloaded {filename} ({size_bytes} bytes) in {dt:.2f}s")
                else:
                    size_mb = size_bytes / (1024 * 1024)
                    logging.info(f"[SFTP] ✓ Downloaded {filename} ({size_mb:.2f} MB) in {dt:.2f}s")
                downloaded_files.append(local_path)

        return downloaded_files

    except Exception as e:
        logging.error(f"[SFTP] Error: {e}")
        raise
    finally:
        try:
            sftp.close()
            logging.info("[chan 0] sftp session closed.")
        except:
            pass
        try:
            ssh.close()
            logging.info("[SFTP] SSH client closed")
        except:
            pass


# ============================================================================
# PHASE 1: Dealer Discovery Functions (Foundation Setup)
# ============================================================================

def extract_family_suffix(file_prefix: str) -> str:
    """Extract family suffix from a file prefix.

    For dealer-aware configs, the FilePrefix should contain just the family name (e.g., "RO_TIME").
    This function returns it as-is since the table already contains the correct family name.

    Args:
        file_prefix: The FilePrefix value from table (e.g., "RO_TIME", "Report_Card_Analysis_Tech")

    Returns:
        str: The family suffix (same as input for dealer-aware configs)

    Examples:
        "RO_TIME" -> "RO_TIME" (dealer-aware config)
        "Report_Card_Analysis_Tech" -> "Report_Card_Analysis_Tech" (dealer-aware config)
        "FIMASTSALES_" -> "FIMASTSALES_" (non-dealer config, returned as-is)
    """
    # For dealer-aware configs, FilePrefix contains the family name directly
    # No need to extract anything - just clean up trailing underscores
    clean_prefix = file_prefix.rstrip('_')

    logging.info(f"[DEALER] Using family suffix '{clean_prefix}' from FilePrefix '{file_prefix}'")
    return clean_prefix


def format_dealer_compound_case(dealer_compound: str) -> str:
    """Format dealer compound to proper case (e.g., "AO_RO_TIME" -> "AO_RO_Time").

    Args:
        dealer_compound: Raw dealer compound (e.g., "AO_RO_TIME")

    Returns:
        str: Properly formatted compound (e.g., "AO_RO_Time")
    """
    parts = dealer_compound.split('_')
    if len(parts) < 2:
        return dealer_compound

    # First part (dealer code) stays uppercase: "AO"
    # Remaining parts get title case: "RO_TIME" -> "Ro_Time"
    formatted_parts = [parts[0]]  # Keep dealer code as-is
    for part in parts[1:]:
        formatted_parts.append(part.title())  # Convert to title case

    return '_'.join(formatted_parts)


def find_dealer_compounds(remote_files: list[str], family_suffix: str) -> dict[str, list[str]]:
    """Find dealer compounds from remote files based on family suffix.

    This function scans the SFTP file listing and identifies dealer compounds that match
    the pattern: ^([A-Za-z0-9]+)_{family_suffix}.*

    Args:
        remote_files: List of filenames from SFTP directory listing
        family_suffix: The family suffix to match (e.g., "RO_TIME")

    Returns:
        dict: {dealer_compound: [matching_filenames...]}

    Example:
        remote_files = ["AO_RO_TIME_08-25-25.csv.zip", "BO_RO_TIME_08-25-25.csv.zip", "FIMASTSALES_08-25-25.csv.zip"]
        family_suffix = "RO_TIME"

        Returns: {
            "AO_RO_TIME": ["AO_RO_TIME_08-25-25.csv.zip"],
            "BO_RO_TIME": ["BO_RO_TIME_08-25-25.csv.zip"]
        }
    """
    import re

    # Build regex pattern to match dealer compounds
    # Pattern: ^([A-Za-z0-9]+)_{family_suffix}.*
    # This captures the dealer code (group 1) and matches the family suffix
    pattern = rf"^([A-Za-z0-9]+)_{re.escape(family_suffix)}.*"
    regex = re.compile(pattern)

    dealer_compounds = {}

    for filename in remote_files:
        # Only process .csv.zip files
        if not filename.lower().endswith('.csv.zip'):
            continue

        match = regex.match(filename)
        if match:
            dealer_code = match.group(1)  # Extract dealer code (e.g., "AO", "BO")
            dealer_compound = f"{dealer_code}_{family_suffix}"  # Use exact SFTP file casing (e.g., "AO_RO_TIME")

            if dealer_compound not in dealer_compounds:
                dealer_compounds[dealer_compound] = []
            dealer_compounds[dealer_compound].append(filename)

    # Log discovery results
    if dealer_compounds:
        logging.info(f"[DEALER] Found {len(dealer_compounds)} dealer compounds for family '{family_suffix}':")
        for compound, files in dealer_compounds.items():
            logging.info(f"[DEALER]   {compound}: {len(files)} files")
    else:
        logging.info(f"[DEALER] No dealer compounds found for family '{family_suffix}'")

    return dealer_compounds


def build_file_inventory_with_dealer_support(remote_files, configs, date_filter=None, process_all=False):
    """Build inventory with dealer compound support for Phase 4+.

    Args:
        remote_files: List of filenames from SFTP
        configs: List of config dicts with folder, dealer_prefix, file_prefix, enabled
        date_filter: Date filter string (e.g., "08-25-25")
        process_all: If True, ignore date filtering

    Returns:
        dict: {
            "non_dealer": {prefix: [files...]},
            "dealer_compounds": {dealer_compound: [files...]},
            "dealer_configs": [config_dicts...]
        }
    """
    start_time = time.time()

    if date_filter is None:
        date_filter = get_date_format()

    # Fetch remote files if not provided
    if remote_files is None:
        logging.info(f"[INVENTORY-PHASE4] Fetching remote files from SFTP...")
        remote_files = list_remote_files_once()

    logging.info(f"[INVENTORY-PHASE4] Building inventory with dealer support")
    logging.info(f"[INVENTORY-PHASE4] Source: {len(remote_files)} remote files")
    logging.info(f"[INVENTORY-PHASE4] Configs: {len(configs)} total")
    logging.info(f"[INVENTORY-PHASE4] Filter: date={date_filter}, process_all={process_all}")

    # SISTER FILE PHASE 2: Test pattern recognition functions
    if configs:
        first_config = configs[0]
        from azure_config import requires_sister_files, get_expected_file_count, extract_file_number, extract_base_pattern
        logging.info(f"[SISTER-FILE-PHASE2] Config: requires_sister_files={requires_sister_files(first_config)}, expected_count={get_expected_file_count(first_config)}")

        # Test pattern recognition with sample filenames
        test_files = [
            "FIMASTSALES_08-18-25_11.40.30.csv.zip",
            "FIMASTSALES_2_08-18-25_11.40.30.csv.zip",
            "AO_RO_TIME_08-20-25_22.40.35.csv.zip",
            "AO_RO_TIME_3_08-20-25_22.40.35.csv.zip"
        ]
        for test_file in test_files:
            file_num = extract_file_number(test_file)
            base_pattern = extract_base_pattern(test_file)
            logging.info(f"[SISTER-FILE-PHASE2] {test_file} -> file_num={file_num}, base={base_pattern}")

    result = {
        "non_dealer": {},
        "dealer_compounds": {},
        "dealer_configs": []
    }

    # Separate dealer and non-dealer configs
    dealer_configs = [c for c in configs if c["dealer_prefix"]]
    non_dealer_configs = [c for c in configs if not c["dealer_prefix"]]

    logging.info(f"[INVENTORY-PHASE4] Processing {len(non_dealer_configs)} non-dealer configs")
    logging.info(f"[INVENTORY-PHASE4] Processing {len(dealer_configs)} dealer configs")

    # Process non-dealer configs with sister file support
    for config in non_dealer_configs:
        prefix = config["file_prefix"]

        # DEBUG: Show ALL files that start with this prefix (before date filtering)
        all_prefix_files = [f for f in remote_files if f.startswith(prefix) and f.lower().endswith('.csv.zip')]
        logging.info(f"[SISTER-FILE-ALL-DEBUG] ALL files starting with '{prefix}': {len(all_prefix_files)} found")
        for i, filename in enumerate(all_prefix_files[:20]):  # Show first 20 files
            logging.info(f"[SISTER-FILE-ALL-DEBUG]   {i+1}. {filename}")
        if len(all_prefix_files) > 20:
            logging.info(f"[SISTER-FILE-ALL-DEBUG]   ... and {len(all_prefix_files) - 20} more files")

        if process_all:
            matching_files = [f for f in remote_files if f.startswith(prefix) and f.lower().endswith('.csv.zip')]
        else:
            # For sister files, we need to handle both patterns:
            # Primary file: PREFIX_DATE_TIME (e.g., FIMASTSALES_08-20-25_06.31.00)
            # Sister file: PREFIX_NUMBER_DATE_TIME (e.g., FIMASTSALES_2_08-20-25_06.35.45)
            matching_files = []
            for f in remote_files:
                if f.lower().endswith('.csv.zip') and f.startswith(prefix):
                    # Check if it matches primary pattern: PREFIX_DATE_
                    if f.startswith(f"{prefix}{date_filter}_"):
                        matching_files.append(f)
                    # Check if it matches sister pattern: PREFIX_NUMBER_DATE_
                    elif f.startswith(prefix):
                        # Extract potential file number and check if date matches
                        import re
                        # Pattern: PREFIX_NUMBER_DATE_TIME
                        pattern = f'^{re.escape(prefix)}(\\d+)_{re.escape(date_filter)}_'
                        if re.match(pattern, f):
                            matching_files.append(f)

        if matching_files:
            from azure_config import requires_sister_files, get_expected_file_count, extract_base_pattern, extract_file_number

            # DEBUG: Show all matching files for this prefix
            logging.info(f"[SISTER-FILE-DEBUG] Found {len(matching_files)} files for prefix '{prefix}':")
            for i, filename in enumerate(matching_files[:10]):  # Show first 10 files
                file_num = extract_file_number(filename)
                base_pattern = extract_base_pattern(filename)
                logging.info(f"[SISTER-FILE-DEBUG]   {i+1}. {filename} -> file_num={file_num}, base={base_pattern}")
            if len(matching_files) > 10:
                logging.info(f"[SISTER-FILE-DEBUG]   ... and {len(matching_files) - 10} more files")

            if requires_sister_files(config):
                # Sister file processing: group files by base pattern
                expected_count = get_expected_file_count(config)
                file_groups = {}

                for filename in matching_files:
                    base_pattern = extract_base_pattern(filename)
                    file_number = extract_file_number(filename)

                    if base_pattern not in file_groups:
                        file_groups[base_pattern] = {}
                    file_groups[base_pattern][file_number] = filename

                # Validate complete groups and create file group objects
                complete_groups = []
                for base_pattern, files_by_number in file_groups.items():
                    if len(files_by_number) == expected_count:
                        # Complete group - create file group object
                        sorted_files = [files_by_number[i] for i in sorted(files_by_number.keys())]
                        file_group = {
                            "base_pattern": base_pattern,
                            "files": sorted_files,
                            "expected_count": expected_count,
                            "actual_count": len(sorted_files),
                            "complete": True
                        }
                        complete_groups.append(file_group)
                    else:
                        # Incomplete group - log warning
                        missing_numbers = set(range(1, expected_count + 1)) - set(files_by_number.keys())
                        logging.warning(f"[SISTER-FILES] ❌ Incomplete group {base_pattern}: Expected {expected_count} files, found {len(files_by_number)}")
                        logging.warning(f"[SISTER-FILES] Missing file numbers: {sorted(missing_numbers)}")

                result["non_dealer"][prefix] = complete_groups
                if complete_groups:
                    total_files = len(complete_groups) * expected_count
                    logging.info(f"[SISTER-FILES] ✓ Non-dealer {prefix}: {len(complete_groups)} complete file groups ({total_files} total files)")
                else:
                    logging.warning(f"[SISTER-FILES] ❌ Non-dealer {prefix}: No complete file groups found")
            else:
                # Single file processing (existing logic)
                result["non_dealer"][prefix] = matching_files
                logging.info(f"[INVENTORY-PHASE4] Non-dealer {prefix}: {len(matching_files)} files")
        else:
            result["non_dealer"][prefix] = []

    # Process dealer configs with compound discovery
    for config in dealer_configs:
        prefix = config["file_prefix"]
        family_suffix = extract_family_suffix(prefix)

        logging.info(f"[INVENTORY-PHASE4] Dealer config {prefix} -> family '{family_suffix}'")

        # Discover dealer compounds for this family
        dealer_compounds = find_dealer_compounds(remote_files, family_suffix)

        # Filter by date if needed
        if not process_all:
            filtered_compounds = {}
            for compound, files in dealer_compounds.items():
                filtered_files = [f for f in files if date_filter in f]
                if filtered_files:
                    filtered_compounds[compound] = filtered_files
            dealer_compounds = filtered_compounds

        # Add to result
        result["dealer_compounds"].update(dealer_compounds)
        result["dealer_configs"].append(config)

        total_dealer_files = sum(len(files) for files in dealer_compounds.values())
        logging.info(f"[INVENTORY-PHASE4] Dealer family '{family_suffix}': {len(dealer_compounds)} compounds, {total_dealer_files} files")

    duration = time.time() - start_time
    total_non_dealer_files = sum(len(files) for files in result["non_dealer"].values())
    total_dealer_files = sum(len(files) for files in result["dealer_compounds"].values())

    logging.info(f"[INVENTORY-PHASE4] ✓ Inventory complete in {duration:.3f}s:")
    logging.info(f"[INVENTORY-PHASE4]   - Non-dealer: {total_non_dealer_files} files across {len(result['non_dealer'])} prefixes")
    logging.info(f"[INVENTORY-PHASE4]   - Dealer: {total_dealer_files} files across {len(result['dealer_compounds'])} compounds")

    return result
