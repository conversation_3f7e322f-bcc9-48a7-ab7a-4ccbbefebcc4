import os
import re
import logging
from azure.storage.blob import BlobServiceClient
from azure.identity import DefaultAzureCredential
from azure.data.tables import TableServiceClient
from azure.core.exceptions import HttpResponseError

# Hardcoded SFTP Configuration (temporary - replace with <PERSON> Vault later)
SFTP_CONFIG = {
    'hostname': 'reyreydaf.swickard.com',
    'username': 'reyrey-daf',
    'password': 'Hyj7*5eX$13!',
    'port': 2222
}

FILE_PREFIXES = ['FIMASTSALES_', 'FIMASTSALES2TEST_', 'AO_Detail_Video_Analysis']
ZIP_PASSWORD = '21NQN7LXW6IVDIO9GG60M0S986LW84FLL6M96JIA'  # Hardcoded temporarily
DATE_FORMAT = '09-17-25'

# Blob Storage - using sagedw for ETL data storage with managed identity
# Note: Function App uses safuncetl77231 for its internal operations
STORAGE_ACCOUNT_URL = "https://sagedw.blob.core.windows.net"
credential = DefaultAzureCredential()

# Quiet Azure SDK HTTP request/response logging to avoid noisy 200/201 lines
def setup_quiet_azure_logging():
    try:
        logging.getLogger("azure").setLevel(logging.WARNING)
        logging.getLogger("azure.core.pipeline.policies.http_logging_policy").setLevel(logging.WARNING)
        logging.getLogger("azure.identity").setLevel(logging.WARNING)
    except Exception:
        pass

blob_service_client = BlobServiceClient(account_url=STORAGE_ACCOUNT_URL, credential=credential)
COMPRESSED_CONTAINER = "fimastsales-compressed"

# Table Storage for dynamic file prefixes
TABLES_URL = "https://sagedw.table.core.windows.net"
TABLE_NAME = "sftpConfig"

_prefix_cache = None
_prefix_source = "static"  # 'table' or 'static'

# Date format cache and source tracking
_date_format_cache = None
_date_format_source = "static"  # 'table' or 'static'


def get_file_prefixes():
    """Return a list of enabled file prefixes from Table Storage; fallback to static list on error.
    Logs source used and any errors non-intrusively.
    """
    global _prefix_cache, _prefix_source
    # Use per-invocation cache if already resolved
    if _prefix_cache is not None:
        return _prefix_cache

    try:
        credential = DefaultAzureCredential()
        svc = TableServiceClient(endpoint=TABLES_URL, credential=credential)
        table = svc.get_table_client(TABLE_NAME)
        # Only enabled rows
        entities = table.query_entities("isEnabled eq true")
        prefixes = []
        for e in entities:
            fp = (e.get("FilePrefix") or "").strip()
            if fp:
                prefixes.append(fp)
        # Deduplicate while preserving order
        seen = set()
        prefixes = [p for p in prefixes if not (p in seen or seen.add(p))]
        if prefixes:
            _prefix_cache = prefixes
            _prefix_source = "table"
            print(f"[CFG] Loaded {len(prefixes)} prefixes from table '{TABLE_NAME}' at '{TABLES_URL}'")
            return _prefix_cache
        else:
            print(f"[CFG] Table query returned 0 enabled prefixes; NO FALLBACK - returning empty list")
            _prefix_cache = []
            _prefix_source = "table_empty"
            return _prefix_cache
    except Exception as ex:
        # No fallback on table access failure - return empty list
        print(f"[CFG] Failed to load prefixes from table; NO FALLBACK - returning empty list. Reason: {ex}")
        _prefix_cache = []
        _prefix_source = "table_error"
        return _prefix_cache


def get_prefix_source():
    """Return 'table' or 'static' indicating current source used for prefixes."""
    return _prefix_source


def get_date_format():
    """Return date format from Table Storage; fallback to hardcoded value on error.

    Loads DateFormat from sftpConfig table. If the key doesn't exist or the value
    doesn't match MM-dd-yy format, falls back to hardcoded DATE_FORMAT.
    Logs source used and any errors non-intrusively.

    Returns:
        str: Date format string (e.g., '09-17-25')
    """
    global _date_format_cache, _date_format_source

    # Return cached value if available
    if _date_format_cache is not None:
        return _date_format_cache

    try:
        print(f"[CFG] Attempting to load DateFormat from table '{TABLE_NAME}' at '{TABLES_URL}'")
        credential = DefaultAzureCredential()
        svc = TableServiceClient(endpoint=TABLES_URL, credential=credential)
        table = svc.get_table_client(TABLE_NAME)

        # Query for entities that have DateFormat property
        entities = table.query_entities("isEnabled eq true")
        date_format_value = None

        for entity in entities:
            if 'DateFormat' in entity:
                date_format_value = entity['DateFormat'].strip()
                print(f"[CFG] Found DateFormat in entity: '{date_format_value}'")
                break

        if date_format_value:
            # Validate format matches MM-dd-yy pattern
            import re
            # Pattern: 2 digits, dash, 2 digits, dash, 2 digits
            pattern = r'^\d{2}-\d{2}-\d{2}$'
            if re.match(pattern, date_format_value):
                _date_format_cache = date_format_value
                _date_format_source = "table"
                print(f"[CFG] ✓ Loaded DateFormat from table: '{date_format_value}'")
                return _date_format_cache
            else:
                print(f"[CFG] ⚠️ DateFormat '{date_format_value}' doesn't match MM-dd-yy pattern, using fallback")
        else:
            print(f"[CFG] ⚠️ No DateFormat key found in enabled entities, using fallback")

    except Exception as ex:
        print(f"[CFG] ⚠️ Failed to load DateFormat from table, using fallback. Reason: {ex}")

    # Fallback to hardcoded value
    _date_format_cache = DATE_FORMAT
    _date_format_source = "static"
    print(f"[CFG] Using fallback DateFormat: '{_date_format_cache}' (source: {_date_format_source})")
    return _date_format_cache


def get_date_format_source():
    """Return 'table' or 'static' indicating current source used for date format."""
    return _date_format_source


def assert_tables_permission():
    """Perform a lightweight permission probe against Table service.
    Returns True if we can list the table or query it; False otherwise. Logs details non-intrusively.
    """
    try:
        credential = DefaultAzureCredential()
        svc = TableServiceClient(endpoint=TABLES_URL, credential=credential)
        # Try a harmless operation: get properties/list tables
        _ = list(svc.list_tables())  # forces a call; ok if large, it's a single page iterator fetch
        print("[CFG] Table permission probe succeeded (Table Data access confirmed)")
        return True
    except Exception as ex:
        print(f"[CFG] Table permission probe failed (no Table Data access?): {ex}")
        return False

DECOMPRESSED_CONTAINER = "fimastsales-decompressed"

# Helpers to derive per-file container names
# Azure container naming rules: lowercase, digits, hyphens; start/end with alphanumeric
# We’ll normalize prefix to a safe container segment, then append '-compressed' or '-decompressed'
SAFE_CHARS_PATTERN = re.compile(r"[^a-z0-9-]")

def _normalize_container_segment(text: str) -> str:
    seg = text.lower().replace('_', '-')
    seg = SAFE_CHARS_PATTERN.sub('-', seg)
    seg = seg.strip('-')
    return seg or 'data'

def container_for_compressed(prefix: str) -> str:
    return f"{_normalize_container_segment(prefix)}-compressed"

def container_for_decompressed(prefix: str) -> str:
    return f"{_normalize_container_segment(prefix)}-decompressed"


# ============================================================================
# PHASE 1: New Infrastructure (Foundation Setup)
# ============================================================================

# Single landing container for new path-based approach
LANDING_CONTAINER = "sftp-landing"

# ============================================================================
# PHASE 2: Dual Upload Configuration
# ============================================================================

# ============================================================================
# PHASE 5: Complete Migration Configuration
# ============================================================================

# Phase 5: Migration complete - use new paths exclusively
USE_NEW_PATHS = True
USE_OLD_CONTAINERS = False  # Phase 5: Disable old container uploads

# Cache for source configurations (similar to prefix cache)
_source_config_cache = None

def get_source_configs():
    """Return a list of source configurations from Table Storage; fallback to static configs on error.

    Returns:
        List[dict]: Each dict contains:
            - folder: str (parent folder name)
            - dealer_prefix: bool (whether this uses dealer-aware discovery)
            - file_prefix: str (the file prefix to match)
            - enabled: bool (whether this config is enabled)
            - files_columns: int (number of sister files for column joining, default 1)
            - files_rows: int (number of sister files for row joining, default 1)

    This is the new function for Phase 1+ that will eventually replace get_file_prefixes().
    For now, it coexists with the existing function for backward compatibility.
    """
    global _source_config_cache

    # SISTER FILE PHASE 1: Force fresh load to test new columns
    print(f"[SISTER-FILE-PHASE1] get_source_configs() called, forcing fresh load for testing")
    _source_config_cache = None  # Force fresh load to test sister file columns

    try:
        print(f"[CFG] Attempting to load configs from table '{TABLE_NAME}' at '{TABLES_URL}'")
        credential = DefaultAzureCredential()
        print(f"[CFG] DefaultAzureCredential created successfully")

        svc = TableServiceClient(endpoint=TABLES_URL, credential=credential)
        print(f"[CFG] TableServiceClient created successfully")

        table = svc.get_table_client(TABLE_NAME)
        print(f"[CFG] Table client created for '{TABLE_NAME}'")

        # Only enabled rows
        print(f"[CFG] Querying entities with filter: 'isEnabled eq true'")
        entities = table.query_entities("isEnabled eq true")
        configs = []
        entity_count = 0

        for e in entities:
            entity_count += 1
            folder = (e.get("Folder") or "").strip()
            dealer_prefix = bool(e.get("DealerPrefix", False))
            file_prefix = (e.get("FilePrefix") or "").strip()
            enabled = bool(e.get("isEnabled", False))

            # Sister file support: read FilesColumns and FilesRows (default to 1)
            files_columns = int(e.get("FilesColumns", 1))
            files_rows = int(e.get("FilesRows", 1))

            print(f"[CFG] Entity {entity_count}: FilePrefix='{file_prefix}', Folder='{folder}', DealerPrefix={dealer_prefix}, Enabled={enabled}, FilesColumns={files_columns}, FilesRows={files_rows}")

            if file_prefix and folder and enabled:
                config = {
                    "folder": folder,
                    "dealer_prefix": dealer_prefix,
                    "file_prefix": file_prefix,
                    "enabled": enabled,
                    "files_columns": files_columns,
                    "files_rows": files_rows
                }
                configs.append(config)
                print(f"[CFG] ✓ Added config: {config}")
            else:
                print(f"[CFG] ❌ Skipped entity (missing required fields)")

        print(f"[CFG] Processed {entity_count} entities, created {len(configs)} valid configs")

        if configs:
            _source_config_cache = configs
            print(f"[CFG] ✅ Successfully loaded {len(configs)} source configs from table")

            # SISTER FILE TEST: Log first config to verify sister file columns are read
            if configs:
                first_config = configs[0]
                print(f"[SISTER-FILE-TEST] First config sister file settings:")
                print(f"[SISTER-FILE-TEST]   files_columns: {first_config.get('files_columns', 'MISSING')}")
                print(f"[SISTER-FILE-TEST]   files_rows: {first_config.get('files_rows', 'MISSING')}")
                print(f"[SISTER-FILE-TEST]   requires_sister_files: {requires_sister_files(first_config)}")
                print(f"[SISTER-FILE-TEST]   expected_file_count: {get_expected_file_count(first_config)}")

            return _source_config_cache
        else:
            print(f"[CFG] ⚠️ Table query returned 0 valid configs; NO FALLBACK - returning empty list")
            # No fallback: return empty list when table has no enabled configs
            # This ensures only explicitly enabled prefixes in the table are processed
            _source_config_cache = []
            return _source_config_cache

    except Exception as ex:
        # No fallback on table access failure - return empty list
        # This ensures the system fails safely when table is inaccessible
        print(f"[CFG] Failed to load source configs from table; NO FALLBACK - returning empty list. Reason: {ex}")
        print(f"[CFG] This ensures only explicitly enabled prefixes in the table are processed")
        _source_config_cache = []
        return _source_config_cache


def has_sister_files(config: dict) -> bool:
    """Check if a configuration requires sister file processing.

    Args:
        config: Configuration dictionary from get_source_configs()

    Returns:
        bool: True if FilesColumns > 1 OR FilesRows > 1
    """
    files_columns = config.get("files_columns", 1)
    files_rows = config.get("files_rows", 1)
    return files_columns > 1 or files_rows > 1


def get_expected_sister_file_count(config: dict) -> int:
    """Get the total number of expected files for a configuration.

    Args:
        config: Configuration dictionary from get_source_configs()

    Returns:
        int: Maximum of FilesColumns or FilesRows (the total expected file count)
    """
    files_columns = config.get("files_columns", 1)
    files_rows = config.get("files_rows", 1)
    return max(files_columns, files_rows)


def build_paths_non_dealer(folder: str, prefix: str) -> dict:
    """Build blob paths for non-dealer rows (DealerPrefix = FALSE).

    Args:
        folder: The Folder value from table (e.g., "FIMAST-SALES")
        prefix: The FilePrefix value (e.g., "FIMASTSALES_")

    Returns:
        dict: {
            "compressed_dir": "FIMAST-SALES/fimastsales-compressed",
            "decompressed_dir": "FIMAST-SALES/fimastsales-decompressed"
        }
    """
    prefix_lower = prefix.lower().rstrip('_')  # Remove trailing underscore and lowercase
    return {
        "compressed_dir": f"{folder}/{prefix_lower}-compressed",
        "decompressed_dir": f"{folder}/{prefix_lower}-decompressed"
    }


def build_paths_dealer(folder: str, dealer_compound: str) -> dict:
    """Build blob paths for dealer-aware rows (DealerPrefix = TRUE).

    Args:
        folder: The Folder value from table (e.g., "RO_Time") - used as-is
        dealer_compound: The discovered dealer compound (e.g., "AO_RO_TIME") - used as-is from SFTP

    Returns:
        dict: {
            "compressed_dir": "RO_Time/AO_RO_TIME/ao_ro_time-compressed",
            "decompressed_dir": "RO_Time/AO_RO_TIME/ao_ro_time-decompressed"
        }

    Casing Strategy:
        - folder: Exact case from table Folder column
        - dealer_compound: Exact case from SFTP files
        - subfolders: Lowercase for consistency
    """
    dealer_compound_lower = dealer_compound.lower()
    return {
        "compressed_dir": f"{folder}/{dealer_compound}/{dealer_compound_lower}-compressed",
        "decompressed_dir": f"{folder}/{dealer_compound}/{dealer_compound_lower}-decompressed"
    }


def requires_sister_files(config: dict) -> bool:
    """Check if a configuration requires sister file processing.

    Args:
        config: Configuration dict with files_columns and files_rows

    Returns:
        bool: True if sister files are required (total files > 1)
    """
    files_columns = config.get("files_columns", 1)
    files_rows = config.get("files_rows", 1)
    total_files = files_columns * files_rows
    return total_files > 1


def get_expected_file_count(config: dict) -> int:
    """Get the expected number of files for a configuration.

    Args:
        config: Configuration dict with files_columns and files_rows

    Returns:
        int: Total expected files (files_columns × files_rows)
    """
    files_columns = config.get("files_columns", 1)
    files_rows = config.get("files_rows", 1)
    return files_columns * files_rows


def extract_file_number(filename: str) -> int:
    """Extract file number from filename.

    Args:
        filename: Filename like "FIMASTSALES_2_08-18-25_11.40.30.csv.zip"

    Returns:
        int: File number (1 for primary file, 2+ for sister files)
    """
    import re

    # Pattern to match optional file number: _2_, _3_, etc.
    # Examples:
    # "FIMASTSALES_08-18-25_11.40.30.csv.zip" -> 1 (no number = primary)
    # "FIMASTSALES_2_08-18-25_11.40.30.csv.zip" -> 2
    # "AO_RO_TIME_3_08-20-25_22.40.35.csv.zip" -> 3

    # Updated pattern to handle dots in time: _NUMBER_DATE_TIME
    # Matches: _2_08-25-25_06.31.00 or _3_08-20-25_22.40.35
    pattern = r'_(\d+)_\d{2}-\d{2}-\d{2}_\d{2}\.\d{2}\.\d{2}'
    match = re.search(pattern, filename)

    if match:
        return int(match.group(1))
    else:
        return 1  # No file number = primary file


def extract_base_pattern(filename: str) -> str:
    """Extract base pattern by removing file number and time (keep date for grouping).

    Args:
        filename: Filename like "FIMASTSALES_2_08-18-25_11.40.30.csv.zip"

    Returns:
        str: Base pattern like "FIMASTSALES_08-18-25" (date only, no time)
    """
    import re

    # Remove file extension first
    base = filename.replace('.csv.zip', '').replace('.CSV.zip', '').replace('.CSV', '').replace('.csv', '')

    # Extract prefix and date, ignoring file number and time
    # Pattern matches: PREFIX_[NUMBER_]DATE_TIME
    # Examples:
    # "FIMASTSALES_08-25-25_06.31.00" -> "FIMASTSALES_08-25-25"
    # "FIMASTSALES_2_08-25-25_06.31.00" -> "FIMASTSALES_08-25-25"

    # First try to match with file number: PREFIX_NUMBER_DATE_TIME
    pattern_with_number = r'^(.+?)_\d+_(\d{2}-\d{2}-\d{2})_\d{2}\.\d{2}\.\d{2}$'
    match = re.match(pattern_with_number, base)

    if match:
        prefix = match.group(1)
        date = match.group(2)
        return f"{prefix}_{date}"

    # If no file number, try: PREFIX_DATE_TIME
    pattern_no_number = r'^(.+?)_(\d{2}-\d{2}-\d{2})_\d{2}\.\d{2}\.\d{2}$'
    match = re.match(pattern_no_number, base)

    if match:
        prefix = match.group(1)
        date = match.group(2)
        return f"{prefix}_{date}"

    # Fallback: return original base if no pattern matches
    return base

