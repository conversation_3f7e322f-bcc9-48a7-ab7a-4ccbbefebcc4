import tempfile
import os
import sys
import time
import logging
from typing import Optional, List
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from azure_config import (
    blob_service_client,
    get_file_prefixes,
    get_source_configs,  # Phase 3: New config-based approach
)
from sftp_downloader import list_remote_files_once, build_file_inventory, build_file_inventory_with_dealer_support, extract_family_suffix
from single_prefix_processor import process_single_prefix

# Simple per-process cache to avoid repeated container creation attempts in one run
_container_checked = set()

# ============================================================================
# PHASE 3: Non-Dealer Configuration Helpers
# ============================================================================

def get_non_dealer_prefixes():
    """Get list of non-dealer prefixes for Phase 3 migration.

    Returns:
        List[str]: File prefixes where DealerPrefix = False
    """
    try:
        configs = get_source_configs()
        non_dealer_prefixes = [
            config["file_prefix"]
            for config in configs
            if not config["dealer_prefix"] and config["enabled"]
        ]
        logging.info(f"[CFG-PHASE3] Found {len(non_dealer_prefixes)} non-dealer prefixes: {non_dealer_prefixes}")
        return non_dealer_prefixes
    except Exception as e:
        logging.warning(f"[CFG-PHASE3] Failed to get non-dealer configs, falling back to all prefixes: {e}")
        return get_file_prefixes()  # Fallback to old behavior


def get_all_enabled_configs():
    """Get all enabled configurations for Phase 4+ (includes both dealer and non-dealer).

    Returns:
        List[dict]: All enabled source configurations
    """
    try:
        configs = get_source_configs()
        enabled_configs = [config for config in configs if config["enabled"]]
        dealer_configs = [c for c in enabled_configs if c["dealer_prefix"]]
        non_dealer_configs = [c for c in enabled_configs if not c["dealer_prefix"]]

        logging.info(f"[CFG-PHASE4] Found {len(enabled_configs)} total configs:")

        # Log each config for debugging
        for i, config in enumerate(enabled_configs, 1):
            logging.info(f'[CFG-PHASE4] Config {i}: FilePrefix="{config["file_prefix"]}", Folder="{config["folder"]}", DealerPrefix={config["dealer_prefix"]}, Enabled={config["enabled"]}')

        logging.info(f"[CFG-PHASE4]   - {len(non_dealer_configs)} non-dealer configs")
        logging.info(f"[CFG-PHASE4]   - {len(dealer_configs)} dealer configs")

        return enabled_configs
    except Exception as e:
        logging.warning(f"[CFG-PHASE4] Failed to get configs; NO FALLBACK - returning empty list: {e}")
        # No fallback: return empty list to ensure only table-enabled prefixes are processed
        return []

def upload_to_blob(file_path, container_name, blob_name):
    """Upload file to blob storage (with non-intrusive logging)"""
    try:
        # Create container if it doesn't exist (only once per run)
        if container_name not in _container_checked:
            try:
                container_client = blob_service_client.get_container_client(container_name)
                container_client.create_container()
                logging.info(f"[BLOB] Created container {container_name}")
            except Exception as container_error:
                if "ContainerAlreadyExists" in str(container_error):
                    logging.info(f"[BLOB] Container {container_name} already exists")
                else:
                    logging.info(f"[BLOB] Container creation info: {container_error}")
            _container_checked.add(container_name)

        size_bytes = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        if size_bytes < 1024 * 1024:
            logging.info(f"[BLOB] Uploading {blob_name} to {container_name} ({size_bytes} bytes)")
        else:
            size_mb = size_bytes / (1024 * 1024)
            logging.info(f"[BLOB] Uploading {blob_name} to {container_name} ({size_mb:.2f} MB)")
        t0 = time.time()
        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)
        with open(file_path, "rb") as data:
            blob_client.upload_blob(data, overwrite=True)
        dt = time.time() - t0
        logging.info(f"[BLOB] ✓ Uploaded: {blob_name} in {dt:.2f}s")
        return True
    except Exception as e:
        logging.error(f"[BLOB] Upload error for {blob_name}: {e}")
        return False

# Original run_pipeline() function removed - now using parallel processing only
# Fallback functionality preserved in run_pipeline_single_prefix_sequential()

def run_pipeline_single_prefix_sequential(
    prefixes_filter: Optional[List[str]] = None,
    date_override: Optional[str] = None,
    process_all: bool = False
) -> dict:
    """Execute ETL pipeline using single-prefix processing (sequential mode for testing)

    This function tests the single-prefix processing approach by calling process_single_prefix()
    for each prefix sequentially. This validates the single-prefix logic before implementing
    parallel processing.

    Args:
        prefixes_filter: List of prefixes to process (None = all enabled)
        date_override: Custom date (None = use DATE_FORMAT)
        process_all: Ignore date filter for backfills

    Returns:
        Dict with aggregated results from all prefixes
    """
    start_time = time.time()
    logging.info("[ETL-SINGLE] Starting single-prefix sequential processing...")

    # Reset per-run container check cache
    try:
        _container_checked.clear()
    except Exception:
        pass

    # Phase 4: Process both dealer and non-dealer configs
    if prefixes_filter:
        # When filtering, use old behavior for compatibility
        prefixes_to_process = prefixes_filter
        logging.info(f"[ETL-SINGLE] Processing filtered prefixes: {prefixes_to_process}")
        use_phase4_logic = False
    else:
        # Phase 4: Use new config-based processing
        all_configs = get_all_enabled_configs()
        logging.info(f"[ETL-SINGLE] Processing all enabled configs with Phase 4 logic")
        use_phase4_logic = True

    # Build shared inventory once (optimization)
    logging.info("[ETL-SINGLE] Building shared file inventory...")
    inventory_start = time.time()
    remote_files = list_remote_files_once()

    if use_phase4_logic:
        # Phase 4: Use new dealer-aware inventory building
        inventory_result = build_file_inventory_with_dealer_support(
            remote_files,
            configs=all_configs,
            date_filter=date_override,
            process_all=process_all
        )
        inventory_duration = time.time() - inventory_start
        logging.info(f"[ETL-SINGLE] Phase 4 inventory built in {inventory_duration:.2f}s")
    else:
        # Legacy: Use old inventory building for filtered prefixes
        file_inventory = build_file_inventory(
            remote_files,
            prefixes=prefixes_to_process,
            date_filter=date_override,
            process_all=process_all
        )
        inventory_duration = time.time() - inventory_start
        logging.info(f"[ETL-SINGLE] Legacy inventory built in {inventory_duration:.2f}s")

    # Process prefixes/compounds sequentially
    prefix_results = []
    total_downloaded = 0
    total_compressed_uploaded = 0
    total_decompressed = 0
    total_decompressed_uploaded = 0

    with tempfile.TemporaryDirectory() as temp_base_dir:
        logging.info(f"[ETL-SINGLE] Using shared temp directory: {temp_base_dir}")

        if use_phase4_logic:
            # Phase 4: Process non-dealer prefixes and dealer compounds
            processing_items = []

            # Add non-dealer prefixes (handle both regular files and sister file groups)
            for prefix, files_or_groups in inventory_result["non_dealer"].items():
                # Check if this is sister file groups or regular file list
                if files_or_groups and isinstance(files_or_groups[0], dict):
                    # Sister file groups: flatten all files from all groups
                    all_files = []
                    for file_group in files_or_groups:
                        all_files.extend(file_group["files"])
                    processing_items.append(("non_dealer", prefix, {prefix: all_files}, None))
                    logging.info(f"[ETL-SINGLE] Sister files for {prefix}: {len(files_or_groups)} groups, {len(all_files)} total files")
                else:
                    # Regular file list
                    processing_items.append(("non_dealer", prefix, {prefix: files_or_groups}, None))

            # Add dealer compounds with their parent config
            dealer_configs_by_compound = {}
            for config in inventory_result["dealer_configs"]:
                family_suffix = extract_family_suffix(config["file_prefix"])
                for compound in inventory_result["dealer_compounds"].keys():
                    if compound.endswith(f"_{family_suffix}"):
                        dealer_configs_by_compound[compound] = config

            for compound, files in inventory_result["dealer_compounds"].items():
                parent_config = dealer_configs_by_compound.get(compound)
                processing_items.append(("dealer_compound", compound, {compound: files}, parent_config))

            logging.info(f"[ETL-SINGLE] Phase 4: Processing {len(processing_items)} items")

            for i, (item_type, item_name, item_inventory, dealer_config) in enumerate(processing_items, 1):
                prefix_id = f"SEQ-{i}"
                logging.info(f"[ETL-SINGLE] [{i}/{len(processing_items)}] Processing {item_type}: {item_name}")

                # For Phase 4, we still use process_single_prefix but with discovered compounds
                prefix_result = process_single_prefix(
                    prefix=item_name,
                    file_inventory=item_inventory,
                    date_override=date_override,
                    process_all=process_all,
                    temp_base_dir=temp_base_dir,
                    prefix_id=prefix_id,
                    dealer_config=dealer_config
                )

                prefix_results.append(prefix_result)

                # Aggregate results
                if prefix_result["status"] == "success":
                    total_downloaded += prefix_result["downloaded"]
                    total_compressed_uploaded += prefix_result["compressed_uploaded"]
                    total_decompressed += prefix_result["decompressed"]
                    total_decompressed_uploaded += prefix_result["decompressed_uploaded"]

                    logging.info(f"[ETL-SINGLE] [{i}/{len(processing_items)}] ✓ {item_type} {item_name} completed: {prefix_result['downloaded']} files in {prefix_result['duration_s']:.2f}s")
                else:
                    logging.error(f"[ETL-SINGLE] [{i}/{len(processing_items)}] ❌ {item_type} {item_name} failed: {prefix_result['error']}")

        else:
            # Legacy: Process filtered prefixes with old logic
            for i, prefix in enumerate(prefixes_to_process, 1):
                prefix_id = f"SEQ-{i}"
                logging.info(f"[ETL-SINGLE] [{i}/{len(prefixes_to_process)}] Processing prefix: {prefix}")

                prefix_result = process_single_prefix(
                    prefix=prefix,
                    file_inventory=file_inventory,
                    date_override=date_override,
                    process_all=process_all,
                    temp_base_dir=temp_base_dir,
                    prefix_id=prefix_id
                )

                prefix_results.append(prefix_result)

                # Aggregate results
                if prefix_result["status"] == "success":
                    total_downloaded += prefix_result["downloaded"]
                    total_compressed_uploaded += prefix_result["compressed_uploaded"]
                    total_decompressed += prefix_result["decompressed"]
                    total_decompressed_uploaded += prefix_result["decompressed_uploaded"]

                    logging.info(f"[ETL-SINGLE] [{i}/{len(prefixes_to_process)}] ✓ {prefix} completed: {prefix_result['downloaded']} files in {prefix_result['duration_s']:.2f}s")
                else:
                    logging.error(f"[ETL-SINGLE] [{i}/{len(prefixes_to_process)}] ❌ {prefix} failed: {prefix_result['error']}")

    # Final results
    total_duration = time.time() - start_time
    successful_items = len([r for r in prefix_results if r["status"] == "success"])

    if use_phase4_logic:
        total_items = len(processing_items)
        mode_name = "phase4_dealer_discovery"
    else:
        total_items = len(prefixes_to_process)
        mode_name = "single_prefix_sequential"

    result = {
        "mode": mode_name,
        "downloaded": total_downloaded,
        "compressed_uploaded": total_compressed_uploaded,
        "decompressed": total_decompressed,
        "decompressed_uploaded": total_decompressed_uploaded,
        "status": "success" if successful_items == total_items else "partial_success",
        "prefixes_processed": total_items,
        "prefixes_successful": successful_items,
        "prefix_results": prefix_results,
        "duration_s": total_duration,
        "inventory_duration_s": inventory_duration
    }

    logging.info(f"[ETL-SINGLE] ✓ Processing complete ({mode_name}):")
    logging.info(f"[ETL-SINGLE]   Total duration: {total_duration:.2f}s")
    logging.info(f"[ETL-SINGLE]   Inventory time: {inventory_duration:.2f}s ({inventory_duration/total_duration*100:.1f}%)")
    logging.info(f"[ETL-SINGLE]   Items: {successful_items}/{total_items} successful")
    logging.info(f"[ETL-SINGLE]   Files: {total_downloaded} downloaded, {total_decompressed_uploaded} uploaded")

    return result

def run_pipeline_parallel(
    prefixes_filter: Optional[List[str]] = None,
    batch_size: int = 3,
    date_override: Optional[str] = None,
    process_all: bool = False
) -> dict:
    """Execute ETL pipeline using parallel processing with ThreadPoolExecutor

    This function implements true parallel processing by running multiple prefixes
    simultaneously using ThreadPoolExecutor. It uses as_completed() for streaming
    results and provides graceful fallback to sequential mode on errors.

    Args:
        prefixes_filter: List of prefixes to process (None = all enabled)
        batch_size: Maximum number of concurrent threads (clamped to [1, 8])
        date_override: Custom date (None = use DATE_FORMAT)
        process_all: Ignore date filter for backfills

    Returns:
        Dict with aggregated results from all prefixes
    """
    start_time = time.time()
    logging.info("[ETL-PARALLEL] Starting parallel processing...")

    # Check if this is a stress test
    import os
    is_stress_test = os.environ.get('STRESS_TEST', 'false').lower() == 'true'

    # Validate and clamp batch_size
    batch_size = max(1, min(8, batch_size))

    if is_stress_test:
        logging.info("🔥" * 50)
        logging.info("[ETL-PARALLEL] ⚡ STRESS TEST MODE ACTIVATED ⚡")
        logging.info(f"[ETL-PARALLEL] Batch size: {batch_size} (STRESS TEST - high concurrency)")
        logging.info(f"[ETL-PARALLEL] Process all files: {process_all} (ignore date filter)")
        logging.info("🔥" * 50)
    else:
        logging.info(f"[ETL-PARALLEL] Batch size: {batch_size} (max concurrent threads)")

    # Reset per-run container check cache
    try:
        from single_prefix_processor import _container_checked
        _container_checked.clear()
        logging.info("[ETL-PARALLEL] Container cache cleared")
    except Exception as e:
        logging.warning(f"[ETL-PARALLEL] Could not clear container cache: {e}")

    # Phase 4+: Use dealer-aware inventory system (same as sequential mode)
    try:
        # SISTER FILE PHASE 1 TEST: Check configuration loading
        configs = get_source_configs()
        logging.info(f"[SISTER-FILE-DIRECT-TEST] Loaded {len(configs)} configs")
        if configs:
            first_config = configs[0]
            logging.info(f"[SISTER-FILE-DIRECT-TEST] First config: {first_config}")
            from azure_config import requires_sister_files, get_expected_file_count
            logging.info(f"[SISTER-FILE-DIRECT-TEST] requires_sister_files: {requires_sister_files(first_config)}")
            logging.info(f"[SISTER-FILE-DIRECT-TEST] expected_file_count: {get_expected_file_count(first_config)}")

        inventory_result = build_file_inventory_with_dealer_support(
            remote_files=None,  # Will be fetched automatically
            configs=configs,
            date_filter=date_override,
            process_all=process_all
        )
        logging.info(f"[ETL-PARALLEL] Inventory built: {len(inventory_result['non_dealer'])} non-dealer + {len(inventory_result['dealer_compounds'])} dealer items")
    except Exception as e:
        logging.error(f"[ETL-PARALLEL] Failed to build inventory: {e}")
        return {
            "mode": "parallel",
            "status": "error",
            "error": str(e),
            "downloaded": 0,
            "compressed_uploaded": 0,
            "decompressed": 0,
            "decompressed_uploaded": 0,
            "prefixes_processed": 0,
            "prefixes_successful": 0,
            "prefix_results": [],
            "duration_s": time.time() - start_time,
            "batch_size": batch_size
        }

    # Build processing items (same logic as sequential mode)
    processing_items = []

    # Add non-dealer prefixes (handle both regular files and sister file groups)
    for prefix, files_or_groups in inventory_result["non_dealer"].items():
        # Check if this is sister file groups or regular file list
        if files_or_groups and isinstance(files_or_groups[0], dict):
            # Sister file groups: flatten all files from all groups
            all_files = []
            for file_group in files_or_groups:
                all_files.extend(file_group["files"])
            processing_items.append(("non_dealer", prefix, {prefix: all_files}, None))
            logging.info(f"[ETL-PARALLEL] Sister files for {prefix}: {len(files_or_groups)} groups, {len(all_files)} total files")
        else:
            # Regular file list
            processing_items.append(("non_dealer", prefix, {prefix: files_or_groups}, None))

    # Add dealer compounds with their parent config
    dealer_configs_by_compound = {}
    for config in inventory_result["dealer_configs"]:
        family_suffix = extract_family_suffix(config["file_prefix"])
        for compound in inventory_result["dealer_compounds"].keys():
            if compound.endswith(f"_{family_suffix}"):
                dealer_configs_by_compound[compound] = config

    for compound, files in inventory_result["dealer_compounds"].items():
        parent_config = dealer_configs_by_compound.get(compound)
        processing_items.append(("dealer_compound", compound, {compound: files}, parent_config))

    # Apply prefix filtering if specified
    if prefixes_filter:
        filtered_items = []
        for item_type, item_name, item_inventory, dealer_config in processing_items:
            if item_name in prefixes_filter:
                filtered_items.append((item_type, item_name, item_inventory, dealer_config))
        processing_items = filtered_items
        logging.info(f"[ETL-PARALLEL] Filtered to {len(processing_items)} items matching: {prefixes_filter}")
    else:
        logging.info(f"[ETL-PARALLEL] Processing all {len(processing_items)} items (non-dealer + dealer compounds)")

    if len(processing_items) == 0:
        logging.warning("[ETL-PARALLEL] No items to process")
        return {
            "mode": "parallel",
            "status": "success",
            "downloaded": 0,
            "compressed_uploaded": 0,
            "decompressed": 0,
            "decompressed_uploaded": 0,
            "prefixes_processed": 0,
            "prefixes_successful": 0,
            "prefix_results": [],
            "duration_s": time.time() - start_time,
            "batch_size": batch_size
        }

    # Inventory was already built above using dealer-aware system
    inventory_duration = inventory_result.get("inventory_duration_s", 0)

    # Parallel processing with ThreadPoolExecutor
    prefix_results = []
    total_downloaded = 0
    total_compressed_uploaded = 0
    total_decompressed = 0
    total_decompressed_uploaded = 0

    with tempfile.TemporaryDirectory() as temp_base_dir:
        logging.info(f"[ETL-PARALLEL] Using shared temp directory: {temp_base_dir}")
        logging.info(f"[ETL-PARALLEL] Starting {len(processing_items)} items with batch_size={batch_size}")

        try:
            with ThreadPoolExecutor(max_workers=batch_size) as executor:
                # Submit all processing tasks
                future_to_item = {}
                for i, (item_type, item_name, item_inventory, dealer_config) in enumerate(processing_items, 1):
                    prefix_id = f"THREAD-{i}"
                    logging.info(f"[ETL-PARALLEL] Submitting task {i}/{len(processing_items)}: {item_name} ({item_type}) (thread_id={prefix_id})")

                    future = executor.submit(
                        process_single_prefix,
                        prefix=item_name,
                        file_inventory=item_inventory,
                        date_override=date_override,
                        process_all=process_all,
                        temp_base_dir=temp_base_dir,
                        prefix_id=prefix_id,
                        dealer_config=dealer_config
                    )
                    future_to_item[future] = (item_type, item_name, prefix_id, i)

                # Process results as they complete (streaming)
                logging.info(f"[ETL-PARALLEL] All tasks submitted, waiting for completion...")
                completed_count = 0

                for future in as_completed(future_to_item):
                    item_type, item_name, prefix_id, _ = future_to_item[future]
                    completed_count += 1

                    try:
                        prefix_result = future.result()
                        prefix_results.append(prefix_result)

                        # Log completion immediately (streaming logs)
                        if prefix_result["status"] == "success":
                            total_downloaded += prefix_result["downloaded"]
                            total_compressed_uploaded += prefix_result["compressed_uploaded"]
                            total_decompressed += prefix_result["decompressed"]
                            total_decompressed_uploaded += prefix_result["decompressed_uploaded"]

                            logging.info(f"[ETL-PARALLEL] ✅ [{completed_count}/{len(processing_items)}] {item_name} ({item_type}) completed: {prefix_result['downloaded']} files in {prefix_result['duration_s']:.2f}s")
                        else:
                            logging.error(f"[ETL-PARALLEL] ❌ [{completed_count}/{len(processing_items)}] {item_name} ({item_type}) failed: {prefix_result['error']}")

                    except Exception as e:
                        # Handle individual task failures
                        error_result = {
                            "prefix": item_name,
                            "status": "error",
                            "error": str(e),
                            "downloaded": 0,
                            "compressed_uploaded": 0,
                            "decompressed": 0,
                            "decompressed_uploaded": 0,
                            "duration_s": 0
                        }
                        prefix_results.append(error_result)
                        logging.error(f"[ETL-PARALLEL] ❌ [{completed_count}/{len(processing_items)}] {item_name} ({item_type}) exception: {e}")

        except Exception as e:
            logging.error(f"[ETL-PARALLEL] ❌ ThreadPoolExecutor failed: {e}")
            logging.info("[ETL-PARALLEL] Falling back to sequential mode")
            return run_pipeline_single_prefix_sequential(prefixes_filter, date_override, process_all)

    # Final results
    total_duration = time.time() - start_time
    successful_items = len([r for r in prefix_results if r["status"] == "success"])

    result = {
        "mode": "parallel",
        "downloaded": total_downloaded,
        "compressed_uploaded": total_compressed_uploaded,
        "decompressed": total_decompressed,
        "decompressed_uploaded": total_decompressed_uploaded,
        "status": "success" if successful_items == len(processing_items) else "partial_success",
        "prefixes_processed": len(processing_items),
        "prefixes_successful": successful_items,
        "prefix_results": prefix_results,
        "duration_s": total_duration,
        "inventory_duration_s": inventory_duration,
        "batch_size": batch_size
    }

    # Calculate time savings vs sequential
    sequential_time_estimate = sum(r.get("duration_s", 0) for r in prefix_results) + inventory_duration
    time_savings = sequential_time_estimate - total_duration
    speedup_factor = sequential_time_estimate / total_duration if total_duration > 0 else 1

    logging.info(f"[ETL-PARALLEL] ✅ Parallel processing complete:")
    logging.info(f"[ETL-PARALLEL]   Total duration: {total_duration:.2f}s")
    logging.info(f"[ETL-PARALLEL]   Inventory time: {inventory_duration:.2f}s ({inventory_duration/total_duration*100:.1f}%)")
    logging.info(f"[ETL-PARALLEL]   Batch size: {batch_size}")
    logging.info(f"[ETL-PARALLEL]   Items: {successful_items}/{len(processing_items)} successful")
    logging.info(f"[ETL-PARALLEL]   Files: {total_downloaded} downloaded, {total_decompressed_uploaded} uploaded")
    logging.info(f"[ETL-PARALLEL]   Performance: {speedup_factor:.2f}x speedup, saved {time_savings:.2f}s")

    return result
