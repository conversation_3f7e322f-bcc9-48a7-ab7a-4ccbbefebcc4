import zipfile
import pyzipper
import os
import sys
import time
import logging
from datetime import datetime, timezone

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from azure_config import ZIP_PASSWORD

def decompress_files(source_dir, target_dir):
    """Decompress files from source to target directory (with non-intrusive logging)"""
    start_ts = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S %Z')
    logging.info(f"[ZIP] Start decompression at {start_ts} → source={source_dir}, target={target_dir}")

    decompressed_files = []

    zip_files = [f for f in os.listdir(source_dir) if f.lower().endswith('.zip')]
    logging.info(f"[ZIP] Found {len(zip_files)} zip files to decompress")

    for zip_filename in zip_files:
        zip_path = os.path.join(source_dir, zip_filename)
        logging.info(f"[ZIP] Decompressing: {zip_filename}")

        try:
            # Try AES decryption first
            t0 = time.time()
            with pyzipper.AESZipFile(zip_path, 'r') as zip_ref:
                if ZIP_PASSWORD:
                    zip_ref.setpassword(ZIP_PASSWORD.encode())
                members = zip_ref.namelist()
                zip_ref.extractall(target_dir)
                for extracted_file in members:
                    decompressed_files.append(os.path.join(target_dir, extracted_file))
                    logging.info(f"[ZIP] ✓ Extracted: {extracted_file}")
            logging.info(f"[ZIP] AES extraction completed for {zip_filename} in {time.time() - t0:.2f}s")

        except Exception:
            # Fallback to standard zip
            try:
                logging.info(f"[ZIP] Trying standard zip extraction for: {zip_filename}")
                t1 = time.time()
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    members = zip_ref.namelist()
                    if ZIP_PASSWORD:
                        zip_ref.extractall(target_dir, pwd=ZIP_PASSWORD.encode())
                    else:
                        zip_ref.extractall(target_dir)
                    for extracted_file in members:
                        decompressed_files.append(os.path.join(target_dir, extracted_file))
                        logging.info(f"[ZIP] ✓ Extracted: {extracted_file}")
                logging.info(f"[ZIP] Standard extraction completed for {zip_filename} in {time.time() - t1:.2f}s")
            except Exception as e:
                logging.error(f"[ZIP] Decompression error for {zip_filename}: {e}")

    return decompressed_files
