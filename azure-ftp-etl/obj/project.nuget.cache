{"version": 2, "dgSpecHash": "e0cA+NlAExI=", "success": false, "projectFilePath": "/Users/<USER>/Developer/GPT/swickard-edw/azure-ftp-etl/extensions.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/microsoft.azure.webjobs.extensions.durabletask/3.4.1/microsoft.azure.webjobs.extensions.durabletask.3.4.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.azure.webjobs.script.extensionsmetadatagenerator/1.1.3/microsoft.azure.webjobs.script.extensionsmetadatagenerator.1.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.build.framework/15.3.409/microsoft.build.framework.15.3.409.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.build.utilities.core/15.3.409/microsoft.build.utilities.core.15.3.409.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.platforms/1.1.0/microsoft.netcore.platforms.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.targets/1.1.0/microsoft.netcore.targets.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.primitives/4.0.1/microsoft.win32.primitives.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.registry/4.0.0/microsoft.win32.registry.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system/4.0.0/runtime.native.system.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.appcontext/4.1.0/system.appcontext.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections/4.0.11/system.collections.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.concurrent/4.0.12/system.collections.concurrent.4.0.12.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.nongeneric/4.0.1/system.collections.nongeneric.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.console/4.0.0/system.console.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.debug/4.0.11/system.diagnostics.debug.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.process/4.1.0/system.diagnostics.process.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.tracesource/4.0.0/system.diagnostics.tracesource.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.tracing/4.1.0/system.diagnostics.tracing.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization/4.0.11/system.globalization.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io/4.3.0/system.io.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem/4.0.1/system.io.filesystem.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem.primitives/4.0.1/system.io.filesystem.primitives.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.linq/4.1.0/system.linq.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.objectmodel/4.0.12/system.objectmodel.4.0.12.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.private.datacontractserialization/4.1.1/system.private.datacontractserialization.4.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection/4.3.0/system.reflection.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit/4.0.1/system.reflection.emit.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit.ilgeneration/4.0.1/system.reflection.emit.ilgeneration.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit.lightweight/4.0.1/system.reflection.emit.lightweight.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.extensions/4.0.1/system.reflection.extensions.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.primitives/4.3.0/system.reflection.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.typeextensions/4.1.0/system.reflection.typeextensions.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.resources.reader/4.0.0/system.resources.reader.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.resources.resourcemanager/4.0.1/system.resources.resourcemanager.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime/4.3.0/system.runtime.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.extensions/4.1.0/system.runtime.extensions.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.handles/4.0.1/system.runtime.handles.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.interopservices/4.1.0/system.runtime.interopservices.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.interopservices.runtimeinformation/4.0.0/system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.loader/4.3.0/system.runtime.loader.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.serialization.primitives/4.1.1/system.runtime.serialization.primitives.4.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.serialization.xml/4.1.1/system.runtime.serialization.xml.4.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding/4.3.0/system.text.encoding.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding.codepages/4.0.1/system.text.encoding.codepages.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding.extensions/4.0.11/system.text.encoding.extensions.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.regularexpressions/4.1.0/system.text.regularexpressions.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading/4.0.11/system.threading.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks/4.3.0/system.threading.tasks.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks.extensions/4.0.0/system.threading.tasks.extensions.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.thread/4.0.0/system.threading.thread.4.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.threadpool/4.0.10/system.threading.threadpool.4.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.timer/4.0.1/system.threading.timer.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.xml.readerwriter/4.0.11/system.xml.readerwriter.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.xml.xmldocument/4.0.1/system.xml.xmldocument.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.xml.xmlserializer/4.0.11/system.xml.xmlserializer.4.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/3.1.0/microsoft.netcore.app.ref.3.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/3.1.10/microsoft.aspnetcore.app.ref.3.1.10.nupkg.sha512"], "logs": [{"code": "NU1202", "level": "Error", "message": "Package Microsoft.Azure.WebJobs.Extensions.DurableTask 3.4.1 is not compatible with netcoreapp3.1 (.NETCoreApp,Version=v3.1). Package Microsoft.Azure.WebJobs.Extensions.DurableTask 3.4.1 supports: net6.0 (.NETCoreApp,Version=v6.0)", "projectPath": "/Users/<USER>/Developer/GPT/swickard-edw/azure-ftp-etl/extensions.csproj", "filePath": "/Users/<USER>/Developer/GPT/swickard-edw/azure-ftp-etl/extensions.csproj", "libraryId": "Microsoft.Azure.WebJobs.Extensions.DurableTask", "targetGraphs": [".NETCoreApp,Version=v3.1"]}]}