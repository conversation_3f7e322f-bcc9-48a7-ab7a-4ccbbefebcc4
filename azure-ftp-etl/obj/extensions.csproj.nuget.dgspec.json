{"format": 1, "restore": {"/Users/<USER>/Developer/GPT/swickard-edw/azure-ftp-etl/extensions.csproj": {}}, "projects": {"/Users/<USER>/Developer/GPT/swickard-edw/azure-ftp-etl/extensions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Developer/GPT/swickard-edw/azure-ftp-etl/extensions.csproj", "projectName": "extensions", "projectPath": "/Users/<USER>/Developer/GPT/swickard-edw/azure-ftp-etl/extensions.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Developer/GPT/swickard-edw/azure-ftp-etl/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Microsoft.Azure.WebJobs.Extensions.DurableTask": {"target": "Package", "version": "[3.4.1, )"}, "Microsoft.Azure.WebJobs.Script.ExtensionsMetadataGenerator": {"target": "Package", "version": "[1.1.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[3.1.10, 3.1.10]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[3.1.0, 3.1.0]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.305/RuntimeIdentifierGraph.json"}}}}}