import azure.functions as func
import logging
import json
import sys
import os
import time
import re
from typing import List, Optional

# Add shared folder to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

def parse_and_validate_parameters(req: func.HttpRequest) -> dict:
    """Parse and validate HTTP parameters for the ETL pipeline"""
    params = {
        'mode': 'parallel',    # Default to parallel processing
        'batch_size': 2,       # Default batch size for production
        'prefixes': None,      # None = use all enabled prefixes
        'date': None,          # None = use current DATE_FORMAT
        'process_all': False   # False = use date filtering
    }

    # Parse mode parameter (now defaults to parallel)
    mode = req.params.get('mode', 'parallel').lower()
    if mode in ['parallel']:
        params['mode'] = mode
    else:
        logging.warning(f"[PARAMS] Invalid mode '{mode}', defaulting to 'parallel'")
        params['mode'] = 'parallel'

    # Parse batch_size parameter (clamp to [1, 8])
    try:
        batch_size = int(req.params.get('batch_size', 2))
        params['batch_size'] = max(1, min(8, batch_size))  # Clamp to safe range
        if batch_size != params['batch_size']:
            logging.warning(f"[PARAMS] batch_size {batch_size} clamped to {params['batch_size']}")
    except (ValueError, TypeError):
        logging.warning(f"[PARAMS] Invalid batch_size, defaulting to 2")
        params['batch_size'] = 2

    # Parse prefixes parameter (validate against enabled prefixes)
    prefixes_param = req.params.get('prefixes')
    if prefixes_param:
        try:
            from azure_config import get_file_prefixes
            enabled_prefixes = set(get_file_prefixes())
            requested_prefixes = [p.strip() for p in prefixes_param.split(',') if p.strip()]

            # Validate that requested prefixes are subset of enabled prefixes
            valid_prefixes = [p for p in requested_prefixes if p in enabled_prefixes]
            invalid_prefixes = [p for p in requested_prefixes if p not in enabled_prefixes]

            if invalid_prefixes:
                logging.warning(f"[PARAMS] Invalid prefixes ignored: {invalid_prefixes}")

            if valid_prefixes:
                params['prefixes'] = valid_prefixes
                logging.info(f"[PARAMS] Using specific prefixes: {valid_prefixes}")
            else:
                logging.warning(f"[PARAMS] No valid prefixes found, using all enabled prefixes")
                params['prefixes'] = None
        except Exception as e:
            logging.error(f"[PARAMS] Error validating prefixes: {e}")
            params['prefixes'] = None

    # Parse date parameter (validate MM-DD-YY format)
    date_param = req.params.get('date')
    if date_param:
        # Validate MM-DD-YY format
        if re.match(r'^\d{2}-\d{2}-\d{2}$', date_param):
            params['date'] = date_param
            logging.info(f"[PARAMS] Using custom date: {date_param}")
        else:
            logging.warning(f"[PARAMS] Invalid date format '{date_param}', expected MM-DD-YY")
            params['date'] = None

    # Parse process_all parameter
    process_all_param = req.params.get('process_all', 'false').lower()
    params['process_all'] = process_all_param in ['true', '1', 'yes']

    return params

def main(req: func.HttpRequest) -> func.HttpResponse:
    start = time.time()
    logging.info('=' * 80)
    logging.info('[HTTP] FTP ETL Pipeline invoked')

    # Check if this is a test run from Azure Portal "Run" button
    # Azure Portal sends POST with only the 'code' parameter
    is_test_run = req.method == 'POST' and len(req.params) <= 1 and 'code' in req.params

    # Check for stress test mode (set STRESS_TEST=true in App Settings)
    import os
    stress_test_mode = os.environ.get('STRESS_TEST', 'false').lower() == 'true'

    if is_test_run:
        if stress_test_mode:
            logging.info('[HTTP] Detected Azure Portal test run - STRESS TEST MODE ENABLED')
        else:
            logging.info('[HTTP] Detected Azure Portal test run - respecting requested mode')

    logging.info('=' * 80)

    try:
        # Quiet Azure SDK verbose http logs
        try:
            from azure_config import setup_quiet_azure_logging
            setup_quiet_azure_logging()
        except Exception:
            pass

        # Parse and validate parameters
        logging.info('[HTTP] Parsing request parameters...')
        params = parse_and_validate_parameters(req)

        # Override for Azure Portal test runs
        if is_test_run:
            if stress_test_mode:
                # STRESS TEST CONFIGURATION
                params['mode'] = 'parallel'
                params['batch_size'] = 4  # Higher concurrency for stress test
                params['process_all'] = True  # Process ALL files, not just today's
                params['prefixes'] = None  # Process ALL enabled prefixes
                logging.info('[HTTP] STRESS TEST MODE - overriding to parallel mode with batch_size=4, process_all=True')
                logging.info('[HTTP] STRESS TEST will process ALL files for ALL prefixes (ignore date filter)')
            else:
                # Normal test configuration - respect user's mode choice
                if params['mode'] == 'parallel':
                    params['batch_size'] = 2
                    logging.info('[HTTP] Azure Portal test run - using parallel mode with batch_size=2')
                else:
                    logging.info(f'[HTTP] Azure Portal test run - using {params["mode"]} mode as requested')

        # Log execution plan
        logging.info('[HTTP] EXECUTION PLAN:')
        logging.info(f'[HTTP]   Mode: {params["mode"]}')
        logging.info(f'[HTTP]   Batch size: {params["batch_size"]}')
        logging.info(f'[HTTP]   Prefixes: {params["prefixes"] or "all enabled"}')
        logging.info(f'[HTTP]   Date: {params["date"] or "current date"}')
        logging.info(f'[HTTP]   Process all: {params["process_all"]}')

        # Initialize result structure
        result = {
            "status": "success",
            "mode": params["mode"],
            "parameters": params,
            "steps": []
        }

        # Step 1: Test imports
        try:
            from orchestrator import run_pipeline_parallel
            result["steps"].append("✓ Imports successful")
            logging.info('[HTTP] ✓ Imports successful')
        except Exception as e:
            result["steps"].append(f"❌ Import failed: {str(e)}")
            logging.error(f'[HTTP] ❌ Import failed: {str(e)}')
            return func.HttpResponse(json.dumps(result), status_code=500, mimetype="application/json")

        # Step 2: Test SFTP config
        try:
            from azure_config import SFTP_CONFIG, FILE_PREFIXES, get_date_format
            current_date_format = get_date_format()
            result["steps"].append("✓ Config loaded successfully")
            result["config"] = {
                "hostname": SFTP_CONFIG['hostname'],
                "port": SFTP_CONFIG['port'],
                "prefixes": FILE_PREFIXES,
                "date": current_date_format
            }
            logging.info('[HTTP] ✓ Config loaded successfully')
        except Exception as e:
            result["steps"].append(f"❌ Config failed: {str(e)}")
            logging.error(f'[HTTP] ❌ Config failed: {str(e)}')
            return func.HttpResponse(json.dumps(result), status_code=500, mimetype="application/json")

        # Step 3: Test Azure Storage config
        try:
            from azure_config import blob_service_client
            _ = blob_service_client  # no-op to avoid lint warning
            result["steps"].append("✓ Storage client created")
            logging.info('[HTTP] ✓ Storage client created')
        except Exception as e:
            result["steps"].append(f"❌ Storage client failed: {str(e)}")
            logging.error(f'[HTTP] ❌ Storage client failed: {str(e)}')
            return func.HttpResponse(json.dumps(result), status_code=500, mimetype="application/json")

        # Step 4: Execute pipeline based on mode
        try:
            if params["mode"] == "sequential":
                # Sequential mode with Phase 4+ dealer discovery
                logging.info('[HTTP] Executing sequential pipeline with dealer discovery...')
                result["steps"].append("✓ Sequential processing enabled")

                # Import the sequential processing function
                from orchestrator import run_pipeline_single_prefix_sequential
                pipeline_result = run_pipeline_single_prefix_sequential(
                    prefixes_filter=params["prefixes"],
                    date_override=params["date"],
                    process_all=params["process_all"]
                )
            else:
                # Parallel mode using ThreadPoolExecutor
                logging.info('[HTTP] Executing parallel pipeline...')
                result["steps"].append("✓ Parallel processing enabled")

                # Import the parallel processing function
                from orchestrator import run_pipeline_parallel
                pipeline_result = run_pipeline_parallel(
                    prefixes_filter=params["prefixes"],
                    batch_size=params["batch_size"],
                    date_override=params["date"],
                    process_all=params["process_all"]
                )

            result["steps"].append("✓ Pipeline completed")
            result["pipeline_result"] = pipeline_result
            logging.info('[HTTP] ✓ Pipeline completed successfully')

        except Exception as e:
            result["steps"].append(f"❌ Pipeline failed: {str(e)}")
            result["error_details"] = str(e)
            result["status"] = "error"
            logging.error(f'[HTTP] ❌ Pipeline failed: {str(e)}')

        # Add timing and final status
        result["duration_s"] = round(time.time() - start, 2)

        logging.info('=' * 80)
        logging.info(f'[HTTP] EXECUTION COMPLETE:')
        logging.info(f'[HTTP]   Status: {result["status"]}')
        logging.info(f'[HTTP]   Mode: {result["mode"]}')
        logging.info(f'[HTTP]   Duration: {result["duration_s"]}s')
        if "pipeline_result" in result:
            pr = result["pipeline_result"]
            logging.info(f'[HTTP]   Files processed: {pr.get("downloaded", 0)} downloaded, {pr.get("decompressed_uploaded", 0)} uploaded')
        logging.info('=' * 80)

        # Determine HTTP status code
        status_code = 200 if result["status"] == "success" else 500

        return func.HttpResponse(
            json.dumps(result, indent=2),
            status_code=status_code,
            mimetype="application/json"
        )

    except Exception as e:
        duration = round(time.time() - start, 2)
        error_result = {
            "status": "error",
            "message": str(e),
            "type": "function_error",
            "duration_s": duration
        }

        logging.error('=' * 80)
        logging.error(f'[HTTP] FUNCTION ERROR after {duration}s: {str(e)}')
        logging.error('=' * 80)

        return func.HttpResponse(
            json.dumps(error_result, indent=2),
            status_code=500,
            mimetype="application/json"
        )
