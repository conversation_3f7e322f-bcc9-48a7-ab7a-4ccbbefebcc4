import paramiko
import os
from config import SFTP_CONFIG, FILE_PREFIXES, DATE_FORMAT, COMPRESSED_DIR
from logger import setup_logger, log_success, log_error

def download_files():
    os.makedirs(COMPRESSED_DIR, exist_ok=True)

    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        ssh.connect(**SFTP_CONFIG)
        sftp = ssh.open_sftp()

        for prefix in FILE_PREFIXES:
            logger = setup_logger(prefix)
            files = sftp.listdir('.')
            matching_files = [f for f in files if f.startswith(f"{prefix}_{DATE_FORMAT}_") and
                            (f.lower().endswith('.csv.zip') or f.upper().endswith('.csv.zip'))]

            for filename in matching_files:
                try:
                    sftp.get(filename, os.path.join(COMPRESSED_DIR, filename))
                    log_success(logger, "DOWNLOAD", filename)
                except Exception as e:
                    log_error(logger, "DOWNLOAD", filename, e)

        sftp.close()
    except Exception as e:
        print(f"SFTP connection failed: {e}")
    finally:
        ssh.close()

if __name__ == "__main__":
    download_files()
