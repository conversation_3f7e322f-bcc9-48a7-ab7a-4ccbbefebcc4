2025-09-16T19:27:51Z   [Verbose]   AuthenticationScheme: WebJobsAuthLevel was successfully authenticated.
2025-09-16T19:27:51Z   [Verbose]   Authorization was successful.
2025-09-16T19:27:51Z   [Information]   Executing 'Functions.CSVProcessingPipeline' (Reason='This function was programmatically called via the host APIs.', Id=2e917b65-212e-4bbd-b493-98c2035b924d)
2025-09-16T19:27:51Z   [Verbose]   Sending invocation id: '2e917b65-212e-4bbd-b493-98c2035b924d
2025-09-16T19:27:51Z   [Verbose]   Posting invocation id:2e917b65-212e-4bbd-b493-98c2035b924d on workerId:c7a2690c-91d9-41f0-81ee-4d6de4bfc54f
2025-09-16T19:27:51Z   [Information]   CSV Processing Pipeline triggered
2025-09-16T19:27:51Z   [Information]   Starting CSV processing pipeline for container: None
2025-09-16T19:27:51Z   [Information]   🚀 Starting dynamic job processing based on Azure Table Storage configuration
2025-09-16T19:27:51Z   [Information]   🚀 Starting dynamic job processing based on Azure Table Storage configuration
2025-09-16T19:27:51Z   [Information]   Request URL: 'http://localhost:8081/msi/token?api-version=REDACTED&resource=REDACTED'
Request method: 'GET'
Request headers:
    'X-IDENTITY-HEADER': 'REDACTED'
    'Metadata': 'REDACTED'
    'User-Agent': 'azsdk-python-identity/1.25.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
No body was attached to the request
2025-09-16T19:27:58Z   [Information]   Response status: 200
Response headers:
    'Content-Type': 'application/json; charset=utf-8'
    'Date': 'Tue, 16 Sep 2025 19:27:57 GMT'
    'Server': 'Kestrel'
    'Transfer-Encoding': 'chunked'
    'X-CORRELATION-ID': 'REDACTED'
2025-09-16T19:27:58Z   [Information]   DefaultAzureCredential acquired a token from ManagedIdentityCredential
2025-09-16T19:27:58Z   [Information]   Request URL: 'https://sagedw.table.core.windows.net/sftpConfig()?$filter=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'DataServiceVersion': 'REDACTED'
    'Accept': 'application/json;odata=minimalmetadata'
    'x-ms-client-request-id': '3c531d0e-9333-11f0-8771-00155dea3ad8'
    'x-ms-date': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:27:51 GMT'
    'User-Agent': 'azsdk-python-data-tables/12.7.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:27:58Z   [Information]   Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/json;odata=minimalmetadata;streaming=true;charset=utf-8'
    'Server': 'Windows-Azure-Table/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': 'fa0fbded-e002-0050-4240-2770a0000000'
    'x-ms-client-request-id': '3c531d0e-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:27:57 GMT'
2025-09-16T19:27:58Z   [Information]   Configuration cache refreshed. Found 2 enabled jobs.
2025-09-16T19:27:58Z   [Information]   📋 Found 2 enabled jobs: ['FIMASTSALES_', 'RO_TIME']
2025-09-16T19:27:58Z   [Information]   🔄 Processing job: FIMASTSALES_
2025-09-16T19:27:58Z   [Information]   📁 Job FIMASTSALES_ configuration:
2025-09-16T19:27:58Z   [Information]     - Source: sftp-landing/FIMAST-SALES/fimastsales-decompressed
2025-09-16T19:27:58Z   [Information]     - Output: sftp-landing/FIMAST-SALES/fimastsales-enhanced
2025-09-16T19:27:58Z   [Information]     - File prefix: FIMASTSALES_
2025-09-16T19:27:58Z   [Information]   🔄 Processing single job: FIMASTSALES_
2025-09-16T19:27:58Z   [Information]   📂 Source: sftp-landing/FIMAST-SALES/fimastsales-decompressed
2025-09-16T19:27:58Z   [Information]   📤 Output: sftp-landing/FIMAST-SALES/fimastsales-enhanced
2025-09-16T19:27:58Z   [Information]   🔍 File patterns: ['FIMASTSALES_']
2025-09-16T19:27:58Z   [Information]   Listing Parquet files in container: sftp-landing, folder: FIMAST-SALES/fimastsales-decompressed
2025-09-16T19:27:58Z   [Information]   Listing Parquet files in container: sftp-landing, folder: FIMAST-SALES/fimastsales-decompressed
2025-09-16T19:27:58Z   [Information]   AppServiceCredential.get_token_info succeeded
2025-09-16T19:27:58Z   [Information]   ManagedIdentityCredential.get_token_info succeeded
2025-09-16T19:27:58Z   [Information]   DefaultAzureCredential acquired a token from ManagedIdentityCredential
2025-09-16T19:27:58Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED&prefix=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4049a7ca-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:27:58Z   [Information]   Response status: 200
Response headers:
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee2880-e01e-0050-7e40-2770a0000000'
    'x-ms-client-request-id': '4049a7ca-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:27:57 GMT'
2025-09-16T19:27:58Z   [Information]   Total Parquet files found: 0
2025-09-16T19:27:58Z   [Information]   Listing CSV files in container: sftp-landing, folder: FIMAST-SALES/fimastsales-decompressed
2025-09-16T19:27:58Z   [Information]   Listing CSV files in container: sftp-landing, folder: FIMAST-SALES/fimastsales-decompressed
2025-09-16T19:27:58Z   [Information]   Job key: FIMASTSALES_, Dealer prefix enabled: False
2025-09-16T19:27:58Z   [Information]   Using standard structure for job: FIMASTSALES_
2025-09-16T19:27:58Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED&prefix=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '405d9b9a-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:27:58Z   [Information]   Response status: 200
Response headers:
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee28c4-e01e-0050-3a40-2770a0000000'
    'x-ms-client-request-id': '405d9b9a-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:27:57 GMT'
2025-09-16T19:27:58Z   [Information]   ✓ Found matching CSV file: FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV
2025-09-16T19:27:58Z   [Information]   Total CSV files found: 1
2025-09-16T19:27:58Z   [Information]   ✓ Found 1 CSV files, using hybrid CSV-to-Parquet pipeline
2025-09-16T19:27:58Z   [Information]   🚀 Starting CSV-to-Parquet Hybrid Pipeline...
2025-09-16T19:27:58Z   [Information]   ================================================================================
2025-09-16T19:27:58Z   [Information]   📊 STANDARD PROCESSING: Consolidating multiple files
2025-09-16T19:27:58Z   [Information]   ============================================================
2025-09-16T19:27:58Z   [Information]   Found 1 CSV files to process
2025-09-16T19:27:58Z   [Information]   🔄 Converting 1 CSV files to Parquet individually...
2025-09-16T19:27:58Z   [Information]   📄 Processing CSV file 1/1: FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV
2025-09-16T19:27:58Z   [Information]   Downloading CSV from blob: FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV
2025-09-16T19:27:58Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4062e276-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:27:58Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '58389814'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:42 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3C35DA54"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee28e1-e01e-0050-5640-2770a0000000'
    'x-ms-client-request-id': '4062e276-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:27:57 GMT'
2025-09-16T19:27:58Z   [Information]   Blob size: 55.68 MB
2025-09-16T19:27:58Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'GET'
Request headers:
    'x-ms-range': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4067bbe8-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:27:58Z   [Information]   Response status: 206
Response headers:
    'Content-Length': '33554432'
    'Content-Type': 'application/octet-stream'
    'Content-Range': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:42 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3C35DA54"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee28f0-e01e-0050-6540-2770a0000000'
    'x-ms-client-request-id': '4067bbe8-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-blob-content-md5': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:27:57 GMT'
2025-09-16T19:27:59Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'GET'
Request headers:
    'x-ms-range': 'REDACTED'
    'If-Match': '"0x8DDF49A3C35DA54"'
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '41090994-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:27:59Z   [Information]   Response status: 206
Response headers:
    'Content-Length': '4194304'
    'Content-Type': 'application/octet-stream'
    'Content-Range': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:42 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3C35DA54"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee2c1c-e01e-0050-3d40-2770a0000000'
    'x-ms-client-request-id': '41090994-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-blob-content-md5': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:27:58 GMT'
2025-09-16T19:27:59Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'GET'
Request headers:
    'x-ms-range': 'REDACTED'
    'If-Match': '"0x8DDF49A3C35DA54"'
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '411bb742-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:27:59Z   [Information]   Response status: 206
Response headers:
    'Content-Length': '4194304'
    'Content-Type': 'application/octet-stream'
    'Content-Range': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:42 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3C35DA54"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee2c5d-e01e-0050-7840-2770a0000000'
    'x-ms-client-request-id': '411bb742-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-blob-content-md5': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:27:58 GMT'
2025-09-16T19:27:59Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'GET'
Request headers:
    'x-ms-range': 'REDACTED'
    'If-Match': '"0x8DDF49A3C35DA54"'
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '412ccc1c-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:00Z   [Information]   Response status: 206
Response headers:
    'Content-Length': '4194304'
    'Content-Type': 'application/octet-stream'
    'Content-Range': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:42 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3C35DA54"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee2caf-e01e-0050-3f40-2770a0000000'
    'x-ms-client-request-id': '412ccc1c-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-blob-content-md5': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:27:58 GMT'
2025-09-16T19:28:00Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'GET'
Request headers:
    'x-ms-range': 'REDACTED'
    'If-Match': '"0x8DDF49A3C35DA54"'
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '413f81fe-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:00Z   [Information]   Response status: 206
Response headers:
    'Content-Length': '4194304'
    'Content-Type': 'application/octet-stream'
    'Content-Range': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:42 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3C35DA54"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee2d08-e01e-0050-0440-2770a0000000'
    'x-ms-client-request-id': '413f81fe-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-blob-content-md5': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:27:59 GMT'
2025-09-16T19:28:00Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'GET'
Request headers:
    'x-ms-range': 'REDACTED'
    'If-Match': '"0x8DDF49A3C35DA54"'
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4187d062-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:00Z   [Information]   Response status: 206
Response headers:
    'Content-Length': '4194304'
    'Content-Type': 'application/octet-stream'
    'Content-Range': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:42 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3C35DA54"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee2e0d-e01e-0050-5740-2770a0000000'
    'x-ms-client-request-id': '4187d062-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-blob-content-md5': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:27:59 GMT'
2025-09-16T19:28:00Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'GET'
Request headers:
    'x-ms-range': 'REDACTED'
    'If-Match': '"0x8DDF49A3C35DA54"'
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '41a46c36-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:00Z   [Information]   Response status: 206
Response headers:
    'Content-Length': '3863862'
    'Content-Type': 'application/octet-stream'
    'Content-Range': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:42 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3C35DA54"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee2ec1-e01e-0050-7b40-2770a0000000'
    'x-ms-client-request-id': '41a46c36-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-blob-content-md5': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:27:59 GMT'
2025-09-16T19:28:06Z   [Information]   ✓ Downloaded CSV: FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV - Shape: (92008, 94)
2025-09-16T19:28:06Z   [Information]   ✓ Loaded CSV: (92008, 94)
2025-09-16T19:28:06Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:06Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:06Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:06Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:06Z   [Information]   🔄 Starting Parquet upload - Blob: FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.CSV
2025-09-16T19:28:06Z   [Information]   📊 DataFrame shape: (92008, 94)
2025-09-16T19:28:06Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:06Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:06Z   [Information]   Ensuring destination folder exists: FIMAST-SALES/fimastsales-enhanced
2025-09-16T19:28:06Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED&prefix=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '44f4a2b6-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:06Z   [Information]   Response status: 200
Response headers:
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee3c82-e01e-0050-4340-2770a0000000'
    'x-ms-client-request-id': '44f4a2b6-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:05 GMT'
2025-09-16T19:28:06Z   [Information]   Creating destination folder structure with placeholder: FIMAST-SALES/fimastsales-enhanced/.placeholder
2025-09-16T19:28:06Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-enhanced/.placeholder'
Request method: 'PUT'
Request headers:
    'x-ms-blob-type': 'REDACTED'
    'Content-Length': '0'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '44f98efc-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:06Z   [Information]   Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:05 GMT'
    'ETag': '"0x8DDF5572938BC10"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee3c8d-e01e-0050-4e40-2770a0000000'
    'x-ms-client-request-id': '44f98efc-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:05 GMT'
2025-09-16T19:28:06Z   [Information]   ✓ Destination folder created: FIMAST-SALES/fimastsales-enhanced
2025-09-16T19:28:06Z   [Information]   📝 Parquet size: 14625648 bytes
2025-09-16T19:28:06Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED'
Request method: 'PUT'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '45445fae-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:06Z   [Information]   Response status: 409
Response headers:
    'Content-Length': '230'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee3def-e01e-0050-1740-2770a0000000'
    'x-ms-client-request-id': '45445fae-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-error-code': 'ContainerAlreadyExists'
    'Date': 'Tue, 16 Sep 2025 19:28:05 GMT'
2025-09-16T19:28:06Z   [Information]   ✅ Container sftp-landing already exists
2025-09-16T19:28:06Z   [Information]   🚀 Starting Parquet blob upload...
2025-09-16T19:28:06Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'PUT'
Request headers:
    'Content-Length': '14625648'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '454ba2fa-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-09-16T19:28:07Z   [Information]   Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:06 GMT'
    'ETag': '"0x8DDF55729DCA6F8"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee3e01-e01e-0050-2940-2770a0000000'
    'x-ms-client-request-id': '454ba2fa-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:06 GMT'
2025-09-16T19:28:07Z   [Information]   📤 Upload result: {'etag': '"0x8DDF55729DCA6F8"', 'last_modified': datetime.datetime(2025, 9, 16, 19, 28, 6, tzinfo=datetime.timezone.utc), 'content_md5': bytearray(b'\x94!\xf9\xbb\xeb\x01\xd5m\x14\x96\x06y\x88\x9e\xb3\xb5'), 'client_request_id': '454ba2fa-9333-11f0-8771-00155dea3ad8', 'request_id': '3eee3e01-e01e-0050-2940-2770a0000000', 'version': '2025-07-05', 'version_id': None, 'date': datetime.datetime(2025, 9, 16, 19, 28, 6, tzinfo=datetime.timezone.utc), 'request_server_encrypted': True, 'encryption_key_sha256': None, 'encryption_scope': None, 'structured_body': None}
2025-09-16T19:28:07Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '45aaf26e-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:07Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '14625648'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:06 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF55729DCA6F8"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee3f66-e01e-0050-6840-2770a0000000'
    'x-ms-client-request-id': '45aaf26e-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:06 GMT'
2025-09-16T19:28:07Z   [Information]   ✅ Verification - Blob size: 14625648 bytes
2025-09-16T19:28:07Z   [Information]   🎉 SUCCESS: Uploaded Parquet: FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.CSV - Shape: (92008, 94)
2025-09-16T19:28:07Z   [Information]   ✓ Converted to Parquet: FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.CSV
2025-09-16T19:28:07Z   [Information]   🧹 Memory cleanup completed for file 1/1
2025-09-16T19:28:07Z   [Information]   ✅ Successfully converted 1/1 files to Parquet
2025-09-16T19:28:07Z   [Information]   ✅ STEP 1 COMPLETED: Converted 1 CSV files to individual Parquet files
2025-09-16T19:28:07Z   [Information]   📋 Individual Parquet files: ['FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.CSV']
2025-09-16T19:28:07Z   [Information]   🔄 Starting consolidation of individual Parquet files...
2025-09-16T19:28:07Z   [Information]   === MEMORY-EFFICIENT PARQUET CONSOLIDATION START ===
2025-09-16T19:28:07Z   [Information]   Parquet files found: 1
2025-09-16T19:28:07Z   [Information]   ✓ Single Parquet file: FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.CSV
2025-09-16T19:28:07Z   [Information]   Streaming Parquet from blob: FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.CSV
2025-09-16T19:28:07Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'GET'
Request headers:
    'x-ms-range': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '45b31480-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:07Z   [Information]   Response status: 206
Response headers:
    'Content-Length': '14625648'
    'Content-Type': 'application/octet-stream'
    'Content-Range': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:06 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF55729DCA6F8"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee3f86-e01e-0050-0740-2770a0000000'
    'x-ms-client-request-id': '45b31480-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-blob-content-md5': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:06 GMT'
2025-09-16T19:28:07Z   [Information]   ✓ Streamed Parquet: FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.CSV - Shape: (92008, 94)
2025-09-16T19:28:07Z   [Information]   ✓ Single file processed - Shape: (92008, 94)
2025-09-16T19:28:07Z   [Information]   ✅ CONSOLIDATION COMPLETED: Final DataFrame shape: (92008, 94)
2025-09-16T19:28:07Z   [Information]   STEP 2: ADD SURROGATE KEY (POLARS)
2025-09-16T19:28:07Z   [Information]   --------------------------------------------------
2025-09-16T19:28:07Z   [Information]   === ADDING SURROGATE KEY & COMPOSITE KEY (CONFIGURABLE) ===
2025-09-16T19:28:07Z   [Information]   Original DataFrame shape: (92008, 94)
2025-09-16T19:28:07Z   [Information]   All columns: ['RECID', 'SYS.STBR', 'DEAL-CATEG', 'NUO', 'DATE-FINALIZED-DR', 'DEAL-DATE', 'ACCTG-JRNL-DATE', 'DATE-REVERSED-DR', 'DEAL-START-DATE', 'CLOSE-DTE', 'STAT', 'CERT-USED-YN', 'COST-VEH', 'DEL-MIL-DV', 'L-RESIDUAL', 'NET-PFT', 'VEH-GRS', 'VIN', 'TRIM', 'STK-NO', 'DAYS-IN-STK', 'APR', 'CUST-NO', 'BIRTH-DATE4-DV', 'B-ADD', 'B-CITY', 'B-COUNTY', 'B-H-PHONE', 'B-LST-NME', 'B-MID-NAME', 'B-NAME', 'B-ZIP', 'B-ST', 'B-SEX', 'S1-NAME', 'S1-NUMBER', 'S2-NAME', 'S2-NUMBER', 'S3-NAME', 'S3-NUMBER', 'DM-NAME', 'DM-NUMBER', 'SM-NAME', 'SM-NUMBER', 'MSRP', 'AMT-FIN', 'BANK-NAME', 'BANK-ID', 'TFI-RES', 'TC-DOWN', 'DISCOUNT', 'GROS-PFT', 'FIN-RES', 'MAKE', 'CARLINE', 'VMS-LIST-PRICE', 'YR', 'LIST-PRICE-DR', 'ACV-TRADE1', 'ACV-TRADE2', 'ACV-TRADE3', 'NET-TRADE1', 'NET-TRADE2', 'NET-TRADE3', 'O-ALLOW1', 'O-ALLOW2', 'O-ALLOW3', 'PAYOFF1', 'PAYOFF2', 'PAYOFF3', 'TR1-BANK', 'TR1-COLOR', 'TR1-MAKE', 'TR1-MODEL', 'TR1-STK-NO', 'TR1-YEAR', 'TR2-BANK', 'TR2-COLOR', 'TR2-MAKE', 'TR2-MODEL', 'TR2-STK-NO', 'TR2-YEAR', 'TR3-BANK', 'TR3-COLOR', 'TR3-MAKE', 'TR3-MODEL', 'TR3-STK-NO', 'TR3-YEAR', 'EMAIL-ADDRESS-DV', 'OPT-OUT', 'JRN-GROSS-DV', 'GROSS-PRICE', 'UDF.TOT-PRICE', 'MOT-PWR']
2025-09-16T19:28:07Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:07Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:07Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:07Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:07Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:07Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:07Z   [Information]   Surrogate key configuration for job 'FIMASTSALES_':
2025-09-16T19:28:07Z   [Information]     - Columns: RECID,SYS.STBR,,
2025-09-16T19:28:07Z   [Information]     - Separator: '|'
2025-09-16T19:28:07Z   [Information]     - Hash method: MD5
2025-09-16T19:28:07Z   [Information]   Parsed columns: ['RECID', 'SYS.STBR', '', '']
2025-09-16T19:28:08Z   [Information]   Surrogate key logic: MD5 hash of 'RECID,SYS.STBR,,' with separator '|'
2025-09-16T19:28:08Z   [Information]   Composite key logic: Cleaned concatenated string (trailing empty values removed)
2025-09-16T19:28:08Z   [Information]   Sample surrogate key values (MD5): ['6a41d788d8b03fa6e0f99a7f2c481588', 'a44e07b8c8fe22474171b9668cf06656', '94043b3e5fe53353cd914d8f76084a6e']
2025-09-16T19:28:08Z   [Information]   Sample composite key values (cleaned): ['10000|01/01', '10001|01/01', '10002|01/01']
2025-09-16T19:28:08Z   [Information]   Sample full concatenated strings (for hashing): ['10000|01/01||', '10001|01/01||', '10002|01/01||']
2025-09-16T19:28:08Z   [Information]   ✓ Surrogate Key and Composite Key added - New shape: (92008, 96)
2025-09-16T19:28:08Z   [Information]   STEP 3: ADD AUDIT COLUMNS (POLARS)
2025-09-16T19:28:08Z   [Information]   --------------------------------------------------
2025-09-16T19:28:08Z   [Information]   ========================================
2025-09-16T19:28:08Z   [Information]   === APPENDING AUDIT COLUMNS START ===
2025-09-16T19:28:08Z   [Information]   ========================================
2025-09-16T19:28:08Z   [Information]   Original DataFrame shape: (92008, 96)
2025-09-16T19:28:08Z   [Information]   Original columns (96): ['Surrogate Key', 'Composite Key', 'RECID', 'SYS.STBR', 'DEAL-CATEG', 'NUO', 'DATE-FINALIZED-DR', 'DEAL-DATE', 'ACCTG-JRNL-DATE', 'DATE-REVERSED-DR', 'DEAL-START-DATE', 'CLOSE-DTE', 'STAT', 'CERT-USED-YN', 'COST-VEH', 'DEL-MIL-DV', 'L-RESIDUAL', 'NET-PFT', 'VEH-GRS', 'VIN', 'TRIM', 'STK-NO', 'DAYS-IN-STK', 'APR', 'CUST-NO', 'BIRTH-DATE4-DV', 'B-ADD', 'B-CITY', 'B-COUNTY', 'B-H-PHONE', 'B-LST-NME', 'B-MID-NAME', 'B-NAME', 'B-ZIP', 'B-ST', 'B-SEX', 'S1-NAME', 'S1-NUMBER', 'S2-NAME', 'S2-NUMBER', 'S3-NAME', 'S3-NUMBER', 'DM-NAME', 'DM-NUMBER', 'SM-NAME', 'SM-NUMBER', 'MSRP', 'AMT-FIN', 'BANK-NAME', 'BANK-ID', 'TFI-RES', 'TC-DOWN', 'DISCOUNT', 'GROS-PFT', 'FIN-RES', 'MAKE', 'CARLINE', 'VMS-LIST-PRICE', 'YR', 'LIST-PRICE-DR', 'ACV-TRADE1', 'ACV-TRADE2', 'ACV-TRADE3', 'NET-TRADE1', 'NET-TRADE2', 'NET-TRADE3', 'O-ALLOW1', 'O-ALLOW2', 'O-ALLOW3', 'PAYOFF1', 'PAYOFF2', 'PAYOFF3', 'TR1-BANK', 'TR1-COLOR', 'TR1-MAKE', 'TR1-MODEL', 'TR1-STK-NO', 'TR1-YEAR', 'TR2-BANK', 'TR2-COLOR', 'TR2-MAKE', 'TR2-MODEL', 'TR2-STK-NO', 'TR2-YEAR', 'TR3-BANK', 'TR3-COLOR', 'TR3-MAKE', 'TR3-MODEL', 'TR3-STK-NO', 'TR3-YEAR', 'EMAIL-ADDRESS-DV', 'OPT-OUT', 'JRN-GROSS-DV', 'GROSS-PRICE', 'UDF.TOT-PRICE', 'MOT-PWR']
2025-09-16T19:28:08Z   [Information]   Current timestamp: 2025-09-16 19:28:07
2025-09-16T19:28:08Z   [Information]   Source filenames: ['FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV']
2025-09-16T19:28:08Z   [Information]     Extracting file info from: FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV
2025-09-16T19:28:08Z   [Information]     Pattern 1 matched: ('FIMAST-SALES/fimastsales-decompressed/FIMASTSALES', '08', '20', '25', '06', '31', '00')
2025-09-16T19:28:08Z   [Information]     Successfully extracted datetime: 08/20/2025 06:31
2025-09-16T19:28:08Z   [Information]   Extracted and parsed source date from filename: 08/20/2025 06:31
2025-09-16T19:28:08Z   [Information]   ✓ Added audit column: 'Source Date/Time'
2025-09-16T19:28:08Z   [Information]   ✓ Added audit column: 'Created Date/Time'
2025-09-16T19:28:08Z   [Information]   ✓ Added audit column: 'Modified Date/Time'
2025-09-16T19:28:08Z   [Information]   New DataFrame shape: (92008, 99)
2025-09-16T19:28:08Z   [Information]   New columns (99): ['Surrogate Key', 'Composite Key', 'RECID', 'SYS.STBR', 'DEAL-CATEG', 'NUO', 'DATE-FINALIZED-DR', 'DEAL-DATE', 'ACCTG-JRNL-DATE', 'DATE-REVERSED-DR', 'DEAL-START-DATE', 'CLOSE-DTE', 'STAT', 'CERT-USED-YN', 'COST-VEH', 'DEL-MIL-DV', 'L-RESIDUAL', 'NET-PFT', 'VEH-GRS', 'VIN', 'TRIM', 'STK-NO', 'DAYS-IN-STK', 'APR', 'CUST-NO', 'BIRTH-DATE4-DV', 'B-ADD', 'B-CITY', 'B-COUNTY', 'B-H-PHONE', 'B-LST-NME', 'B-MID-NAME', 'B-NAME', 'B-ZIP', 'B-ST', 'B-SEX', 'S1-NAME', 'S1-NUMBER', 'S2-NAME', 'S2-NUMBER', 'S3-NAME', 'S3-NUMBER', 'DM-NAME', 'DM-NUMBER', 'SM-NAME', 'SM-NUMBER', 'MSRP', 'AMT-FIN', 'BANK-NAME', 'BANK-ID', 'TFI-RES', 'TC-DOWN', 'DISCOUNT', 'GROS-PFT', 'FIN-RES', 'MAKE', 'CARLINE', 'VMS-LIST-PRICE', 'YR', 'LIST-PRICE-DR', 'ACV-TRADE1', 'ACV-TRADE2', 'ACV-TRADE3', 'NET-TRADE1', 'NET-TRADE2', 'NET-TRADE3', 'O-ALLOW1', 'O-ALLOW2', 'O-ALLOW3', 'PAYOFF1', 'PAYOFF2', 'PAYOFF3', 'TR1-BANK', 'TR1-COLOR', 'TR1-MAKE', 'TR1-MODEL', 'TR1-STK-NO', 'TR1-YEAR', 'TR2-BANK', 'TR2-COLOR', 'TR2-MAKE', 'TR2-MODEL', 'TR2-STK-NO', 'TR2-YEAR', 'TR3-BANK', 'TR3-COLOR', 'TR3-MAKE', 'TR3-MODEL', 'TR3-STK-NO', 'TR3-YEAR', 'EMAIL-ADDRESS-DV', 'OPT-OUT', 'JRN-GROSS-DV', 'GROSS-PRICE', 'UDF.TOT-PRICE', 'MOT-PWR', 'Source Date/Time', 'Created Date/Time', 'Modified Date/Time']
2025-09-16T19:28:08Z   [Information]   ✓ VERIFICATION PASSED: Audit columns are the last three columns
2025-09-16T19:28:08Z   [Information]   === APPENDING AUDIT COLUMNS END ===
2025-09-16T19:28:08Z   [Information]   ========================================
2025-09-16T19:28:08Z   [Information]   STEP 4: UPLOAD AS PARQUET AND CSV FILES
2025-09-16T19:28:08Z   [Information]   --------------------------------------------------
2025-09-16T19:28:08Z   [Information]   📄 Single file detected: Using original filename 'FIMASTSALES_08-20-25_06.31.00'
2025-09-16T19:28:08Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:08Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:08Z   [Information]   🔄 Starting Parquet upload - Blob: FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.parquet
2025-09-16T19:28:08Z   [Information]   📊 DataFrame shape: (92008, 99)
2025-09-16T19:28:08Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:08Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:08Z   [Information]   Ensuring destination folder exists: FIMAST-SALES/fimastsales-enhanced
2025-09-16T19:28:08Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED&prefix=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4618396e-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:08Z   [Information]   Response status: 200
Response headers:
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee4116-e01e-0050-7840-2770a0000000'
    'x-ms-client-request-id': '4618396e-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:06 GMT'
2025-09-16T19:28:08Z   [Information]   ✓ Destination folder already exists: FIMAST-SALES/fimastsales-enhanced
2025-09-16T19:28:08Z   [Information]   📝 Parquet size: 18061598 bytes
2025-09-16T19:28:08Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED'
Request method: 'PUT'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '464fbe70-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:08Z   [Information]   Response status: 409
Response headers:
    'Content-Length': '230'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee41ed-e01e-0050-3c40-2770a0000000'
    'x-ms-client-request-id': '464fbe70-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-error-code': 'ContainerAlreadyExists'
    'Date': 'Tue, 16 Sep 2025 19:28:07 GMT'
2025-09-16T19:28:08Z   [Information]   ✅ Container sftp-landing already exists
2025-09-16T19:28:08Z   [Information]   🚀 Starting Parquet blob upload...
2025-09-16T19:28:08Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.parquet'
Request method: 'PUT'
Request headers:
    'Content-Length': '18061598'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '465604ec-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-09-16T19:28:11Z   [Information]   Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:11 GMT'
    'ETag': '"0x8DDF5572C644368"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee420a-e01e-0050-5840-2770a0000000'
    'x-ms-client-request-id': '465604ec-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:10 GMT'
2025-09-16T19:28:11Z   [Information]   📤 Upload result: {'etag': '"0x8DDF5572C644368"', 'last_modified': datetime.datetime(2025, 9, 16, 19, 28, 11, tzinfo=datetime.timezone.utc), 'content_md5': bytearray(b'^2CL\xec\x15\xf3\xb0l`\xc7\x14\x83\xec\xcd@'), 'client_request_id': '465604ec-9333-11f0-8771-00155dea3ad8', 'request_id': '3eee420a-e01e-0050-5840-2770a0000000', 'version': '2025-07-05', 'version_id': None, 'date': datetime.datetime(2025, 9, 16, 19, 28, 10, tzinfo=datetime.timezone.utc), 'request_server_encrypted': True, 'encryption_key_sha256': None, 'encryption_scope': None, 'structured_body': None}
2025-09-16T19:28:11Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.parquet'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4832582e-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:11Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '18061598'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:11 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF5572C644368"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee4aa1-e01e-0050-0d40-2770a0000000'
    'x-ms-client-request-id': '4832582e-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:10 GMT'
2025-09-16T19:28:11Z   [Information]   ✅ Verification - Blob size: 18061598 bytes
2025-09-16T19:28:11Z   [Information]   🎉 SUCCESS: Uploaded Parquet: FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.parquet - Shape: (92008, 99)
2025-09-16T19:28:11Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:11Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:11Z   [Information]   🔄 Starting CSV upload - Blob: FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.csv, Container: sftp-landing
2025-09-16T19:28:11Z   [Information]   📊 DataFrame shape: (92008, 99)
2025-09-16T19:28:11Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:11Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:11Z   [Information]   Ensuring destination folder exists: FIMAST-SALES/fimastsales-enhanced
2025-09-16T19:28:11Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED&prefix=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '48372f98-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:11Z   [Information]   Response status: 200
Response headers:
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee4ab6-e01e-0050-2040-2770a0000000'
    'x-ms-client-request-id': '48372f98-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:10 GMT'
2025-09-16T19:28:11Z   [Information]   ✓ Destination folder already exists: FIMAST-SALES/fimastsales-enhanced
2025-09-16T19:28:12Z   [Information]   📝 CSV size: 51332446 bytes
2025-09-16T19:28:12Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED'
Request method: 'PUT'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '487897bc-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:12Z   [Information]   Response status: 409
Response headers:
    'Content-Length': '230'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee4ba1-e01e-0050-7940-2770a0000000'
    'x-ms-client-request-id': '487897bc-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-error-code': 'ContainerAlreadyExists'
    'Date': 'Tue, 16 Sep 2025 19:28:10 GMT'
2025-09-16T19:28:12Z   [Information]   ✅ Container sftp-landing already exists
2025-09-16T19:28:12Z   [Information]   🚀 Starting blob upload...
2025-09-16T19:28:12Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.csv'
Request method: 'PUT'
Request headers:
    'Content-Length': '51332446'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '487ee11c-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-09-16T19:28:21Z   [Information]   Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:20 GMT'
    'ETag': '"0x8DDF55731FDD35C"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee4baf-e01e-0050-0740-2770a0000000'
    'x-ms-client-request-id': '487ee11c-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:19 GMT'
2025-09-16T19:28:21Z   [Information]   📤 Upload result: {'etag': '"0x8DDF55731FDD35C"', 'last_modified': datetime.datetime(2025, 9, 16, 19, 28, 20, tzinfo=datetime.timezone.utc), 'content_md5': bytearray(b'\x8b\xa5\xfd\xaby\xe0{[>\x115\xa6\xd9+\x99\x88'), 'client_request_id': '487ee11c-9333-11f0-8771-00155dea3ad8', 'request_id': '3eee4baf-e01e-0050-0740-2770a0000000', 'version': '2025-07-05', 'version_id': None, 'date': datetime.datetime(2025, 9, 16, 19, 28, 19, tzinfo=datetime.timezone.utc), 'request_server_encrypted': True, 'encryption_key_sha256': None, 'encryption_scope': None, 'structured_body': None}
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.csv'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4dcbe85e-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '51332446'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:20 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF55731FDD35C"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee64e0-e01e-0050-6140-2770a0000000'
    'x-ms-client-request-id': '4dcbe85e-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:19 GMT'
2025-09-16T19:28:21Z   [Information]   ✅ Verification - Blob size: 51332446 bytes
2025-09-16T19:28:21Z   [Information]   🎉 SUCCESS: Uploaded CSV: FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_08-20-25_06.31.00.csv - Shape: (92008, 99)
2025-09-16T19:28:21Z   [Information]   STEP 5: CLEANUP AND ZIP MOVEMENT
2025-09-16T19:28:21Z   [Information]   --------------------------------------------------
2025-09-16T19:28:21Z   [Information]   Cleaning up .placeholder files...
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4dd0da9e-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 200
Response headers:
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee64f6-e01e-0050-7740-2770a0000000'
    'x-ms-client-request-id': '4dd0da9e-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:19 GMT'
2025-09-16T19:28:21Z   [Information]   Attempting to delete blob file: FIMAST-SALES/fimastsales-enhanced/.placeholder
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-enhanced/.placeholder'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4ddd9db0-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '0'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:05 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF5572938BC10"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee652d-e01e-0050-2e40-2770a0000000'
    'x-ms-client-request-id': '4ddd9db0-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:19 GMT'
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-enhanced/.placeholder'
Request method: 'DELETE'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4de23532-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 202
Response headers:
    'Content-Length': '0'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee653d-e01e-0050-3e40-2770a0000000'
    'x-ms-client-request-id': '4de23532-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-delete-type-permanent': 'REDACTED'
    'x-ms-deletion-id': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:21Z   [Information]   ✓ Successfully deleted blob file: FIMAST-SALES/fimastsales-enhanced/.placeholder
2025-09-16T19:28:21Z   [Information]   ✓ Cleaned up 1 .placeholder file(s)
2025-09-16T19:28:21Z   [Information]   ℹ️ Single file processing - no intermediate parquet cleanup needed
2025-09-16T19:28:21Z   [Information]   CLEANUP: Removing successfully processed source CSV files...
2025-09-16T19:28:21Z   [Information]   Files to delete: 1
2025-09-16T19:28:21Z   [Information]   Deleting source file 1/1: FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV
2025-09-16T19:28:21Z   [Information]   Attempting to delete blob file: FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4dee1a14-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '58389814'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:42 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3C35DA54"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee6578-e01e-0050-7040-2770a0000000'
    'x-ms-client-request-id': '4dee1a14-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV'
Request method: 'DELETE'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4df2e56c-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 202
Response headers:
    'Content-Length': '0'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee6590-e01e-0050-0640-2770a0000000'
    'x-ms-client-request-id': '4df2e56c-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-delete-type-permanent': 'REDACTED'
    'x-ms-deletion-id': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:21Z   [Information]   ✓ Successfully deleted blob file: FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV
2025-09-16T19:28:21Z   [Information]     ✓ Successfully deleted: FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV
2025-09-16T19:28:21Z   [Information]   ==================================================
2025-09-16T19:28:21Z   [Information]   SOURCE FILE CLEANUP SUMMARY:
2025-09-16T19:28:21Z   [Information]     Total files to delete: 1
2025-09-16T19:28:21Z   [Information]     Successfully deleted: 1
2025-09-16T19:28:21Z   [Information]     Failed deletions: 0
2025-09-16T19:28:21Z   [Information]     ✓ Deleted files: ['FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_08-20-25_06.31.00.CSV']
2025-09-16T19:28:21Z   [Information]   ==================================================
2025-09-16T19:28:21Z   [Information]   ============================================================
2025-09-16T19:28:21Z   [Information]   === DEALER-AWARE ZIP FILE ARCHIVE MOVEMENT START ===
2025-09-16T19:28:21Z   [Information]   ============================================================
2025-09-16T19:28:21Z   [Information]   Job key: FIMASTSALES_
2025-09-16T19:28:21Z   [Information]   Container: sftp-landing
2025-09-16T19:28:21Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:21Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:21Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:21Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:21Z   [Information]   📦 STANDARD ZIP ARCHIVING: Processing non-dealer zip files
2025-09-16T19:28:21Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:21Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:21Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:21Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:21Z   [Information]   Could not parse JSON in ValidationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\required_columns\:[\column1\,\column2\]}
2025-09-16T19:28:21Z   [Information]   Could not parse JSON in TransformationRules for job FIMASTSALES_. Using empty configuration. Raw value: {\add_audit_columns\:true}
2025-09-16T19:28:21Z   [Information]   ============================================================
2025-09-16T19:28:21Z   [Information]   === ZIP FILE ARCHIVE MOVEMENT START ===
2025-09-16T19:28:21Z   [Information]   ============================================================
2025-09-16T19:28:21Z   [Information]   Moving zip files from FIMAST-SALES/fimastsales-compressed to FIMAST-SALES/archive
2025-09-16T19:28:21Z   [Information]   Container: sftp-landing
2025-09-16T19:28:21Z   [Information]   File pattern: FIMASTSALES*.zip
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED&prefix=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4dfdc414-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 200
Response headers:
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee65cd-e01e-0050-3d40-2770a0000000'
    'x-ms-client-request-id': '4dfdc414-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:21Z   [Information]   Found 2 zip files to move:
2025-09-16T19:28:21Z   [Information]     - FIMAST-SALES/fimastsales-compressed/FIMASTSALES_08-20-25_06.31.00.CSV.zip
2025-09-16T19:28:21Z   [Information]     - FIMAST-SALES/fimastsales-compressed/FIMASTSALES_2_08-20-25_06.35.45.csv.zip
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED&prefix=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e06f2e6-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Warning]   Could not verify/create archive folder: Session.request() got an unexpected keyword argument 'max_results'
2025-09-16T19:28:21Z   [Information]   Moving file 1/2: FIMASTSALES_08-20-25_06.31.00.CSV.zip
2025-09-16T19:28:21Z   [Information]     Source: FIMAST-SALES/fimastsales-compressed/FIMASTSALES_08-20-25_06.31.00.CSV.zip
2025-09-16T19:28:21Z   [Information]     Destination: FIMAST-SALES/archive/FIMASTSALES_08-20-25_06.31.00.CSV.zip
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-compressed/FIMASTSALES_08-20-25_06.31.00.CSV.zip'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e07171c-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '13137917'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:41 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3B265910"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee65f3-e01e-0050-6040-2770a0000000'
    'x-ms-client-request-id': '4e07171c-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/archive/FIMASTSALES_08-20-25_06.31.00.CSV.zip'
Request method: 'PUT'
Request headers:
    'x-ms-copy-source': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e0bf642-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 202
Response headers:
    'Content-Length': '0'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:21 GMT'
    'ETag': '"0x8DDF55732559F3C"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee660b-e01e-0050-7840-2770a0000000'
    'x-ms-client-request-id': '4e0bf642-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-copy-id': 'REDACTED'
    'x-ms-copy-status': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/archive/FIMASTSALES_08-20-25_06.31.00.CSV.zip'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e23f18e-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '13137917'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:21 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF55732559F3C"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee66a3-e01e-0050-0640-2770a0000000'
    'x-ms-client-request-id': '4e23f18e-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-copy-id': 'REDACTED'
    'x-ms-copy-source': 'REDACTED'
    'x-ms-copy-status': 'REDACTED'
    'x-ms-copy-progress': 'REDACTED'
    'x-ms-copy-completion-time': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-compressed/FIMASTSALES_08-20-25_06.31.00.CSV.zip'
Request method: 'DELETE'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e28ad0a-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 202
Response headers:
    'Content-Length': '0'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee66ba-e01e-0050-1b40-2770a0000000'
    'x-ms-client-request-id': '4e28ad0a-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-delete-type-permanent': 'REDACTED'
    'x-ms-deletion-id': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:21Z   [Information]     ✓ Successfully moved: FIMASTSALES_08-20-25_06.31.00.CSV.zip
2025-09-16T19:28:21Z   [Information]   Moving file 2/2: FIMASTSALES_2_08-20-25_06.35.45.csv.zip
2025-09-16T19:28:21Z   [Information]     Source: FIMAST-SALES/fimastsales-compressed/FIMASTSALES_2_08-20-25_06.35.45.csv.zip
2025-09-16T19:28:21Z   [Information]     Destination: FIMAST-SALES/archive/FIMASTSALES_2_08-20-25_06.35.45.csv.zip
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-compressed/FIMASTSALES_2_08-20-25_06.35.45.csv.zip'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e347202-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '1591304'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:41 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3B3273F0"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee66ef-e01e-0050-4b40-2770a0000000'
    'x-ms-client-request-id': '4e347202-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/archive/FIMASTSALES_2_08-20-25_06.35.45.csv.zip'
Request method: 'PUT'
Request headers:
    'x-ms-copy-source': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e394ff2-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 202
Response headers:
    'Content-Length': '0'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:21 GMT'
    'ETag': '"0x8DDF5573277369C"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee6701-e01e-0050-5c40-2770a0000000'
    'x-ms-client-request-id': '4e394ff2-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-copy-id': 'REDACTED'
    'x-ms-copy-status': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/archive/FIMASTSALES_2_08-20-25_06.35.45.csv.zip'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e45626a-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:21Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '1591304'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:21 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF5573277369C"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee6731-e01e-0050-0940-2770a0000000'
    'x-ms-client-request-id': '4e45626a-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-copy-id': 'REDACTED'
    'x-ms-copy-source': 'REDACTED'
    'x-ms-copy-status': 'REDACTED'
    'x-ms-copy-progress': 'REDACTED'
    'x-ms-copy-completion-time': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:21Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/fimastsales-compressed/FIMASTSALES_2_08-20-25_06.35.45.csv.zip'
Request method: 'DELETE'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e4a37a4-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 202
Response headers:
    'Content-Length': '0'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee6746-e01e-0050-1d40-2770a0000000'
    'x-ms-client-request-id': '4e4a37a4-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-delete-type-permanent': 'REDACTED'
    'x-ms-deletion-id': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:22Z   [Information]     ✓ Successfully moved: FIMASTSALES_2_08-20-25_06.35.45.csv.zip
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/FIMAST-SALES/archive/.placeholder'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e560bf6-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 404
Response headers:
    'Transfer-Encoding': 'chunked'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee6774-e01e-0050-4840-2770a0000000'
    'x-ms-client-request-id': '4e560bf6-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-error-code': 'BlobNotFound'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:22Z   [Information]   ============================================================
2025-09-16T19:28:22Z   [Information]   === ZIP FILE ARCHIVE MOVEMENT SUMMARY ===
2025-09-16T19:28:22Z   [Information]   Total files found: 2
2025-09-16T19:28:22Z   [Information]   Successfully moved: 2
2025-09-16T19:28:22Z   [Information]   Failed movements: 0
2025-09-16T19:28:22Z   [Information]   Successfully moved files:
2025-09-16T19:28:22Z   [Information]     ✓ FIMASTSALES_08-20-25_06.31.00.CSV.zip
2025-09-16T19:28:22Z   [Information]     ✓ FIMASTSALES_2_08-20-25_06.35.45.csv.zip
2025-09-16T19:28:22Z   [Information]   === ZIP FILE ARCHIVE MOVEMENT END ===
2025-09-16T19:28:22Z   [Information]   ============================================================
2025-09-16T19:28:22Z   [Information]   📦 Zip archiving completed for job FIMASTSALES_: 2 files moved
2025-09-16T19:28:22Z   [Information]   🎉 CSV-to-Parquet pipeline completed successfully!
2025-09-16T19:28:22Z   [Information]   📊 Final dataset: 92008 rows × 99 columns
2025-09-16T19:28:22Z   [Information]   ✅ Job FIMASTSALES_ completed successfully
2025-09-16T19:28:22Z   [Information]   🔄 Processing job: RO_TIME
2025-09-16T19:28:22Z   [Information]   📁 Job RO_TIME configuration:
2025-09-16T19:28:22Z   [Information]     - Source: sftp-landing/RO_Time
2025-09-16T19:28:22Z   [Information]     - Output: sftp-landing/RO_Time
2025-09-16T19:28:22Z   [Information]     - File prefix: RO_TIME
2025-09-16T19:28:22Z   [Information]   🔄 Processing single job: RO_TIME
2025-09-16T19:28:22Z   [Information]   📂 Source: sftp-landing/RO_Time
2025-09-16T19:28:22Z   [Information]   📤 Output: sftp-landing/RO_Time
2025-09-16T19:28:22Z   [Information]   🔍 File patterns: ['RO_TIME']
2025-09-16T19:28:22Z   [Information]   Listing Parquet files in container: sftp-landing, folder: RO_Time
2025-09-16T19:28:22Z   [Information]   Listing Parquet files in container: sftp-landing, folder: RO_Time
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED&prefix=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e5b0f3e-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 200
Response headers:
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee678c-e01e-0050-5f40-2770a0000000'
    'x-ms-client-request-id': '4e5b0f3e-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:22Z   [Information]   Total Parquet files found: 0
2025-09-16T19:28:22Z   [Information]   Listing CSV files in container: sftp-landing, folder: RO_Time
2025-09-16T19:28:22Z   [Information]   Listing CSV files in container: sftp-landing, folder: RO_Time
2025-09-16T19:28:22Z   [Information]   Job key: RO_TIME, Dealer prefix enabled: True
2025-09-16T19:28:22Z   [Information]   Using dealer prefix structure for job: RO_TIME
2025-09-16T19:28:22Z   [Information]   Base folder for dealer search: RO_Time
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED&prefix=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e6547a6-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 200
Response headers:
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee67bc-e01e-0050-0540-2770a0000000'
    'x-ms-client-request-id': '4e6547a6-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:22Z   [Information]   Found 1 dealer folders: ['AO_RO_TIME']
2025-09-16T19:28:22Z   [Information]   Searching in dealer path: RO_Time/AO_RO_TIME/ao_ro_time-decompressed
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED&prefix=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e6e8da2-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 200
Response headers:
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee67dd-e01e-0050-2240-2770a0000000'
    'x-ms-client-request-id': '4e6e8da2-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:22Z   [Information]   ✓ Found matching dealer CSV file: RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv
2025-09-16T19:28:22Z   [Information]   Total CSV files found: 1
2025-09-16T19:28:22Z   [Information]   ✓ Found 1 CSV files, using hybrid CSV-to-Parquet pipeline
2025-09-16T19:28:22Z   [Information]   🚀 Starting CSV-to-Parquet Hybrid Pipeline...
2025-09-16T19:28:22Z   [Information]   ================================================================================
2025-09-16T19:28:22Z   [Information]   🏢 DEALER-AWARE PROCESSING: Processing each dealer file individually
2025-09-16T19:28:22Z   [Information]   ============================================================
2025-09-16T19:28:22Z   [Information]   🔄 Processing individual dealer file: AO_RO_TIME_08-20-25_22.40.35.csv
2025-09-16T19:28:22Z   [Information]   Downloading CSV from blob: RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e73ba52-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '119769'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:39 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3A760F68"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee67ef-e01e-0050-2f40-2770a0000000'
    'x-ms-client-request-id': '4e73ba52-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:22Z   [Information]   Blob size: 0.11 MB
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv'
Request method: 'GET'
Request headers:
    'x-ms-range': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e78b58e-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 206
Response headers:
    'Content-Length': '119769'
    'Content-Type': 'application/octet-stream'
    'Content-Range': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:39 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3A760F68"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee67fd-e01e-0050-3b40-2770a0000000'
    'x-ms-client-request-id': '4e78b58e-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-blob-content-md5': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:20 GMT'
2025-09-16T19:28:22Z   [Information]   ✓ Downloaded CSV: RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv - Shape: (844, 17)
2025-09-16T19:28:22Z   [Information]   === ADDING SURROGATE KEY & COMPOSITE KEY (CONFIGURABLE) ===
2025-09-16T19:28:22Z   [Information]   Original DataFrame shape: (844, 17)
2025-09-16T19:28:22Z   [Information]   All columns: ['Tech#', 'Tech Name', 'Date', 'Department', 'RO#', 'Operation Code', 'Start Time', 'Finish Time', 'Reason', 'Actual Time', 'Booked Time', 'ST', 'Rate', 'Extended', 'Job', 'Bill Type', 'Prior Date Flag']
2025-09-16T19:28:22Z   [Information]   Surrogate key configuration for job 'RO_TIME':
2025-09-16T19:28:22Z   [Information]     - Columns: RO#,Operation Code,,
2025-09-16T19:28:22Z   [Information]     - Separator: '|'
2025-09-16T19:28:22Z   [Information]     - Hash method: MD5
2025-09-16T19:28:22Z   [Information]   Parsed columns: ['RO#', 'Operation Code', '', '']
2025-09-16T19:28:22Z   [Information]   Surrogate key logic: MD5 hash of 'RO#,Operation Code,,' with separator '|'
2025-09-16T19:28:22Z   [Information]   Composite key logic: Cleaned concatenated string (trailing empty values removed)
2025-09-16T19:28:22Z   [Information]   Sample surrogate key values (MD5): ['e5e841bd37ef16edb6161d51c384404b', '7f508610ca346463faddd24db355d64f', 'd7faec88aaada30e0077b6048a2349b9']
2025-09-16T19:28:22Z   [Information]   Sample composite key values (cleaned): ['1171141|86AOZ-MXSHLD', '1174667|80AOZ-10', '1178376|80AOZ-10']
2025-09-16T19:28:22Z   [Information]   Sample full concatenated strings (for hashing): ['1171141|86AOZ-MXSHLD||', '1174667|80AOZ-10||', '1178376|80AOZ-10||']
2025-09-16T19:28:22Z   [Information]   ✓ Surrogate Key and Composite Key added - New shape: (844, 19)
2025-09-16T19:28:22Z   [Information]   ========================================
2025-09-16T19:28:22Z   [Information]   === APPENDING AUDIT COLUMNS START ===
2025-09-16T19:28:22Z   [Information]   ========================================
2025-09-16T19:28:22Z   [Information]   Original DataFrame shape: (844, 19)
2025-09-16T19:28:22Z   [Information]   Original columns (19): ['Surrogate Key', 'Composite Key', 'Tech#', 'Tech Name', 'Date', 'Department', 'RO#', 'Operation Code', 'Start Time', 'Finish Time', 'Reason', 'Actual Time', 'Booked Time', 'ST', 'Rate', 'Extended', 'Job', 'Bill Type', 'Prior Date Flag']
2025-09-16T19:28:22Z   [Information]   Current timestamp: 2025-09-16 19:28:21
2025-09-16T19:28:22Z   [Information]   Source filenames: ['RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv']
2025-09-16T19:28:22Z   [Information]     Extracting file info from: RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv
2025-09-16T19:28:22Z   [Information]     Pattern 1 matched: ('RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME', '08', '20', '25', '22', '40', '35')
2025-09-16T19:28:22Z   [Information]     Successfully extracted datetime: 08/20/2025 22:40
2025-09-16T19:28:22Z   [Information]   Extracted and parsed source date from filename: 08/20/2025 22:40
2025-09-16T19:28:22Z   [Information]   ✓ Added audit column: 'Source Date/Time'
2025-09-16T19:28:22Z   [Information]   ✓ Added audit column: 'Created Date/Time'
2025-09-16T19:28:22Z   [Information]   ✓ Added audit column: 'Modified Date/Time'
2025-09-16T19:28:22Z   [Information]   New DataFrame shape: (844, 22)
2025-09-16T19:28:22Z   [Information]   New columns (22): ['Surrogate Key', 'Composite Key', 'Tech#', 'Tech Name', 'Date', 'Department', 'RO#', 'Operation Code', 'Start Time', 'Finish Time', 'Reason', 'Actual Time', 'Booked Time', 'ST', 'Rate', 'Extended', 'Job', 'Bill Type', 'Prior Date Flag', 'Source Date/Time', 'Created Date/Time', 'Modified Date/Time']
2025-09-16T19:28:22Z   [Information]   ✓ VERIFICATION PASSED: Audit columns are the last three columns
2025-09-16T19:28:22Z   [Information]   === APPENDING AUDIT COLUMNS END ===
2025-09-16T19:28:22Z   [Information]   ========================================
2025-09-16T19:28:22Z   [Information]   🔄 Starting Parquet upload - Blob: RO_Time/AO_RO_TIME/ao_ro_time-enhanced/AO_RO_TIME_08-20-25_22.40.35.parquet
2025-09-16T19:28:22Z   [Information]   📊 DataFrame shape: (844, 22)
2025-09-16T19:28:22Z   [Information]   Ensuring destination folder exists: RO_Time/enhanced
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED&prefix=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e8ed684-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 200
Response headers:
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee6856-e01e-0050-0840-2770a0000000'
    'x-ms-client-request-id': '4e8ed684-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:21 GMT'
2025-09-16T19:28:22Z   [Information]   Creating destination folder structure with placeholder: RO_Time/enhanced/.placeholder
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/RO_Time/enhanced/.placeholder'
Request method: 'PUT'
Request headers:
    'x-ms-blob-type': 'REDACTED'
    'Content-Length': '0'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e93e822-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:22 GMT'
    'ETag': '"0x8DDF55732D16844"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee6875-e01e-0050-2340-2770a0000000'
    'x-ms-client-request-id': '4e93e822-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:21 GMT'
2025-09-16T19:28:22Z   [Information]   ✓ Destination folder created: RO_Time/enhanced
2025-09-16T19:28:22Z   [Information]   📝 Parquet size: 54742 bytes
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED'
Request method: 'PUT'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4e9fdc22-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 409
Response headers:
    'Content-Length': '230'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee68a0-e01e-0050-4d40-2770a0000000'
    'x-ms-client-request-id': '4e9fdc22-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-error-code': 'ContainerAlreadyExists'
    'Date': 'Tue, 16 Sep 2025 19:28:21 GMT'
2025-09-16T19:28:22Z   [Information]   ✅ Container sftp-landing already exists
2025-09-16T19:28:22Z   [Information]   🚀 Starting Parquet blob upload...
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/RO_Time/AO_RO_TIME/ao_ro_time-enhanced/AO_RO_TIME_08-20-25_22.40.35.parquet'
Request method: 'PUT'
Request headers:
    'Content-Length': '54742'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4ea5ad96-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-09-16T19:28:22Z   [Information]   Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:22 GMT'
    'ETag': '"0x8DDF55732E3CC60"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee68bd-e01e-0050-6940-2770a0000000'
    'x-ms-client-request-id': '4ea5ad96-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:21 GMT'
2025-09-16T19:28:22Z   [Information]   📤 Upload result: {'etag': '"0x8DDF55732E3CC60"', 'last_modified': datetime.datetime(2025, 9, 16, 19, 28, 22, tzinfo=datetime.timezone.utc), 'content_md5': bytearray(b'\xbd\x83\xc2\r{\xb6\x90#\xba\xc1\xf9Bas7\xac'), 'client_request_id': '4ea5ad96-9333-11f0-8771-00155dea3ad8', 'request_id': '3eee68bd-e01e-0050-6940-2770a0000000', 'version': '2025-07-05', 'version_id': None, 'date': datetime.datetime(2025, 9, 16, 19, 28, 21, tzinfo=datetime.timezone.utc), 'request_server_encrypted': True, 'encryption_key_sha256': None, 'encryption_scope': None, 'structured_body': None}
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/RO_Time/AO_RO_TIME/ao_ro_time-enhanced/AO_RO_TIME_08-20-25_22.40.35.parquet'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4eb1fc2c-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '54742'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:22 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF55732E3CC60"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee68f3-e01e-0050-1940-2770a0000000'
    'x-ms-client-request-id': '4eb1fc2c-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:21 GMT'
2025-09-16T19:28:22Z   [Information]   ✅ Verification - Blob size: 54742 bytes
2025-09-16T19:28:22Z   [Information]   🎉 SUCCESS: Uploaded Parquet: RO_Time/AO_RO_TIME/ao_ro_time-enhanced/AO_RO_TIME_08-20-25_22.40.35.parquet - Shape: (844, 22)
2025-09-16T19:28:22Z   [Information]   🔄 Starting CSV upload - Blob: RO_Time/AO_RO_TIME/ao_ro_time-enhanced/AO_RO_TIME_08-20-25_22.40.35.csv, Container: sftp-landing
2025-09-16T19:28:22Z   [Information]   📊 DataFrame shape: (844, 22)
2025-09-16T19:28:22Z   [Information]   Ensuring destination folder exists: RO_Time/enhanced
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED&comp=REDACTED&prefix=REDACTED'
Request method: 'GET'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4eb6ef98-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 200
Response headers:
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee6917-e01e-0050-3a40-2770a0000000'
    'x-ms-client-request-id': '4eb6ef98-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:21 GMT'
2025-09-16T19:28:22Z   [Information]   ✓ Destination folder already exists: RO_Time/enhanced
2025-09-16T19:28:22Z   [Information]   📝 CSV size: 175465 bytes
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing?restype=REDACTED'
Request method: 'PUT'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4ebc4c5e-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 409
Response headers:
    'Content-Length': '230'
    'Content-Type': 'application/xml'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee693e-e01e-0050-5e40-2770a0000000'
    'x-ms-client-request-id': '4ebc4c5e-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-error-code': 'ContainerAlreadyExists'
    'Date': 'Tue, 16 Sep 2025 19:28:21 GMT'
2025-09-16T19:28:22Z   [Information]   ✅ Container sftp-landing already exists
2025-09-16T19:28:22Z   [Information]   🚀 Starting blob upload...
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/RO_Time/AO_RO_TIME/ao_ro_time-enhanced/AO_RO_TIME_08-20-25_22.40.35.csv'
Request method: 'PUT'
Request headers:
    'Content-Length': '175465'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4ec2126a-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-09-16T19:28:22Z   [Information]   Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:22 GMT'
    'ETag': '"0x8DDF55732FD9188"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee695a-e01e-0050-7840-2770a0000000'
    'x-ms-client-request-id': '4ec2126a-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:21 GMT'
2025-09-16T19:28:22Z   [Information]   📤 Upload result: {'etag': '"0x8DDF55732FD9188"', 'last_modified': datetime.datetime(2025, 9, 16, 19, 28, 22, tzinfo=datetime.timezone.utc), 'content_md5': bytearray(b'\xf4\xdc\x12[1\x19\xca\x0c\xe1P\x84Z\xd0tw\xd5'), 'client_request_id': '4ec2126a-9333-11f0-8771-00155dea3ad8', 'request_id': '3eee695a-e01e-0050-7840-2770a0000000', 'version': '2025-07-05', 'version_id': None, 'date': datetime.datetime(2025, 9, 16, 19, 28, 21, tzinfo=datetime.timezone.utc), 'request_server_encrypted': True, 'encryption_key_sha256': None, 'encryption_scope': None, 'structured_body': None}
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/RO_Time/AO_RO_TIME/ao_ro_time-enhanced/AO_RO_TIME_08-20-25_22.40.35.csv'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4ecbab4a-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '175465'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Tue, 16 Sep 2025 19:28:22 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF55732FD9188"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee6981-e01e-0050-1d40-2770a0000000'
    'x-ms-client-request-id': '4ecbab4a-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:21 GMT'
2025-09-16T19:28:22Z   [Information]   ✅ Verification - Blob size: 175465 bytes
2025-09-16T19:28:22Z   [Information]   🎉 SUCCESS: Uploaded CSV: RO_Time/AO_RO_TIME/ao_ro_time-enhanced/AO_RO_TIME_08-20-25_22.40.35.csv - Shape: (844, 22)
2025-09-16T19:28:22Z   [Information]   ✅ Successfully processed dealer file: AO_RO_TIME_08-20-25_22.40.35.csv
2025-09-16T19:28:22Z   [Information]   🎯 DEALER PROCESSING SUMMARY: 1/1 files processed successfully
2025-09-16T19:28:22Z   [Information]   🧹 CLEANUP: Removing successfully processed source CSV files...
2025-09-16T19:28:22Z   [Information]   CLEANUP: Removing successfully processed source CSV files...
2025-09-16T19:28:22Z   [Information]   Files to delete: 1
2025-09-16T19:28:22Z   [Information]   Deleting source file 1/1: RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv
2025-09-16T19:28:22Z   [Information]   Attempting to delete blob file: RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv'
Request method: 'HEAD'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4ed08804-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 200
Response headers:
    'Content-Length': '119769'
    'Content-Type': 'application/octet-stream'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Mon, 15 Sep 2025 20:55:39 GMT'
    'Accept-Ranges': 'REDACTED'
    'ETag': '"0x8DDF49A3A760F68"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee6992-e01e-0050-2d40-2770a0000000'
    'x-ms-client-request-id': '4ed08804-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-resource-type': 'REDACTED'
    'x-ms-creation-time': 'REDACTED'
    'x-ms-lease-status': 'REDACTED'
    'x-ms-lease-state': 'REDACTED'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-server-encrypted': 'REDACTED'
    'x-ms-access-tier': 'REDACTED'
    'x-ms-access-tier-inferred': 'REDACTED'
    'x-ms-owner': 'REDACTED'
    'x-ms-group': 'REDACTED'
    'x-ms-permissions': 'REDACTED'
    'x-ms-acl': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:21 GMT'
2025-09-16T19:28:22Z   [Information]   Request URL: 'https://sagedw.blob.core.windows.net/sftp-landing/RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv'
Request method: 'DELETE'
Request headers:
    'x-ms-version': 'REDACTED'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.26.0 Python/3.11.13 (Linux-*********-microsoft-standard-x86_64-with-glibc2.31)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '4ed55fb4-9333-11f0-8771-00155dea3ad8'
    'Authorization': 'REDACTED'
No body was attached to the request
2025-09-16T19:28:22Z   [Information]   Response status: 202
Response headers:
    'Content-Length': '0'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '3eee69a4-e01e-0050-3f40-2770a0000000'
    'x-ms-client-request-id': '4ed55fb4-9333-11f0-8771-00155dea3ad8'
    'x-ms-version': 'REDACTED'
    'x-ms-delete-type-permanent': 'REDACTED'
    'x-ms-deletion-id': 'REDACTED'
    'Date': 'Tue, 16 Sep 2025 19:28:21 GMT'
2025-09-16T19:28:22Z   [Information]   ✓ Successfully deleted blob file: RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv
2025-09-16T19:28:22Z   [Information]     ✓ Successfully deleted: RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv
2025-09-16T19:28:22Z   [Information]   ==================================================
2025-09-16T19:28:22Z   [Information]   SOURCE FILE CLEANUP SUMMARY:
2025-09-16T19:28:22Z   [Information]     Total files to delete: 1
2025-09-16T19:28:22Z   [Information]     Successfully deleted: 1
2025-09-16T19:28:22Z   [Information]     Failed deletions: 0
2025-09-16T19:28:22Z   [Information]     ✓ Deleted files: ['RO_Time/AO_RO_TIME/ao_ro_time-decompressed/AO_RO_TIME_08-20-25_22.40.35.csv']
2025-09-16T19:28:22Z   [Information]   ==================================================
2025-09-16T19:28:22Z   [Information]   ✅ Job RO_TIME completed successfully
2025-09-16T19:28:22Z   [Information]   🎯 Job processing summary: 2/2 successful, 0 failed
2025-09-16T19:28:22Z   [Information]   Executed 'Functions.CSVProcessingPipeline' (Succeeded, Id=2e917b65-212e-4bbd-b493-98c2035b924d, Duration=31140ms)