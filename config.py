import os
from dotenv import load_dotenv

load_dotenv()

SFTP_CONFIG = {
    'hostname': os.getenv('SFTP_HOST'),
    'username': os.getenv('SFTP_USERNAME'),
    'password': os.getenv('SFTP_PASSWORD'),
    'port': int(os.getenv('SFTP_PORT', 22))
}

FILE_PREFIXES = os.getenv('FILE_PREFIXES', 'FIMASTSALES,FIMASTSALES2TEST').split(',')
ZIP_PASSWORD = os.getenv('ZIP_PASSWORD')
DATE_FORMAT = '08-18-25'  # Demo date

BASE_PATH = os.getenv('BASE_PATH', '/Users/<USER>/Desktop/GPTI/Swickard/swickardedw/landing/reynolds-ftp/fimastsales')
COMPRESSED_DIR = os.path.join(BASE_PATH, 'compressed')
DECOMPRESSED_DIR = os.path.join(BASE_PATH, 'decompressed')
