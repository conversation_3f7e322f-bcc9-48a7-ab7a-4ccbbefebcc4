# Swickard EDW - Azure Function Apps Suite

A comprehensive enterprise data warehouse solution featuring two Azure Function Apps for complete data processing workflows: **azure-csv-processing** for intelligent CSV data processing and **azure-ftp-etl** for automated SFTP data extraction and transformation.

## 🚀 Project Overview

This repository contains two enterprise-grade Azure Function Apps that work together to provide a complete data processing solution:

### 📊 Azure CSV Processing Function App
An intelligent CSV data processing pipeline that automatically:
- Downloads CSV files from Azure Blob Storage with streaming support for large files
- Performs intelligent datetime extraction from filenames using 9 pattern recognition algorithms
- Validates and transforms data with robust schema alignment using Polars high-performance engine
- Adds configurable composite keys with MD5/SHA256/SHA1 hashing algorithms
- Adds comprehensive audit columns with source traceability
- Converts data to optimized Parquet format for analytics with memory-efficient streaming
- Handles type compatibility and data quality issues automatically
- **Automatically deletes successfully processed source CSV files** for storage optimization
- **Moves processed zip files to archive folders** for organized storage management
- **Dynamic configuration loading from Azure Table Storage** (sftpConfig table) with real-time updates
- **Memory-optimized processing** with garbage collection and streaming for files up to 15GB
- **Job-specific configurations** supporting multiple processing workflows (FIMASTSALES, CUSTOMER-ACCTG, etc.)

### 🔄 Azure FTP ETL Function App
An automated SFTP data extraction and transformation pipeline that:
- **Connects to SFTP servers** with secure authentication and connection management
- **Downloads files by prefix** with intelligent filtering and date-based processing
- **Decompresses ZIP archives** automatically with support for nested structures
- **Processes multiple file types** with configurable prefix-based routing
- **Handles dealer-specific processing** with dynamic folder organization
- **Provides parallel processing** with configurable batch sizes for optimal performance
- **Integrates with Azure Table Storage** for dynamic configuration management
- **Supports sister file processing** for files that correspond to multiple dealers
- **Comprehensive logging and monitoring** with detailed processing statistics

## 🏗️ Architecture

### Modular Design
Both applications follow a clean, modular architecture with separation of concerns:

```
swickard-edw/
├── azure-csv-processing/           # CSV Processing Function App
│   ├── CSVProcessingPipeline/      # Main Azure Function
│   │   ├── __init__.py             # HTTP trigger entry point
│   │   └── function.json           # Function configuration
│   ├── shared/                     # Shared components library
│   │   ├── azure_config.py        # Azure configuration management
│   │   ├── blob_manager.py        # Azure Blob Storage operations
│   │   ├── csv_processor.py       # CSV validation and processing
│   │   ├── data_transformer.py    # Data transformation and audit columns
│   │   ├── file_utils.py          # Intelligent datetime extraction
│   │   ├── file_consolidator.py   # Multi-file processing
│   │   ├── orchestrator.py        # Pipeline orchestration
│   │   └── error_handler.py       # Comprehensive error handling
│   ├── requirements.txt            # Python dependencies
│   ├── host.json                  # Function App configuration
│   └── local.settings.json        # Local development settings
├── azure-ftp-etl/                 # FTP ETL Function App
│   ├── FTPETLPipeline/             # Main Azure Function
│   │   ├── __init__.py             # HTTP trigger entry point
│   │   └── function.json           # Function configuration
│   ├── shared/                     # Shared components library
│   │   ├── azure_config.py        # Azure configuration management
│   │   ├── sftp_downloader.py     # SFTP connection and file download
│   │   ├── file_decompressor.py   # ZIP file decompression
│   │   ├── orchestrator.py        # ETL pipeline orchestration
│   │   └── single_prefix_processor.py # Individual prefix processing
│   ├── requirements.txt            # Python dependencies
│   ├── host.json                  # Function App configuration
│   └── local.settings.json        # Local development settings
└── scripts/                       # Deployment and configuration scripts
```

### Core Components

#### Azure CSV Processing Components
- **🔧 Orchestrator**: Manages the complete 6-step processing pipeline
- **☁️ Blob Manager**: Handles Azure Storage operations with retry logic
- **📊 CSV Processor**: Validates CSV structure and handles encoding issues
- **🔄 Data Transformer**: Performs schema alignment and adds audit columns
- **📅 File Utils**: Intelligent datetime extraction from various filename patterns
- **🛠️ Error Handler**: Comprehensive error handling and logging

#### Azure FTP ETL Components
- **🔄 ETL Orchestrator**: Manages the complete SFTP-to-Azure pipeline
- **📡 SFTP Downloader**: Secure SFTP connections with retry logic and authentication
- **📦 File Decompressor**: ZIP archive extraction with nested structure support
- **⚙️ Prefix Processor**: Individual file type processing with dealer-aware logic
- **🔧 Configuration Manager**: Dynamic Azure Table Storage configuration loading

## ✨ Key Features

### 🧠 Intelligent Datetime Extraction
- **9 Pattern Recognition Algorithms** for extracting dates from filenames
- **Automatic Format Detection** for various date/time patterns
- **Fallback Mechanisms** using file metadata when patterns don't match
- **Robust Error Handling** with graceful degradation

### 🔄 Advanced Data Processing
- **Polars-Based Processing** for high-performance data operations with lazy evaluation
- **Streaming Processing** with `collect(streaming=True)` for memory efficiency
- **Automatic Schema Alignment** with type compatibility fixes
- **Configurable Composite Key Generation** with multiple hashing algorithms (MD5, SHA256, SHA1)
- **Job-Specific Surrogate Keys** with customizable column combinations and separators
- **Comprehensive Audit Trails** with source date/time tracking
- **Memory Monitoring** with `psutil` integration and automatic garbage collection

### 🛡️ Enterprise Reliability
- **Retry Logic** for transient failures
- **Comprehensive Logging** for debugging and monitoring
- **Type Safety** with automatic data type conversion
- **Memory-Optimized Processing** with chunked processing for extremely large datasets
- **Graceful Degradation** with fallback mechanisms for configuration failures

### 🗂️ Storage Management & Cleanup
- **Automatic Source File Deletion** - Removes successfully processed CSV files to optimize storage
- **Zip File Archiving** - Moves FIMASTSALES*.zip files from compressed folder to archive folder
- **Placeholder File Cleanup** - Automatically removes .placeholder files after processing
- **Configurable Cleanup Operations** - All cleanup operations can be enabled/disabled via configuration

### ⚙️ Dynamic Configuration System
- **Azure Table Storage Integration** - Configuration loaded from sftpConfig table with caching
- **Real-time Configuration Updates** - Changes take effect without redeployment
- **Job-Specific Settings** - Support for multiple processing workflows (FIMASTSALES, CUSTOMER-ACCTG, etc.)
- **Configurable Surrogate Keys** - Customizable column combinations, separators, and hash methods
- **Intelligent Job Detection** - Automatic job identification based on file patterns
- **Configuration Validation** - Built-in validation and error handling for configuration issues
- **Fallback Configuration** - Graceful degradation to hardcoded values when table unavailable

### 📊 Large File Processing Capabilities
- **Optimized Architecture**: Polars lazy evaluation with streaming processing
- **Memory Efficient**: Uses `collect(streaming=True)` for large file handling
- **Streaming Downloads**: Memory-efficient CSV downloads from Azure Blob Storage
- **Chunked Processing**: Automatic fallback for datasets too large for memory
- **Dynamic Batch Sizing**: Automatically adjusts batch sizes based on dataset size
- **Memory Monitoring**: Real-time memory usage tracking with `psutil`
- **Garbage Collection**: Automatic memory cleanup during processing
- **Recommended**: Files up to **5GB** with optimal performance
- **Maximum Capacity**: Files up to **15GB** (with Premium Azure Function Plan)

## 🛠️ Quick Start

### Azure CSV Processing Function App
```bash
cd azure-csv-processing
# See azure-csv-processing/README.md for detailed setup
func start  # Local development
func azure functionapp publish csv-processing-func-3514  # Deploy
```

### Azure FTP ETL Function App
```bash
cd azure-ftp-etl
# See azure-ftp-etl/README.md for detailed setup (if available)
func start  # Local development
func azure functionapp publish <your-ftp-etl-function-app-name>  # Deploy
```

## 📋 Prerequisites

### Azure Resources
- **Azure Function Apps** (Python 3.11 runtime):
  - `csv-processing-func-3514` (CSV Processing)
  - `<ftp-etl-function-app-name>` (FTP ETL)
- **Azure Storage Account** (`sagedw`) with blob containers:
  - `sftp-landing` (primary container with organized folder structure)
    - `FIMAST-SALES/fimastsales-decompressed` (source CSV files)
    - `FIMAST-SALES/fimastsales-enhanced` (output Parquet/CSV files)
    - `FIMAST-SALES/fimastsales-compressed` (zip files)
    - `FIMAST-SALES/archive` (archived zip files)
- **Azure Table Storage** (`sftpConfig` table for dynamic configuration)
- **Azure Application Insights** (optional, for monitoring)

### Development Environment
- **Python 3.9+** (3.11 recommended for Azure deployment)
- **Azure Functions Core Tools** v4.x
- **Azure CLI** (for authentication and deployment)

### Required Dependencies
```txt
# Core Azure Functions
azure-functions>=1.0.0,<2.0.0
azure-storage-blob>=12.0.0
azure-data-tables>=12.0.0
azure-identity>=1.0.0
python-dateutil>=2.8.0

# High-Performance Data Processing
polars>=0.20.0
pyarrow>=14.0.0

# Memory Monitoring
psutil>=5.8.0
```

## 🔐 Configuration

### Dynamic Configuration System
The application now uses **Azure Table Storage** for dynamic configuration management through the `sftpConfig` table. This allows real-time configuration updates without redeployment.

#### Azure Table Storage Configuration (sftpConfig table)
The system loads configuration from the `sftpConfig` table in your Azure Storage Account:

**System Configuration (PartitionKey: 'SystemConfig')**:
- `StorageAccountName` - Azure Storage Account name
- `SourceContainer` - Source container name (default: 'sftp-landing')
- `OutputContainer` - Output container name (default: 'sftp-landing')
- `SurrogateKeyColumn` - Name of composite key column (default: 'Composite Key')
- `AuditColumns` - Comma-separated audit column names
- `DefaultConsolidationMethod` - File consolidation method (default: 'concat')
- `ZipMovementEnabled` - Enable/disable zip file archiving (true/false)
- `ZipSourceFolder` - Source folder for zip files (default: 'FIMAST-SALES/fimastsales-compressed')
- `ZipArchiveFolder` - Archive folder for zip files (default: 'FIMAST-SALES/archive')
- `ZipFilePattern` - Pattern for zip files to move (default: 'FIMASTSALES*.zip')

**Job-Specific Configuration**:
Each job can have its own PartitionKey (e.g., 'FIMASTSALES_', 'CUSTOMER-ACCTG_') with specific settings:
- `Folder` - Base folder path (e.g., 'FIMAST-SALES', 'CUSTOMER-ACCTG')
- `SourceSubfolder` - Source subfolder name (e.g., 'fimastsales-decompressed')
- `OutputSubfolder` - Output subfolder name (e.g., 'fimastsales-enhanced')
- `FilePrefix` - File prefix pattern (e.g., 'FIMASTSALES', 'CUSTOMER-ACCTG')
- `isEnabled` - Enable/disable the job (true/false)
- `SurrogateKeyColumns` - Comma-separated column names for composite key generation
- `SurrogateKeySeparator` - Separator for key concatenation (PIPE, COMMA, UNDERSCORE)
- `SurrogateKeyMethod` - Hash method (MD5, SHA256, SHA1)
- `ZipMovementEnabled` - Job-specific zip movement setting

### Fallback Environment Variables
If Azure Table Storage is unavailable, the system falls back to these environment variables:

```bash
# Azure Storage Configuration (Managed Identity - Recommended)
# No connection string needed when using Managed Identity

# Legacy Environment Variables (Fallback only)
AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=..."
AZURE_STORAGE_ACCOUNT_NAME="sagedw"

# Optional: Application Insights
APPINSIGHTS_INSTRUMENTATIONKEY="your-app-insights-key"
```

## 🆕 Recent Enhancements (September 2025)

### Memory Optimization & Large File Support
- **Streaming Processing**: Implemented `collect(streaming=True)` for memory-efficient processing of large datasets
- **Memory Monitoring**: Added `psutil` integration for real-time memory usage tracking
- **Garbage Collection**: Automatic memory cleanup with `gc.collect()` during processing
- **Chunked Processing**: Fallback mechanism for datasets too large for memory, even with streaming
- **Dynamic Batch Sizing**: Automatically adjusts batch sizes based on dataset size to prevent memory overflow
- **Streaming Downloads**: Memory-efficient CSV downloads from Azure Blob Storage

### Advanced Configuration System
- **Job-Specific Configurations**: Support for multiple processing workflows (FIMASTSALES, CUSTOMER-ACCTG, etc.)
- **Configurable Surrogate Keys**: Customizable column combinations, separators, and hash methods (MD5, SHA256, SHA1)
- **Intelligent Job Detection**: Automatic job identification based on file patterns from configuration
- **Configuration Validation**: Built-in validation and error handling for configuration issues
- **Real-time Configuration**: Configuration changes take effect immediately without requiring redeployment

### Storage Management & Cleanup
- **Automatic Source File Deletion**: Successfully processed CSV files are automatically deleted from the source container
- **Zip File Archiving**: Processed zip files are automatically moved to archive folders for organized storage
- **Placeholder File Cleanup**: Temporary .placeholder files are automatically removed after successful processing
- **Configurable Cleanup Operations**: All cleanup operations can be enabled/disabled via configuration

### Enhanced Data Processing
- **Configurable Composite Key Generation**: Support for multiple hashing algorithms and column combinations
- **Job-Specific Surrogate Keys**: Different key generation logic per processing job
- **Improved Error Handling**: Enhanced error handling for configuration and processing failures
- **Processing Statistics**: Comprehensive reporting of successfully processed files and cleanup results

## 📖 Documentation

- **Comprehensive Guide:** See `azure-csv-processing/README.md` for detailed documentation
- **Network Security:** See `scripts/` for network configuration and security setup
- **Deployment Scripts:** See `azure-csv-processing/deployment/` for validation and testing

## 🧪 Testing

The function app includes comprehensive test suites:
- **Unit Tests:** `azure-csv-processing/tests/`
- **Integration Tests:** `tests/test_integration_phase2.py`
- **Performance Tests:** Polars streaming validation
- **End-to-End Tests:** Complete pipeline validation

## 🚀 Deployment

### Local Development
```bash
cd azure-csv-processing
func start
```

### Azure Deployment
```bash
# Recommended deployment method using full path to func.cmd
cd azure-csv-processing
C:\Users\<USER>\AppData\Roaming\npm\func.cmd azure functionapp publish csv-processing-func-3514

# Alternative using Azure Functions Core Tools
func azure functionapp publish csv-processing-func-3514
```

### Configuration Table Setup
To set up the Azure Table Storage configuration:

1. **Create sftpConfig Table** in your Azure Storage Account
2. **Add System Configuration** entries with PartitionKey = 'SystemConfig':
   ```
   RowKey: ZipMovementEnabled, Value: true
   RowKey: ZipSourceFolder, Value: FIMAST-SALES/fimastsales-compressed
   RowKey: ZipArchiveFolder, Value: FIMAST-SALES/archive
   RowKey: SourceContainer, Value: sftp-landing
   RowKey: OutputContainer, Value: sftp-landing
   ```
3. **Add Job-Specific Configuration** with PartitionKey = 'JobConfig':
   ```
   # Example: FIMASTSALES job
   RowKey: FIMASTSALES_, isEnabled: true
   Folder: FIMAST-SALES
   SourceSubfolder: fimastsales-decompressed
   OutputSubfolder: fimastsales-enhanced
   FilePrefix: FIMASTSALES
   SurrogateKeyColumns: ST/BR,DEAL NO,,
   SurrogateKeySeparator: PIPE
   SurrogateKeyMethod: MD5

   # Example: CUSTOMER-ACCTG job
   RowKey: CUSTOMER-ACCTG_, isEnabled: true
   Folder: CUSTOMER-ACCTG
   SourceSubfolder: customer-acctg-decompressed
   OutputSubfolder: customer-acctg-enhanced
   FilePrefix: CUSTOMER-ACCTG
   SurrogateKeyColumns: RECID,LAST-NAME,,
   SurrogateKeySeparator: PIPE
   SurrogateKeyMethod: MD5
   ```

### CI/CD Integration
- GitHub Actions workflow included
- Automated testing and deployment
- Environment-specific configurations

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Configuration Issues
- **BlobNotFound Errors**: Ensure job-specific configuration exists in Azure Table Storage
- **Missing Configuration**: Use `check_config.py` to validate configuration entries
- **Path Mismatches**: Verify `Folder`, `SourceSubfolder`, and `OutputSubfolder` settings

#### Memory Issues
- **Out of Memory**: The system automatically uses streaming processing and chunked processing
- **Large Files**: Files up to 15GB are supported with Premium Azure Function Plan
- **Memory Monitoring**: Check logs for memory usage statistics

#### Processing Failures
- **Job Detection**: Ensure `FilePrefix` matches your CSV file naming pattern
- **Surrogate Key Errors**: Verify `SurrogateKeyColumns` exist in your CSV files
- **Hash Method**: Supported methods are MD5, SHA256, SHA1

## 📞 Support

### Azure CSV Processing Function App
For issues and troubleshooting:
- **Configuration Validation:** Use `azure-csv-processing/check_config.py`
- **Detailed Troubleshooting:** Check `azure-csv-processing/README.md`
- **Performance Issues:** Review Polars streaming configuration and memory monitoring logs

### Azure FTP ETL Function App
For issues and troubleshooting:
- **SFTP Connection Issues:** Check credentials and network connectivity
- **File Processing Errors:** Review function app logs for detailed error messages
- **Configuration Issues:** Validate Azure Table Storage configuration entries

### General Support
- **Azure Issues:** Check Function App logs and Application Insights
- **Network Security:** Review `scripts/` configuration files

---

## 🏆 Key Achievements

### Azure CSV Processing Function App
- **✅ Production-Ready**: Successfully processing multiple job types (FIMASTSALES, CUSTOMER-ACCTG)
- **✅ Memory Optimized**: Handles files up to 15GB with streaming and chunked processing
- **✅ Highly Configurable**: Dynamic job configurations with real-time updates
- **✅ Storage Efficient**: Automatic cleanup and archiving of processed files
- **✅ Performance Optimized**: Polars-based processing with lazy evaluation

### Azure FTP ETL Function App
- **✅ Automated SFTP Integration**: Secure connection and file retrieval from external systems
- **✅ Parallel Processing**: Configurable batch processing for optimal performance
- **✅ Dealer-Aware Processing**: Intelligent handling of dealer-specific file structures
- **✅ Sister File Support**: Advanced logic for files corresponding to multiple dealers
- **✅ Dynamic Configuration**: Real-time configuration updates via Azure Table Storage

### Overall Solution
- **✅ Enterprise-Grade**: Comprehensive error handling, logging, and monitoring across both apps
- **✅ Scalable Architecture**: Modular design supporting multiple data processing workflows
- **✅ Complete Data Pipeline**: End-to-end solution from SFTP extraction to processed analytics data

**Built with ❤️ for enterprise-grade data processing on Azure**
