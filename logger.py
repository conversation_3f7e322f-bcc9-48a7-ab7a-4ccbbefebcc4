import logging

def setup_logger(file_prefix):
    logger = logging.getLogger(file_prefix)
    logger.setLevel(logging.INFO)

    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    file_handler = logging.FileHandler(f"{file_prefix}.log", mode='a')
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    return logger

def log_success(logger, operation, filename):
    logger.info(f"SUCCESS: {operation} - {filename}")

def log_error(logger, operation, filename, error):
    logger.error(f"ERROR: {operation} - {filename} - {str(error)}")
