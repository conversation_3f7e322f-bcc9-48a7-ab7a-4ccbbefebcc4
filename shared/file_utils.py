"""
File utility functions for Polars implementation
Minimal utilities without pandas dependencies
"""

import re
from datetime import datetime
import logging

def get_file_info_from_name(filename):
    """Extract information from filename for audit purposes"""
    try:
        # Extract date and prefix information from filename
        # Expected format: PREFIX_DATE_*.csv or PREFIX_DATE_*.parquet

        file_info = {
            'original_filename': filename,
            'prefix': None,
            'date_part': None,
            'source_date': None
        }

        # Remove file extensions
        name_without_ext = filename.replace('.csv', '').replace('.parquet', '')

        # Common file patterns for FIMASTSALES
        file_patterns = ['FIMASTSALES', 'SALES', 'DATA']
        
        # Try to extract prefix and date
        for prefix in file_patterns:
            if name_without_ext.startswith(prefix):
                file_info['prefix'] = prefix
                # Try to extract date part (assuming format: PREFIX_MM-DD-YY_HH-MM)
                remaining = name_without_ext[len(prefix):].lstrip('_')

                # Look for date pattern MM-DD-YY (with optional time HH-MM)
                date_pattern = r'(\d{2}-\d{2}-\d{2})(?:_(\d{2}-\d{2}))?'
                match = re.search(date_pattern, remaining)
                
                if match:
                    date_part = match.group(1)  # MM-DD-YY
                    time_part = match.group(2)  # HH-MM (optional)
                    
                    file_info['date_part'] = date_part
                    
                    # Try to parse the date
                    try:
                        # Convert MM-DD-YY to datetime
                        parsed_date = datetime.strptime(date_part, '%m-%d-%y')
                        
                        if time_part:
                            # Add time if available
                            time_obj = datetime.strptime(time_part, '%H-%M').time()
                            parsed_date = datetime.combine(parsed_date.date(), time_obj)
                        
                        file_info['source_date'] = parsed_date.strftime('%Y-%m-%d %H:%M:%S')
                        
                    except ValueError as date_error:
                        logging.warning(f"Could not parse date '{date_part}': {date_error}")
                        file_info['source_date'] = None
                
                break

        logging.info(f"File info extracted: {file_info}")
        return file_info
        
    except Exception as e:
        logging.error(f"Error extracting file info from {filename}: {e}")
        return {
            'original_filename': filename,
            'prefix': None,
            'date_part': None,
            'source_date': None
        }

def detect_numeric_columns_polars(df):
    """Detect numeric columns in a Polars DataFrame"""
    try:
        import polars as pl
        
        numeric_columns = []
        for col in df.columns:
            if df[col].dtype in [pl.Int64, pl.Float64, pl.Int32, pl.Float32, pl.Int16, pl.Int8]:
                numeric_columns.append(col)
        
        logging.info(f"Detected numeric columns: {numeric_columns}")
        return numeric_columns
        
    except Exception as e:
        logging.error(f"Error detecting numeric columns: {e}")
        return []
