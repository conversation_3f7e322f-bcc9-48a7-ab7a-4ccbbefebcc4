from sftp_downloader import download_files
from file_decompressor import decompress_files

def run_pipeline():
    print("Starting FTP ETL Pipeline...")

    print("Downloading files from SFTP...")
    download_files()
    print("✓ Download completed")

    print("Extracting files...")
    decompress_files()
    print("✓ Extraction completed")

    print("Pipeline completed successfully!")

if __name__ == "__main__":
    run_pipeline()
