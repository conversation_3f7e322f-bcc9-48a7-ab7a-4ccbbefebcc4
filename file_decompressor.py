import zipfile
import pyzipper
import os
from config import COMPRESSED_DIR, DECOMPRESSED_DIR, FILE_PREFIXES, ZIP_PASSWORD
from logger import setup_logger, log_success, log_error

def decompress_files():
    os.makedirs(DECOMPRESSED_DIR, exist_ok=True)

    if not os.path.exists(COMPRESSED_DIR):
        return

    for prefix in FILE_PREFIXES:
        logger = setup_logger(prefix)
        zip_files = [f for f in os.listdir(COMPRESSED_DIR)
                    if f.startswith(prefix) and (f.lower().endswith('.csv.zip') or f.upper().endswith('.csv.zip'))]

        for zip_filename in zip_files:
            zip_path = os.path.join(COMPRESSED_DIR, zip_filename)
            try:
                with pyzipper.AESZipFile(zip_path, 'r') as zip_ref:
                    if ZIP_PASSWORD:
                        zip_ref.setpassword(ZIP_PASSWORD.encode())
                    zip_ref.extractall(DECOMPRESSED_DIR)
                    for extracted_file in zip_ref.namelist():
                        log_success(logger, "DECOMPRESS", f"{zip_filename} -> {extracted_file}")
            except Exception:
                try:
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        if ZIP_PASSWORD:
                            zip_ref.extractall(DECOMPRESSED_DIR, pwd=ZIP_PASSWORD.encode())
                        else:
                            zip_ref.extractall(DECOMPRESSED_DIR)
                        for extracted_file in zip_ref.namelist():
                            log_success(logger, "DECOMPRESS", f"{zip_filename} -> {extracted_file}")
                except Exception as e:
                    log_error(logger, "DECOMPRESS", zip_filename, e)

if __name__ == "__main__":
    decompress_files()
