"""
Integration tests for Phase 2: Core Processing Logic
Tests end-to-end pipeline with Polars lazy evaluation and join operations
"""

import os
import sys
import unittest
import polars as pl
import logging
from io import BytesIO
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add shared directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(os.path.dirname(current_dir), 'shared')
if shared_dir not in sys.path:
    sys.path.append(shared_dir)

from orchestrator import run_parquet_processing_pipeline, CSVProcessingOrchestrator
from file_consolidator import consolidate_parquet_files_lazy
from data_transformer import add_surrogate_key_polars, append_audit_columns_polars


class TestPhase2Integration(unittest.TestCase):
    """Integration tests for Phase 2 core processing logic"""
    
    def setUp(self):
        """Set up test fixtures with sample business data"""
        # Create realistic sales data for testing
        self.sales_data_1 = pl.DataFrame({
            'customer_id': [1001, 1002, 1003, 1004, 1005],
            'product_id': [2001, 2002, 2003, 2001, 2004],
            'sale_amount': [150.50, 299.99, 75.25, 150.50, 450.00],
            'sale_date': ['2024-01-15', '2024-01-15', '2024-01-15', '2024-01-16', '2024-01-16'],
            'sales_rep': ['John Doe', 'Jane Smith', 'Bob Wilson', 'John Doe', 'Alice Brown']
        })
        
        self.sales_data_2 = pl.DataFrame({
            'customer_id': [1006, 1007, 1008, 1009, 1010],
            'product_id': [2002, 2005, 2001, 2003, 2006],
            'sale_amount': [199.99, 89.50, 150.50, 75.25, 320.00],
            'sale_date': ['2024-01-16', '2024-01-17', '2024-01-17', '2024-01-17', '2024-01-18'],
            'sales_rep': ['Jane Smith', 'Charlie Davis', 'John Doe', 'Bob Wilson', 'Alice Brown']
        })
        
        # Create Parquet buffers for testing
        self.parquet_buffer_1 = BytesIO()
        self.sales_data_1.write_parquet(self.parquet_buffer_1, compression='snappy')
        self.parquet_bytes_1 = self.parquet_buffer_1.getvalue()
        
        self.parquet_buffer_2 = BytesIO()
        self.sales_data_2.write_parquet(self.parquet_buffer_2, compression='snappy')
        self.parquet_bytes_2 = self.parquet_buffer_2.getvalue()

    @patch('orchestrator.list_parquet_files_in_container')
    @patch('orchestrator.consolidate_parquet_files_lazy')
    @patch('orchestrator.upload_parquet_to_blob')
    def test_end_to_end_parquet_pipeline(self, mock_upload, mock_consolidate, mock_list_files):
        """Test complete end-to-end Parquet processing pipeline"""
        
        # Mock file discovery
        mock_list_files.return_value = ['FIMASTSALES_01-15-24.parquet', 'FIMASTSALES_01-16-24.parquet']
        
        # Mock consolidation - return combined data
        combined_data = pl.concat([self.sales_data_1, self.sales_data_2], how="vertical")
        mock_consolidate.return_value = combined_data
        
        # Mock successful upload
        mock_upload.return_value = True
        
        # Run the pipeline
        result = run_parquet_processing_pipeline()
        
        # Assertions
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['processing_engine'], 'Polars')
        self.assertEqual(result['file_format'], 'Parquet')
        self.assertIn('final_shape', result)
        self.assertIn('total_time', result)
        
        # Verify function calls
        mock_list_files.assert_called_once()
        mock_consolidate.assert_called_once()
        mock_upload.assert_called_once()

    def test_lazy_evaluation_memory_efficiency(self):
        """Test that lazy evaluation provides memory efficiency"""
        
        # Create larger datasets to test memory efficiency
        large_data_1 = pl.DataFrame({
            'id': range(5000),
            'value': [i * 1.5 for i in range(5000)],
            'category': [f'Cat_{i % 100}' for i in range(5000)],
            'timestamp': ['2024-01-15T10:00:00' for _ in range(5000)]
        })
        
        large_data_2 = pl.DataFrame({
            'id': range(5000, 10000),
            'value': [i * 1.5 for i in range(5000, 10000)],
            'category': [f'Cat_{i % 100}' for i in range(5000, 10000)],
            'timestamp': ['2024-01-16T10:00:00' for _ in range(5000)]
        })
        
        # Mock the lazy download function
        def mock_download_lazy(filename, container):
            if 'file1' in filename:
                return pl.LazyFrame(large_data_1)
            else:
                return pl.LazyFrame(large_data_2)
        
        with patch('file_consolidator.download_parquet_lazy', side_effect=mock_download_lazy):
            result = consolidate_parquet_files_lazy(['file1.parquet', 'file2.parquet'], 'test-container')
            
            # Verify the result
            self.assertIsInstance(result, pl.DataFrame)
            self.assertEqual(result.shape, (10000, 5))  # Combined data + source file column
            
            # Check that source tracking was added
            self.assertIn('_source_file', result.columns)

    def test_join_operations_with_lazy_evaluation(self):
        """Test join operations using lazy evaluation"""
        
        # Create customer data for joining
        customer_data = pl.DataFrame({
            'customer_id': [1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010],
            'customer_name': ['Alice Corp', 'Bob Inc', 'Charlie Ltd', 'Delta Co', 'Echo LLC',
                             'Foxtrot Inc', 'Golf Corp', 'Hotel Ltd', 'India Co', 'Juliet LLC'],
            'customer_tier': ['Gold', 'Silver', 'Bronze', 'Gold', 'Platinum',
                             'Silver', 'Bronze', 'Gold', 'Silver', 'Platinum']
        })
        
        # Combine sales data
        combined_sales = pl.concat([self.sales_data_1, self.sales_data_2], how="vertical")
        
        # Perform lazy join operation
        lazy_sales = combined_sales.lazy()
        lazy_customers = customer_data.lazy()
        
        joined_lazy = lazy_sales.join(lazy_customers, on='customer_id', how='inner')
        result = joined_lazy.collect()
        
        # Assertions
        self.assertIsInstance(result, pl.DataFrame)
        self.assertEqual(result.shape, (10, 8))  # All sales records should join
        self.assertIn('customer_name', result.columns)
        self.assertIn('customer_tier', result.columns)
        
        # Verify join worked correctly
        gold_customers = result.filter(pl.col('customer_tier') == 'Gold')
        self.assertTrue(len(gold_customers) > 0)

    def test_data_transformation_accuracy(self):
        """Test accuracy of data transformations with business logic"""
        
        # Test surrogate key generation
        result_with_key = add_surrogate_key_polars(self.sales_data_1)
        
        # Verify surrogate key is first column
        self.assertEqual(result_with_key.columns[0], 'Surrogate Key')
        
        # Verify surrogate key values (should be string concatenation of customer_id + product_id)
        expected_keys = ["10012001", "10022002", "10032003", "10042001", "10052004"]  # customer_id + product_id as strings
        actual_keys = result_with_key.select(pl.col('Surrogate Key')).to_series().to_list()
        self.assertEqual(actual_keys, expected_keys)
        
        # Test audit columns
        source_files = ['FIMASTSALES_01-15-24.parquet']
        result_with_audit = append_audit_columns_polars(result_with_key, source_files)
        
        # Verify audit columns are present and at the end
        audit_cols = ['Source Date/Time', 'Created Date/Time', 'Modified Date/Time']
        for col in audit_cols:
            self.assertIn(col, result_with_audit.columns)
        
        # Verify audit columns are the last three
        last_three = result_with_audit.columns[-3:]
        self.assertEqual(list(last_three), audit_cols)

    def test_performance_improvement_validation(self):
        """Test that processing time shows improvement over baseline"""
        
        # Create performance test data
        perf_data = pl.DataFrame({
            'id': range(1000),
            'value1': [i * 1.1 for i in range(1000)],
            'value2': [i * 2.2 for i in range(1000)],
            'text_field': [f'Text_{i}' for i in range(1000)],
            'date_field': ['2024-01-15' for _ in range(1000)]
        })
        
        # Time the transformations
        start_time = datetime.now()
        
        # Apply transformations
        with_key = add_surrogate_key_polars(perf_data)
        with_audit = append_audit_columns_polars(with_key, ['test.parquet'])
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # Verify transformations completed
        self.assertIsInstance(with_audit, pl.DataFrame)
        self.assertEqual(with_audit.shape, (1000, 9))  # 5 original + 1 surrogate + 3 audit
        
        # Performance should be reasonable (under 1 second for 1000 rows)
        self.assertLess(processing_time, 1.0, "Processing should be fast with Polars")
        
        logging.info(f"Performance test: {processing_time:.4f} seconds for 1000 rows")

    @patch('orchestrator.SOURCE_CONTAINER', 'test-source')
    @patch('orchestrator.get_output_container')
    def test_orchestrator_auto_detection(self, mock_get_output):
        """Test orchestrator auto-detection of file formats"""
        
        mock_get_output.return_value = 'test-output'
        
        orchestrator = CSVProcessingOrchestrator({})
        
        # Test explicit Parquet processing
        with patch('orchestrator.run_parquet_processing_pipeline') as mock_parquet:
            mock_parquet.return_value = {'status': 'success', 'format': 'parquet'}
            
            result = orchestrator.process_parquet_pipeline()
            
            self.assertEqual(result['status'], 'success')
            mock_parquet.assert_called_once()

    def test_error_handling_and_recovery(self):
        """Test error handling in the processing pipeline"""
        
        # Test with invalid data
        invalid_data = pl.DataFrame({
            'invalid_col': [None, None, None]
        })
        
        # Should handle gracefully
        try:
            result = add_surrogate_key_polars(invalid_data)
            # Should still work, using row numbers as fallback
            self.assertIsInstance(result, pl.DataFrame)
            self.assertEqual(result.columns[0], 'Surrogate Key')
        except Exception as e:
            self.fail(f"Should handle invalid data gracefully: {e}")


if __name__ == '__main__':
    # Configure logging for tests
    logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
    
    # Run tests
    unittest.main()
