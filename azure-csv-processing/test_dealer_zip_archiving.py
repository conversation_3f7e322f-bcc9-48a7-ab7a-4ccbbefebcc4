"""
Test suite for dealer-aware zip archiving functionality
Tests the new feature that handles dynamic zip archiving for dealer-involved file types
"""

import pytest
import logging
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add the shared directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(current_dir, 'shared')
sys.path.insert(0, shared_dir)

# Configure logging for tests
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class TestDealerZipArchiving:
    """Test cases for dealer-aware zip archiving functionality"""

    def test_dealer_zip_archiving_enabled_detection(self):
        """Test detection of dealer zip archiving configuration"""

        # Mock job config with dealer zip archiving enabled (using existing keys)
        mock_job_config = {
            'DealerPrefix': True,
            'ZipMovementEnabled': '<EMAIL>'
        }

        with patch('azure_config.get_job_config') as mock_get_job_config:
            mock_get_job_config.return_value = mock_job_config

            from azure_config import get_dealer_zip_archiving_enabled

            result = get_dealer_zip_archiving_enabled('RO_TIME')
            assert result == True
            
    def test_dealer_zip_archiving_disabled_detection(self):
        """Test detection when dealer zip archiving is disabled"""
        
        # Mock job config with dealer zip archiving disabled
        mock_job_config = {
            'DealerPrefix': True,
            'DealerZipArchivingEnabled': '<EMAIL>'
        }
        
        with patch('azure_config.get_job_config') as mock_get_job_config:
            mock_get_job_config.return_value = mock_job_config
            
            from azure_config import get_dealer_zip_archiving_enabled
            
            result = get_dealer_zip_archiving_enabled('RO_TIME')
            assert result == False

    def test_non_dealer_job_detection(self):
        """Test detection for non-dealer jobs"""
        
        # Mock job config for non-dealer job
        mock_job_config = {
            'DealerPrefix': False,
            'DealerZipArchivingEnabled': '<EMAIL>'
        }
        
        with patch('azure_config.get_job_config') as mock_get_job_config:
            mock_get_job_config.return_value = mock_job_config
            
            from azure_config import get_dealer_zip_archiving_enabled
            
            result = get_dealer_zip_archiving_enabled('FIMASTSALES_')
            assert result == False

    def test_dealer_zip_archive_folder_generation(self):
        """Test generation of dealer-specific archive folder paths"""

        # Mock job config (using existing ZipArchiveFolder)
        mock_job_config = {
            'ZipArchiveFolder': 'RO_Time/archive'
        }

        mock_config = Mock()
        mock_config.source_folder_path = 'RO_Time/decompressed'
        mock_config.zip_archive_folder = 'RO_Time/archive'

        with patch('azure_config.get_job_config') as mock_get_job_config, \
             patch('azure_config.Config') as mock_config_class:

            mock_get_job_config.return_value = mock_job_config
            mock_config_class.return_value = mock_config

            from azure_config import get_dealer_zip_archive_folder

            result = get_dealer_zip_archive_folder('AO_RO_TIME', 'RO_TIME')
            expected = 'RO_Time/AO_RO_TIME/archive'
            assert result == expected

    def test_dealer_zip_file_pattern_generation(self):
        """Test generation of dealer-specific file patterns"""

        # Mock job config with existing ZipFilePattern
        mock_job_config = {
            'ZipFilePattern': '*RO_TIME*.zip'
        }

        mock_config = Mock()
        mock_config.zip_file_pattern = '*RO_TIME*.zip'

        with patch('azure_config.get_job_config') as mock_get_job_config, \
             patch('azure_config.Config') as mock_config_class:

            mock_get_job_config.return_value = mock_job_config
            mock_config_class.return_value = mock_config

            from azure_config import get_dealer_zip_file_pattern_for_dealer

            result = get_dealer_zip_file_pattern_for_dealer('AO_RO_TIME', 'RO_TIME')
            expected = 'AO_RO_TIME*.zip'
            assert result == expected

    def test_dealer_zip_file_pattern_fallback(self):
        """Test fallback pattern generation when no custom pattern is configured"""
        
        # Mock job config without custom pattern
        mock_job_config = {}
        
        with patch('azure_config.get_job_config') as mock_get_job_config:
            mock_get_job_config.return_value = mock_job_config
            
            from azure_config import get_dealer_zip_file_pattern_for_dealer
            
            result = get_dealer_zip_file_pattern_for_dealer('AO_RO_TIME', 'RO_TIME')
            expected = 'AO*.zip'  # Should extract dealer prefix
            assert result == expected

    def test_dealer_zip_source_folders_discovery(self):
        """Test discovery of dealer zip source folders"""

        # Mock blob service and container client
        mock_container_client = Mock()
        mock_blob_service = Mock()
        mock_blob_service.get_container_client.return_value = mock_container_client

        # Mock blob list with dealer folders - create proper Mock objects with name attribute
        mock_blob1 = Mock()
        mock_blob1.name = 'RO_Time/AO_RO_TIME/ao_ro_time-decompressed/file1.csv'
        mock_blob2 = Mock()
        mock_blob2.name = 'RO_Time/BMWL_RO_TIME/bmwl_ro_time-decompressed/file2.csv'
        mock_blob3 = Mock()
        mock_blob3.name = 'RO_Time/other_folder/file3.csv'  # Should be ignored

        mock_blobs = [mock_blob1, mock_blob2, mock_blob3]
        mock_container_client.list_blobs.return_value = mock_blobs

        # Mock config and job config
        mock_config = Mock()
        mock_config.source_folder_path = 'RO_Time/decompressed'

        mock_job_config = {
            'DealerPrefix': True,
            'DealerZipArchivingEnabled': '<EMAIL>'
        }

        with patch('azure_config.get_job_config') as mock_get_job_config, \
             patch('azure_config.Config') as mock_config_class, \
             patch('azure_config.get_source_container') as mock_get_container, \
             patch('azure_config.get_dealer_zip_archiving_enabled') as mock_enabled, \
             patch('azure_config.blob_service_client', mock_blob_service):

            mock_get_job_config.return_value = mock_job_config
            mock_config_class.return_value = mock_config
            mock_get_container.return_value = 'test-container'
            mock_enabled.return_value = True

            from azure_config import get_dealer_zip_source_folders

            result = get_dealer_zip_source_folders('RO_TIME')

            # Should find two dealer folders
            expected = [
                'RO_Time/AO_RO_TIME/ao_ro_time-compressed',
                'RO_Time/BMWL_RO_TIME/bmwl_ro_time-compressed'
            ]

            # Sort both lists for comparison since order may vary
            assert sorted(result) == sorted(expected)

    def test_dealer_zip_archiving_main_function_dealer_enabled(self):
        """Test main dealer-aware zip archiving function with dealer enabled"""

        with patch('azure_config.get_dealer_zip_archiving_enabled') as mock_dealer_enabled, \
             patch('blob_manager._move_dealer_zip_files_to_archive') as mock_dealer_move:

            mock_dealer_enabled.return_value = True
            mock_dealer_move.return_value = {
                'status': 'success',
                'successfully_moved': 5,
                'total_files': 5
            }

            from blob_manager import move_zip_files_to_archive_dealer_aware

            result = move_zip_files_to_archive_dealer_aware('test-container', 'RO_TIME')

            assert result['status'] == 'success'
            assert result['successfully_moved'] == 5
            mock_dealer_move.assert_called_once_with('test-container', 'RO_TIME')

    def test_dealer_zip_archiving_main_function_standard_enabled(self):
        """Test main dealer-aware zip archiving function with standard archiving"""

        with patch('azure_config.get_dealer_zip_archiving_enabled') as mock_dealer_enabled, \
             patch('azure_config.get_zip_movement_enabled') as mock_standard_enabled, \
             patch('blob_manager.move_zip_files_to_archive') as mock_standard_move, \
             patch('azure_config.get_zip_source_folder') as mock_source, \
             patch('azure_config.get_zip_archive_folder') as mock_archive, \
             patch('azure_config.get_zip_file_pattern') as mock_pattern:

            mock_dealer_enabled.return_value = False
            mock_standard_enabled.return_value = True
            mock_source.return_value = 'FIMAST-SALES/fimastsales-compressed'
            mock_archive.return_value = 'FIMAST-SALES/archive'
            mock_pattern.return_value = 'FIMASTSALES*.zip'
            mock_standard_move.return_value = {
                'status': 'success',
                'successfully_moved': 3,
                'total_files': 3
            }

            from blob_manager import move_zip_files_to_archive_dealer_aware

            result = move_zip_files_to_archive_dealer_aware('test-container', 'FIMASTSALES_')

            assert result['status'] == 'success'
            assert result['successfully_moved'] == 3
            mock_standard_move.assert_called_once()

    def test_dealer_zip_archiving_disabled(self):
        """Test behavior when zip archiving is completely disabled"""

        with patch('azure_config.get_dealer_zip_archiving_enabled') as mock_dealer_enabled, \
             patch('azure_config.get_zip_movement_enabled') as mock_standard_enabled:

            mock_dealer_enabled.return_value = False
            mock_standard_enabled.return_value = False

            from blob_manager import move_zip_files_to_archive_dealer_aware

            result = move_zip_files_to_archive_dealer_aware('test-container', 'TEST_JOB')

            assert result['status'] == 'disabled'
            assert result['successfully_moved'] == 0
            assert 'disabled' in result['message'].lower()

if __name__ == '__main__':
    # Run the tests
    pytest.main([__file__, '-v'])
