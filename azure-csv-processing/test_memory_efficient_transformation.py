#!/usr/bin/env python3
"""
Test script for memory-efficient per-file transformation approach
"""

import sys
import os
import polars as pl
import logging

# Add shared directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'shared'))

from data_transformer import add_surrogate_key_configurable, append_audit_columns_polars, ensure_consistent_schema

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

def test_per_file_transformation():
    """Test the per-file transformation approach"""
    
    print("🧪 Testing Memory-Efficient Per-File Transformation")
    print("=" * 60)
    
    # Create sample data representing different CSV files with varying schemas
    file1_data = pl.DataFrame({
        'ST/BR': ['001', '002', '003'],
        'DEAL NO': ['D001', 'D002', 'D003'],
        'CUSTOMER': ['<PERSON>', '<PERSON>', '<PERSON>'],
        'AMOUNT': [100.0, 200.0, 300.0]
    })
    
    file2_data = pl.DataFrame({
        'ST/BR': ['004', '005'],
        'DEAL NO': ['D004', 'D005'],
        'CUSTOMER': ['Alice', 'Charlie'],
        'AMOUNT': [400.0, 500.0],
        'EXTRA_COLUMN': ['X', 'Y']  # Extra column to test schema alignment
    })
    
    file3_data = pl.DataFrame({
        'ST/BR': ['006'],
        'DEAL NO': ['D006'],
        'CUSTOMER': ['David'],
        # Missing AMOUNT column to test schema alignment
        'NOTES': ['Test note']
    })
    
    files_data = [
        ('file1.csv', file1_data),
        ('file2.csv', file2_data),
        ('file3.csv', file3_data)
    ]
    
    transformed_files = []
    expected_schema = None

    print(f"📁 Processing {len(files_data)} files individually...")

    # Transform each file individually (simulating the new approach)
    for i, (filename, df) in enumerate(files_data, 1):
        print(f"\n📄 Processing file {i}/{len(files_data)}: {filename}")
        print(f"   Original shape: {df.shape}")
        print(f"   Original columns: {df.columns}")

        try:
            # Step 1: Add surrogate key and composite key
            df_with_surrogate = add_surrogate_key_configurable(df, job_key=None)
            print(f"   After surrogate key: {df_with_surrogate.shape}")

            # Step 2: Add audit columns
            df_with_audit = append_audit_columns_polars(df_with_surrogate, [filename])
            print(f"   After audit columns: {df_with_audit.shape}")

            # Step 3: Ensure consistent schema
            df_aligned, expected_schema = ensure_consistent_schema(
                df_with_audit, expected_schema, filename
            )
            print(f"   After schema alignment: {df_aligned.shape}")
            print(f"   Final columns: {df_aligned.columns}")

            transformed_files.append((filename, df_aligned))

        except Exception as e:
            print(f"   ❌ Error processing {filename}: {e}")
            return False

    # Second pass: Re-align all files to the final schema
    print(f"\n🔄 Second pass: Ensuring all files have the same final schema...")
    final_transformed_files = []

    for filename, df in transformed_files:
        df_final, _ = ensure_consistent_schema(df, expected_schema, f"{filename} (final pass)")
        final_transformed_files.append((filename, df_final))
        print(f"   {filename}: {df_final.shape} columns: {len(df_final.columns)}")

    transformed_files = final_transformed_files
    
    print(f"\n✅ Successfully transformed {len(transformed_files)} files")
    
    # Test combining the transformed files
    print(f"\n🔗 Combining transformed files...")
    
    try:
        combined_dfs = [df for _, df in transformed_files]
        combined_df = pl.concat(combined_dfs, how="vertical")
        
        print(f"   Combined shape: {combined_df.shape}")
        print(f"   Combined columns: {combined_df.columns}")
        
        # Verify the structure
        expected_columns = ['Surrogate Key', 'Composite Key']  # Should be first
        audit_columns = ['Source Date/Time', 'Created Date/Time', 'Modified Date/Time']  # Should be last
        
        actual_first_two = combined_df.columns[:2]
        actual_last_three = combined_df.columns[-3:]
        
        if actual_first_two == expected_columns:
            print("   ✅ Surrogate and Composite keys are in correct positions (first)")
        else:
            print(f"   ❌ Key columns not in correct positions. Expected: {expected_columns}, Got: {actual_first_two}")
            
        if actual_last_three == audit_columns:
            print("   ✅ Audit columns are in correct positions (last)")
        else:
            print(f"   ❌ Audit columns not in correct positions. Expected: {audit_columns}, Got: {actual_last_three}")
        
        # Show sample data
        print(f"\n📊 Sample of combined data:")
        print(combined_df.head(3))
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error combining files: {e}")
        return False

def test_memory_efficiency_comparison():
    """Compare memory usage of old vs new approach (conceptual)"""
    
    print(f"\n🧠 Memory Efficiency Analysis")
    print("=" * 40)
    
    print("OLD APPROACH:")
    print("  1. Load all CSV files → High memory usage")
    print("  2. Combine all files → Peak memory usage")
    print("  3. Transform combined data → Still high memory")
    print("  4. Save result")
    
    print("\nNEW APPROACH:")
    print("  1. Load one CSV file → Low memory usage")
    print("  2. Transform single file → Low memory usage")
    print("  3. Save transformed file → Memory freed")
    print("  4. Repeat for each file")
    print("  5. Combine pre-transformed files → Lower peak memory")
    
    print("\n✅ NEW APPROACH BENEFITS:")
    print("  • Peak memory = max(single_file_size) instead of sum(all_files)")
    print("  • Each file processed independently")
    print("  • Failures isolated to individual files")
    print("  • Better scalability for large datasets")

if __name__ == "__main__":
    print("🚀 Memory-Efficient Transformation Test")
    print("=" * 50)
    
    success = test_per_file_transformation()
    
    if success:
        test_memory_efficiency_comparison()
        print(f"\n🎉 All tests passed! Memory-efficient transformation is working correctly.")
    else:
        print(f"\n❌ Tests failed. Check the implementation.")
        sys.exit(1)
