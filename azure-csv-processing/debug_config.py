#!/usr/bin/env python3
"""
Debug script to check what's actually in the sftpConfig Azure Table Storage
"""
import os
import sys

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(current_dir, 'shared')
if shared_dir not in sys.path:
    sys.path.append(shared_dir)

from azure_config import get_job_configs, get_job_config
from blob_manager import list_csv_files_in_container

def main():
    print("🔍 Debugging sftpConfig table contents...")
    
    try:
        # Get all job configurations
        job_configs = get_job_configs()
        
        print(f"\n📋 Found {len(job_configs)} enabled jobs:")

        for job_key, job_config in job_configs.items():
            print(f"\n🔧 Job: {job_key}")
            print(f"   PartitionKey: {job_config.get('PartitionKey', 'NOT_SET')}")
            print(f"   RowKey: {job_config.get('RowKey', 'NOT_SET')}")
            print(f"   isEnabled: {job_config.get('isEnabled', 'NOT_SET')}")
            print(f"   FilePrefix: '{job_config.get('FilePrefix', 'NOT_SET')}'")
            print(f"   Folder: '{job_config.get('Folder', 'NOT_SET')}'")
            print(f"   SourceSubfolder: '{job_config.get('SourceSubfolder', 'NOT_SET')}'")
            print(f"   OutputSubfolder: '{job_config.get('OutputSubfolder', 'NOT_SET')}'")
            print(f"   DealerPrefix: {job_config.get('DealerPrefix', 'NOT_SET')}")

        # Compare with azure-ftp-etl approach
        print(f"\n🔄 Comparing with azure-ftp-etl approach:")
        try:
            # Simulate azure-ftp-etl's get_file_prefixes() approach
            from azure.data.tables import TableServiceClient
            from azure.identity import DefaultAzureCredential

            storage_account_name = "sagedw"
            table_storage_url = f"https://{storage_account_name}.table.core.windows.net"
            credential = DefaultAzureCredential()
            table_service_client = TableServiceClient(endpoint=table_storage_url, credential=credential)
            table_client = table_service_client.get_table_client(table_name="sftpConfig")

            # Query ALL enabled entities (like azure-ftp-etl does)
            entities = table_client.query_entities("isEnabled eq true")
            all_prefixes = []
            for e in entities:
                fp = (e.get("FilePrefix") or "").strip()
                if fp:
                    all_prefixes.append(fp)

            print(f"   azure-ftp-etl would find {len(all_prefixes)} FilePrefix values:")
            for prefix in all_prefixes:
                print(f"     - '{prefix}'")

        except Exception as e:
            print(f"   ❌ Error simulating azure-ftp-etl approach: {e}")
        
        # Specifically check CUSTOMER-ACCTG_ job
        print(f"\n🎯 Specific check for CUSTOMER-ACCTG_ job:")
        customer_config = get_job_config('CUSTOMER-ACCTG_')
        if customer_config:
            print(f"   Found config for CUSTOMER-ACCTG_")
            file_prefix = customer_config.get('FilePrefix', 'NOT_SET')
            print(f"   FilePrefix: '{file_prefix}'")

            # Test the pattern matching logic
            test_filenames = [
                'EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv',
                'EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv',
                'EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv'
            ]

            print(f"\n🧪 Testing pattern matching:")
            print(f"   Pattern: '{file_prefix}'")
            for filename in test_filenames:
                matches = filename.startswith(file_prefix)
                print(f"   '{filename}' starts with pattern: {matches}")

            # Now test actual blob storage
            print(f"\n🔍 Testing actual blob storage:")
            source_folder = f"{customer_config.get('Folder', '')}/{customer_config.get('SourceSubfolder', '')}".strip('/')
            print(f"   Looking in: sftp-landing/{source_folder}")
            print(f"   Using file_patterns: ['{file_prefix}']")

            try:
                csv_files = list_csv_files_in_container(
                    container_name='sftp-landing',
                    file_patterns=[file_prefix],
                    folder_path=source_folder,
                    job_key='CUSTOMER-ACCTG_'
                )
                print(f"   Found {len(csv_files)} CSV files:")
                for csv_file in csv_files:
                    print(f"     - {csv_file}")
            except Exception as e:
                print(f"   ❌ Error listing files: {e}")

        else:
            print(f"   ❌ No config found for CUSTOMER-ACCTG_")

            # Try without underscore
            customer_config_no_underscore = get_job_config('CUSTOMER-ACCTG')
            if customer_config_no_underscore:
                print(f"   Found config for CUSTOMER-ACCTG (without underscore)")
                print(f"   FilePrefix: '{customer_config_no_underscore.get('FilePrefix', 'NOT_SET')}'")
            else:
                print(f"   ❌ No config found for CUSTOMER-ACCTG either")
        
    except Exception as e:
        print(f"❌ Error accessing sftpConfig table: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
