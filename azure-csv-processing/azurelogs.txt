2025-09-21T17:15:31Z   [Verbose]   AuthenticationScheme: WebJobsAuthLevel was successfully authenticated.
2025-09-21T17:15:31Z   [Verbose]   Authorization was successful.
2025-09-21T17:15:31Z   [Information]   Executing 'Functions.CSVProcessingPipeline' (Reason='This function was programmatically called via the host APIs.', Id=9a0f58dd-25c2-4c7b-93e1-45007e67ea18)
2025-09-21T17:15:31Z   [Verbose]   Sending invocation id: '9a0f58dd-25c2-4c7b-93e1-45007e67ea18
2025-09-21T17:15:31Z   [Verbose]   Posting invocation id:9a0f58dd-25c2-4c7b-93e1-45007e67ea18 on workerId:3c52e2a8-833a-446d-aa19-8703109fd72f
2025-09-21T17:15:31Z   [Information]   CSV Processing Pipeline triggered
2025-09-21T17:15:31Z   [Information]   Starting CSV processing pipeline for container: None
2025-09-21T17:15:31Z   [Information]   🚀 Starting dynamic job processing based on Azure Table Storage configuration
2025-09-21T17:15:31Z   [Information]   🚀 Starting dynamic job processing based on Azure Table Storage configuration
2025-09-21T17:15:37Z   [Information]   Configuration cache refreshed. Found 1 enabled jobs.
2025-09-21T17:15:37Z   [Information]   🔍 Job CUSTOMER-ACCTG_: FilePrefix = 'EDW-CUSTOMER-ACCTG_'
2025-09-21T17:15:37Z   [Information]   📋 Found 1 enabled jobs: ['CUSTOMER-ACCTG_']
2025-09-21T17:15:37Z   [Information]   🔄 Processing job: CUSTOMER-ACCTG_
2025-09-21T17:15:37Z   [Information]   📁 Job CUSTOMER-ACCTG_: sftp-landing/CUSTOMER-ACCTG/edw-customer-acctg-decompressed → sftp-landing/CUSTOMER-ACCTG/edw-customer-acctg-enhanced
2025-09-21T17:15:37Z   [Information]   🔍 Job CUSTOMER-ACCTG_: Using FilePrefix = 'EDW-CUSTOMER-ACCTG_' for file pattern matching
2025-09-21T17:15:37Z   [Information]   🔄 Processing job CUSTOMER-ACCTG_: sftp-landing/CUSTOMER-ACCTG/edw-customer-acctg-decompressed → sftp-landing/CUSTOMER-ACCTG/edw-customer-acctg-enhanced
2025-09-21T17:15:38Z   [Information]   Total Parquet files found: 0
2025-09-21T17:15:38Z   [Information]   Using standard structure for job: CUSTOMER-ACCTG_
2025-09-21T17:15:38Z   [Information]   🔍 Checking file 'EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv' against patterns: ['EDW-CUSTOMER-ACCTG_']
2025-09-21T17:15:38Z   [Information]   🔍 File 'EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv' matches pattern: True
2025-09-21T17:15:38Z   [Information]   ✅ Added file to processing list: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv
2025-09-21T17:15:38Z   [Information]   🔍 Checking file 'EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv' against patterns: ['EDW-CUSTOMER-ACCTG_']
2025-09-21T17:15:38Z   [Information]   🔍 File 'EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv' matches pattern: True
2025-09-21T17:15:38Z   [Information]   ✅ Added file to processing list: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv
2025-09-21T17:15:38Z   [Information]   🔍 Checking file 'EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv' against patterns: ['EDW-CUSTOMER-ACCTG_']
2025-09-21T17:15:38Z   [Information]   🔍 File 'EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv' matches pattern: True
2025-09-21T17:15:38Z   [Information]   ✅ Added file to processing list: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv
2025-09-21T17:15:38Z   [Information]   Total CSV files found: 3
2025-09-21T17:15:38Z   [Information]   ✓ Found 3 CSV files, using hybrid CSV-to-Parquet pipeline
2025-09-21T17:15:38Z   [Information]   🚀 Starting CSV-to-Parquet Hybrid Pipeline...
2025-09-21T17:15:38Z   [Information]   ================================================================================
2025-09-21T17:15:38Z   [Information]   📊 STANDARD PROCESSING: Consolidating multiple files
2025-09-21T17:15:38Z   [Information]   ============================================================
2025-09-21T17:15:38Z   [Information]   Found 3 CSV files to process
2025-09-21T17:15:38Z   [Information]   🔄 Converting and transforming 3 CSV files to Parquet individually...
2025-09-21T17:15:38Z   [Information]   🧠 MEMORY-EFFICIENT APPROACH: Transform each file before combining
2025-09-21T17:15:38Z   [Information]   📋 Phase 1: Schema discovery across all files...
2025-09-21T17:15:38Z   [Information]   🔍 Discovering schema for file 1/3: EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv
2025-09-21T17:15:38Z   [Information]   📋 Reading only header and sample rows for schema discovery...
2025-09-21T17:15:38Z   [Information]   📋 LIGHTWEIGHT SCHEMA DISCOVERY: Reading only 10 sample rows from CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv
2025-09-21T17:15:38Z   [Information]   📊 Blob size: 236.45 MB (will only read header + 10 rows)
2025-09-21T17:15:38Z   [Error]   ❌ Lightweight schema discovery failed for CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv: could not parse `"SXXVXXXC` as dtype `str` at column 'CUST-TYPE' (column number 2)

The current offset in the file is 7 bytes.

You might want to try:
- increasing `infer_schema_length` (e.g. `infer_schema_length=10000`),
- specifying correct dtype with the `schema_overrides` argument
- setting `ignore_errors` to `True`,
- adding `"SXXVXXXC` to the `null_values` list.

Original error: ```invalid csv file

Field `"SXXVXXXC` is not properly escaped.```
2025-09-21T17:15:38Z   [Information]   🔄 Falling back to regular download for schema discovery...
2025-09-21T17:15:38Z   [Information]   📥 Downloading CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv (236.5 MB)
2025-09-21T17:15:38Z   [Information]   Using streaming approach for large file
2025-09-21T17:15:38Z   [Information]   Streaming large CSV from blob: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv
2025-09-21T17:15:38Z   [Information]   🧠 Memory usage before streaming download: 114.3 MB
2025-09-21T17:15:38Z   [Information]   Blob size: 236.45 MB
2025-09-21T17:15:38Z   [Information]   Using chunked processing for very large file
2025-09-21T17:15:38Z   [Information]   Using chunked processing for extremely large CSV: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv
2025-09-21T17:15:38Z   [Information]   Processing 236.45 MB file in chunks
2025-09-21T17:15:39Z   [Information]   Downloading in chunks...
2025-09-21T17:15:41Z   [Information]   Downloaded 100.0 MB / 236.5 MB (42.3%)
2025-09-21T17:15:44Z   [Information]   Downloaded 200.0 MB / 236.5 MB (84.6%)
2025-09-21T17:15:45Z   [Information]   ✓ Download complete: 247940923 bytes
2025-09-21T17:15:45Z   [Information]   Reading CSV with lazy evaluation...
2025-09-21T17:15:45Z   [Information]   Collecting data with streaming...
2025-09-21T17:15:48Z   [Information]   ✓ Chunked processing complete: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv - Shape: (1270096, 23)
2025-09-21T17:15:48Z   [Information]   ✓ Temporary file cleaned up
2025-09-21T17:15:48Z   [Information]   ✓ Schema sample loaded: (10, 23) (lightweight)
2025-09-21T17:15:48Z   [Information]   === ADDING SURROGATE KEY & COMPOSITE KEY (CONFIGURABLE) ===
2025-09-21T17:15:48Z   [Information]   Original DataFrame shape: (10, 23)
2025-09-21T17:15:48Z   [Information]   All columns: ['CUST-NO', 'CUST-TYPE', 'FIRST-NAME', 'MIDDLE-NAME', 'LAST-NAME', 'FULL-NAME', 'CUST-BDATE', 'ADDR1-CAT', 'ADDR2-CAT', 'CTY', 'ST', 'CNTY', 'COUNTRY', 'ZIP', 'PHONE-NO-RES', 'PHONE-NO-BUS', 'EMAIL-ADDR', 'SEX', 'IBFLAG', 'OPT-OUT', 'DLR-CONTACT-CALL', 'DLR-CONTACT-EMAIL', 'DLR-CONTACT-MAIL']
2025-09-21T17:15:48Z   [Information]   Surrogate key configuration for job 'CUSTOMER-ACCTG_':
2025-09-21T17:15:48Z   [Information]     - Columns: CUST-NO,,,
2025-09-21T17:15:48Z   [Information]     - Separator: '|'
2025-09-21T17:15:48Z   [Information]     - Hash method: MD5
2025-09-21T17:15:48Z   [Information]   Parsed columns: ['CUST-NO', '', '', '']
2025-09-21T17:15:48Z   [Information]   Surrogate key logic: MD5 hash of 'CUST-NO,,,' with separator '|'
2025-09-21T17:15:48Z   [Information]   Composite key logic: Cleaned concatenated string (trailing empty values removed)
2025-09-21T17:15:48Z   [Information]   Sample surrogate key values (MD5): ['ac4c38f527c97d394be4f9dc0db294d2', 'a5179ce1beed1fb0ca527764e5fd259c', '8bdff0c86471443b8e43e627eba2fa72']
2025-09-21T17:15:48Z   [Information]   Sample composite key values (cleaned): ['1', '2', '3']
2025-09-21T17:15:48Z   [Information]   Sample full concatenated strings (for hashing): ['1|||', '2|||', '3|||']
2025-09-21T17:15:48Z   [Information]   ✓ Surrogate Key and Composite Key added - New shape: (10, 25)
2025-09-21T17:15:48Z   [Information]   === APPENDING AUDIT COLUMNS (POLARS) ===
2025-09-21T17:15:48Z   [Information]     Extracting file info from: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv
2025-09-21T17:15:48Z   [Information]     Pattern 1 matched: ('CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1', '09', '19', '25', '05', '54', '25')
2025-09-21T17:15:48Z   [Information]     Successfully extracted datetime: 09/19/2025 05:54
2025-09-21T17:15:48Z   [Information]   ✓ Audit columns appended - Final shape: (10, 28)
2025-09-21T17:15:48Z   [Information]   🔧 Ensuring consistent schema for file: EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv (schema discovery)
2025-09-21T17:15:48Z   [Information]   📋 Using current schema as baseline: ['Surrogate Key', 'Composite Key', 'CUST-NO', 'CUST-TYPE', 'FIRST-NAME', 'MIDDLE-NAME', 'LAST-NAME', 'FULL-NAME', 'CUST-BDATE', 'ADDR1-CAT', 'ADDR2-CAT', 'CTY', 'ST', 'CNTY', 'COUNTRY', 'ZIP', 'PHONE-NO-RES', 'PHONE-NO-BUS', 'EMAIL-ADDR', 'SEX', 'IBFLAG', 'OPT-OUT', 'DLR-CONTACT-CALL', 'DLR-CONTACT-EMAIL', 'DLR-CONTACT-MAIL', 'Source Date/Time', 'Created Date/Time', 'Modified Date/Time']
2025-09-21T17:15:48Z   [Information]   🔍 Discovering schema for file 2/3: EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv
2025-09-21T17:15:48Z   [Information]   📋 Reading only header and sample rows for schema discovery...
2025-09-21T17:15:48Z   [Information]   📋 LIGHTWEIGHT SCHEMA DISCOVERY: Reading only 10 sample rows from CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv
2025-09-21T17:15:48Z   [Information]   📊 Blob size: 78.45 MB (will only read header + 10 rows)
2025-09-21T17:15:48Z   [Error]   ❌ Lightweight schema discovery failed for CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv: could not parse `"AUD` as dtype `str` at column 'FULL-NAME' (column number 6)

The current offset in the file is 53 bytes.

You might want to try:
- increasing `infer_schema_length` (e.g. `infer_schema_length=10000`),
- specifying correct dtype with the `schema_overrides` argument
- setting `ignore_errors` to `True`,
- adding `"AUD` to the `null_values` list.

Original error: ```invalid csv file

Field `"AUD` is not properly escaped.```
2025-09-21T17:15:48Z   [Information]   🔄 Falling back to regular download for schema discovery...
2025-09-21T17:15:48Z   [Information]   📥 Downloading CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv (78.4 MB)
2025-09-21T17:15:48Z   [Information]   Using streaming approach for large file
2025-09-21T17:15:48Z   [Information]   Streaming large CSV from blob: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv
2025-09-21T17:15:48Z   [Information]   🧠 Memory usage before streaming download: 701.9 MB
2025-09-21T17:15:48Z   [Information]   Blob size: 78.45 MB
2025-09-21T17:15:48Z   [Information]   Using optimized streaming for large file
2025-09-21T17:15:50Z   [Information]   ✓ Downloaded to temporary file: 82257756 bytes
2025-09-21T17:15:53Z   [Information]   ✓ Streamed CSV: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv - Shape: (433706, 23)
2025-09-21T17:15:53Z   [Information]   ✓ Temporary file cleaned up
2025-09-21T17:15:53Z   [Information]   ✓ Schema sample loaded: (10, 23) (lightweight)
2025-09-21T17:15:53Z   [Information]   === ADDING SURROGATE KEY & COMPOSITE KEY (CONFIGURABLE) ===
2025-09-21T17:15:53Z   [Information]   Original DataFrame shape: (10, 23)
2025-09-21T17:15:53Z   [Information]   All columns: ['CUST-NO', 'CUST-TYPE', 'FIRST-NAME', 'MIDDLE-NAME', 'LAST-NAME', 'FULL-NAME', 'CUST-BDATE', 'ADDR1-CAT', 'ADDR2-CAT', 'CTY', 'ST', 'CNTY', 'COUNTRY', 'ZIP', 'PHONE-NO-RES', 'PHONE-NO-BUS', 'EMAIL-ADDR', 'SEX', 'IBFLAG', 'OPT-OUT', 'DLR-CONTACT-CALL', 'DLR-CONTACT-EMAIL', 'DLR-CONTACT-MAIL']
2025-09-21T17:15:53Z   [Information]   Surrogate key configuration for job 'CUSTOMER-ACCTG_':
2025-09-21T17:15:53Z   [Information]     - Columns: CUST-NO,,,
2025-09-21T17:15:53Z   [Information]     - Separator: '|'
2025-09-21T17:15:53Z   [Information]     - Hash method: MD5
2025-09-21T17:15:53Z   [Information]   Parsed columns: ['CUST-NO', '', '', '']
2025-09-21T17:15:53Z   [Information]   Surrogate key logic: MD5 hash of 'CUST-NO,,,' with separator '|'
2025-09-21T17:15:53Z   [Information]   Composite key logic: Cleaned concatenated string (trailing empty values removed)
2025-09-21T17:15:53Z   [Information]   Sample surrogate key values (MD5): ['ac4c38f527c97d394be4f9dc0db294d2', 'ac4c38f527c97d394be4f9dc0db294d2', 'a5179ce1beed1fb0ca527764e5fd259c']
2025-09-21T17:15:53Z   [Information]   Sample composite key values (cleaned): ['1', '1', '2']
2025-09-21T17:15:53Z   [Information]   Sample full concatenated strings (for hashing): ['1|||', '1|||', '2|||']
2025-09-21T17:15:53Z   [Information]   ✓ Surrogate Key and Composite Key added - New shape: (10, 25)
2025-09-21T17:15:53Z   [Information]   === APPENDING AUDIT COLUMNS (POLARS) ===
2025-09-21T17:15:53Z   [Information]     Extracting file info from: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv
2025-09-21T17:15:53Z   [Information]     Pattern 1 matched: ('CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2', '09', '19', '25', '06', '09', '06')
2025-09-21T17:15:53Z   [Information]     Successfully extracted datetime: 09/19/2025 06:09
2025-09-21T17:15:53Z   [Information]   ✓ Audit columns appended - Final shape: (10, 28)
2025-09-21T17:15:53Z   [Information]   🔧 Ensuring consistent schema for file: EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv (schema discovery)
2025-09-21T17:15:53Z   [Information]   ✓ Schema alignment completed for EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv (schema discovery): (10, 28)
2025-09-21T17:15:53Z   [Information]   🔍 Discovering schema for file 3/3: EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv
2025-09-21T17:15:53Z   [Information]   📋 Reading only header and sample rows for schema discovery...
2025-09-21T17:15:53Z   [Information]   📋 LIGHTWEIGHT SCHEMA DISCOVERY: Reading only 10 sample rows from CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv
2025-09-21T17:15:53Z   [Information]   📊 Blob size: 237.00 MB (will only read header + 10 rows)
2025-09-21T17:15:53Z   [Information]   ✅ LIGHTWEIGHT schema discovery complete: (10, 23) (only 10 rows)
2025-09-21T17:15:53Z   [Information]   🔍 Discovered columns: ['CUST-NO', 'CUST-TYPE', 'FIRST-NAME', 'MIDDLE-NAME', 'LAST-NAME', 'FULL-NAME', 'CUST-BDATE', 'ADDR1-CAT', 'ADDR2-CAT', 'CTY', 'ST', 'CNTY', 'COUNTRY', 'ZIP', 'PHONE-NO-RES', 'PHONE-NO-BUS', 'EMAIL-ADDR', 'SEX', 'IBFLAG', 'OPT-OUT', 'DLR-CONTACT-CALL', 'DLR-CONTACT-EMAIL', 'DLR-CONTACT-MAIL']
2025-09-21T17:15:53Z   [Information]   ✓ Schema sample loaded: (10, 23) (lightweight)
2025-09-21T17:15:53Z   [Information]   === ADDING SURROGATE KEY & COMPOSITE KEY (CONFIGURABLE) ===
2025-09-21T17:15:53Z   [Information]   Original DataFrame shape: (10, 23)
2025-09-21T17:15:53Z   [Information]   All columns: ['CUST-NO', 'CUST-TYPE', 'FIRST-NAME', 'MIDDLE-NAME', 'LAST-NAME', 'FULL-NAME', 'CUST-BDATE', 'ADDR1-CAT', 'ADDR2-CAT', 'CTY', 'ST', 'CNTY', 'COUNTRY', 'ZIP', 'PHONE-NO-RES', 'PHONE-NO-BUS', 'EMAIL-ADDR', 'SEX', 'IBFLAG', 'OPT-OUT', 'DLR-CONTACT-CALL', 'DLR-CONTACT-EMAIL', 'DLR-CONTACT-MAIL']
2025-09-21T17:15:53Z   [Information]   Surrogate key configuration for job 'CUSTOMER-ACCTG_':
2025-09-21T17:15:53Z   [Information]     - Columns: CUST-NO,,,
2025-09-21T17:15:53Z   [Information]     - Separator: '|'
2025-09-21T17:15:53Z   [Information]     - Hash method: MD5
2025-09-21T17:15:53Z   [Information]   Parsed columns: ['CUST-NO', '', '', '']
2025-09-21T17:15:53Z   [Information]   Surrogate key logic: MD5 hash of 'CUST-NO,,,' with separator '|'
2025-09-21T17:15:53Z   [Information]   Composite key logic: Cleaned concatenated string (trailing empty values removed)
2025-09-21T17:15:53Z   [Information]   Sample surrogate key values (MD5): ['ac4c38f527c97d394be4f9dc0db294d2', 'ac4c38f527c97d394be4f9dc0db294d2', 'a5179ce1beed1fb0ca527764e5fd259c']
2025-09-21T17:15:53Z   [Information]   Sample composite key values (cleaned): ['1', '1', '2']
2025-09-21T17:15:53Z   [Information]   Sample full concatenated strings (for hashing): ['1|||', '1|||', '2|||']
2025-09-21T17:15:53Z   [Information]   ✓ Surrogate Key and Composite Key added - New shape: (10, 25)
2025-09-21T17:15:53Z   [Information]   === APPENDING AUDIT COLUMNS (POLARS) ===
2025-09-21T17:15:53Z   [Information]     Extracting file info from: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv
2025-09-21T17:15:53Z   [Information]     Pattern 1 matched: ('CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_3', '09', '19', '25', '06', '14', '28')
2025-09-21T17:15:53Z   [Information]     Successfully extracted datetime: 09/19/2025 06:14
2025-09-21T17:15:53Z   [Information]   ✓ Audit columns appended - Final shape: (10, 28)
2025-09-21T17:15:53Z   [Information]   🔧 Ensuring consistent schema for file: EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv (schema discovery)
2025-09-21T17:15:53Z   [Information]   🔄 Converting PHONE-NO-BUS from String to Int64 in EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv (schema discovery)
2025-09-21T17:15:54Z   [Warning]   ⚠️ Could not cast PHONE-NO-BUS to Int64: conversion from `str` to `i64` failed in column 'PHONE-NO-BUS' for 4 out of 10 values: ["", "", … ""]
2025-09-21T17:15:54Z   [Information]   ✓ Schema alignment completed for EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv (schema discovery): (10, 28)
2025-09-21T17:15:54Z   [Information]   ✅ Schema discovery completed. Final schema has 28 columns
2025-09-21T17:15:54Z   [Information]   📋 Final column order: ['Surrogate Key', 'Composite Key', 'CUST-NO', 'CUST-TYPE', 'FIRST-NAME', 'MIDDLE-NAME', 'LAST-NAME', 'FULL-NAME', 'CUST-BDATE', 'ADDR1-CAT', 'ADDR2-CAT', 'CTY', 'ST', 'CNTY', 'COUNTRY', 'ZIP', 'PHONE-NO-RES', 'PHONE-NO-BUS', 'EMAIL-ADDR', 'SEX', 'IBFLAG', 'OPT-OUT', 'DLR-CONTACT-CALL', 'DLR-CONTACT-EMAIL', 'DLR-CONTACT-MAIL', 'Source Date/Time', 'Created Date/Time', 'Modified Date/Time']
2025-09-21T17:15:54Z   [Information]   🔄 Phase 2: Transform and upload files with consistent schema...
2025-09-21T17:15:54Z   [Information]   📄 Processing CSV file 1/3: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv
2025-09-21T17:15:54Z   [Information]   🧠 Memory usage before processing file 1/3: 728.9 MB
2025-09-21T17:15:54Z   [Information]   📥 Downloading CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv (236.5 MB)
2025-09-21T17:15:54Z   [Information]   Using streaming approach for large file
2025-09-21T17:15:54Z   [Information]   Streaming large CSV from blob: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv
2025-09-21T17:15:54Z   [Information]   🧠 Memory usage before streaming download: 728.9 MB
2025-09-21T17:15:54Z   [Information]   Blob size: 236.45 MB
2025-09-21T17:15:54Z   [Information]   Using chunked processing for very large file
2025-09-21T17:15:54Z   [Information]   Using chunked processing for extremely large CSV: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv
2025-09-21T17:15:54Z   [Information]   Processing 236.45 MB file in chunks
2025-09-21T17:15:54Z   [Information]   Downloading in chunks...
2025-09-21T17:15:57Z   [Information]   Downloaded 100.0 MB / 236.5 MB (42.3%)
2025-09-21T17:16:00Z   [Information]   Downloaded 200.0 MB / 236.5 MB (84.6%)
2025-09-21T17:16:01Z   [Information]   ✓ Download complete: 247940923 bytes
2025-09-21T17:16:01Z   [Information]   Reading CSV with lazy evaluation...
2025-09-21T17:16:01Z   [Information]   Collecting data with streaming...
2025-09-21T17:16:03Z   [Information]   ✓ Chunked processing complete: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv - Shape: (1270096, 23)
2025-09-21T17:16:03Z   [Information]   ✓ Temporary file cleaned up
2025-09-21T17:16:03Z   [Information]   ✓ Loaded CSV: (1270096, 23)
2025-09-21T17:16:03Z   [Information]   🧠 Memory usage after loading file 1/3: 728.5 MB
2025-09-21T17:16:03Z   [Information]   🔑 Adding surrogate key and composite key to file 1/3
2025-09-21T17:16:03Z   [Information]   === ADDING SURROGATE KEY & COMPOSITE KEY (CONFIGURABLE) ===
2025-09-21T17:16:03Z   [Information]   Original DataFrame shape: (1270096, 23)
2025-09-21T17:16:03Z   [Information]   All columns: ['CUST-NO', 'CUST-TYPE', 'FIRST-NAME', 'MIDDLE-NAME', 'LAST-NAME', 'FULL-NAME', 'CUST-BDATE', 'ADDR1-CAT', 'ADDR2-CAT', 'CTY', 'ST', 'CNTY', 'COUNTRY', 'ZIP', 'PHONE-NO-RES', 'PHONE-NO-BUS', 'EMAIL-ADDR', 'SEX', 'IBFLAG', 'OPT-OUT', 'DLR-CONTACT-CALL', 'DLR-CONTACT-EMAIL', 'DLR-CONTACT-MAIL']
2025-09-21T17:16:03Z   [Information]   Surrogate key configuration for job 'CUSTOMER-ACCTG_':
2025-09-21T17:16:03Z   [Information]     - Columns: CUST-NO,,,
2025-09-21T17:16:03Z   [Information]     - Separator: '|'
2025-09-21T17:16:03Z   [Information]     - Hash method: MD5
2025-09-21T17:16:03Z   [Information]   Parsed columns: ['CUST-NO', '', '', '']
2025-09-21T17:16:06Z   [Information]   Surrogate key logic: MD5 hash of 'CUST-NO,,,' with separator '|'
2025-09-21T17:16:06Z   [Information]   Composite key logic: Cleaned concatenated string (trailing empty values removed)
2025-09-21T17:16:06Z   [Information]   Sample surrogate key values (MD5): ['ac4c38f527c97d394be4f9dc0db294d2', 'a5179ce1beed1fb0ca527764e5fd259c', '8bdff0c86471443b8e43e627eba2fa72']
2025-09-21T17:16:06Z   [Information]   Sample composite key values (cleaned): ['1', '2', '3']
2025-09-21T17:16:06Z   [Information]   Sample full concatenated strings (for hashing): ['1|||', '2|||', '3|||']
2025-09-21T17:16:06Z   [Information]   ✓ Surrogate Key and Composite Key added - New shape: (1270096, 25)
2025-09-21T17:16:06Z   [Information]   ✓ Surrogate key added: (1270096, 25)
2025-09-21T17:16:06Z   [Information]   🧠 Memory usage after surrogate key for file 1/3: 877.9 MB
2025-09-21T17:16:06Z   [Warning]   ⚠️ High memory usage detected: 877.9 MB - forcing garbage collection
2025-09-21T17:16:06Z   [Information]   📅 Adding audit columns to file 1/3
2025-09-21T17:16:06Z   [Information]   === APPENDING AUDIT COLUMNS (POLARS) ===
2025-09-21T17:16:06Z   [Information]     Extracting file info from: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv
2025-09-21T17:16:06Z   [Information]     Pattern 1 matched: ('CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_1', '09', '19', '25', '05', '54', '25')
2025-09-21T17:16:06Z   [Information]     Successfully extracted datetime: 09/19/2025 05:54
2025-09-21T17:16:06Z   [Information]   ✓ Audit columns appended - Final shape: (1270096, 28)
2025-09-21T17:16:06Z   [Information]   ✓ Audit columns added: (1270096, 28)
2025-09-21T17:16:06Z   [Information]   🧠 Memory usage after audit columns for file 1/3: 918.8 MB
2025-09-21T17:16:06Z   [Warning]   ⚠️ High memory usage detected: 918.8 MB - forcing garbage collection
2025-09-21T17:16:06Z   [Information]   🔧 Ensuring consistent schema for file: EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv
2025-09-21T17:16:06Z   [Information]   ✓ Schema alignment completed for EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.csv: (1270096, 28)
2025-09-21T17:16:06Z   [Information]   ✓ Schema alignment completed: (1270096, 28)
2025-09-21T17:16:06Z   [Information]   🧠 Memory usage after schema alignment for file 1/3: 918.8 MB
2025-09-21T17:16:06Z   [Warning]   ⚠️ High memory usage detected: 918.8 MB - forcing garbage collection
2025-09-21T17:16:06Z   [Information]   📤 Uploading Parquet: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.parquet (1,270,096 rows)
2025-09-21T17:16:06Z   [Information]   Ensuring destination folder exists: CUSTOMER-ACCTG/edw-customer-acctg-enhanced
2025-09-21T17:16:06Z   [Information]   Creating destination folder structure with placeholder: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/.placeholder
2025-09-21T17:16:06Z   [Information]   ✓ Destination folder created: CUSTOMER-ACCTG/edw-customer-acctg-enhanced
2025-09-21T17:16:06Z   [Information]   ⚠️ Large DataFrame detected (1,270,096 rows) - using memory-efficient upload
2025-09-21T17:16:06Z   [Information]   🚀 Starting memory-efficient Parquet upload for large DataFrame
2025-09-21T17:16:06Z   [Information]   📊 DataFrame info: 1,270,096 rows, 28 columns
2025-09-21T17:16:06Z   [Information]   📝 Writing DataFrame to temporary file with streaming...
2025-09-21T17:16:09Z   [Information]   📝 Temporary Parquet file size: 144910061 bytes
2025-09-21T17:16:09Z   [Information]   🚀 Starting chunked blob upload...
2025-09-21T17:16:09Z   [Information]   ✅ Container sftp-landing already exists
2025-09-21T17:16:11Z   [Information]   ✅ Successfully uploaded large Parquet file: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.parquet
2025-09-21T17:16:11Z   [Information]   ✅ Verification - Blob size: 144910061 bytes
2025-09-21T17:16:11Z   [Information]   🎉 SUCCESS: Uploaded Parquet: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.parquet - Size: 144910061 bytes
2025-09-21T17:16:11Z   [Information]   🧹 Temporary file cleaned up
2025-09-21T17:16:11Z   [Information]   ✓ Transformed and uploaded: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.parquet
2025-09-21T17:16:11Z   [Information]   🧠 Memory usage after cleanup for file 1/3: 933.9 MB
2025-09-21T17:16:11Z   [Warning]   ⚠️ High memory usage detected: 933.9 MB - forcing garbage collection
2025-09-21T17:16:11Z   [Information]   📄 Processing CSV file 2/3: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv
2025-09-21T17:16:11Z   [Information]   🧠 Memory usage before processing file 2/3: 933.9 MB
2025-09-21T17:16:11Z   [Warning]   ⚠️ High memory usage detected: 933.9 MB - forcing garbage collection
2025-09-21T17:16:11Z   [Information]   📥 Downloading CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv (78.4 MB)
2025-09-21T17:16:11Z   [Information]   Using streaming approach for large file
2025-09-21T17:16:11Z   [Information]   Streaming large CSV from blob: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv
2025-09-21T17:16:11Z   [Information]   🧠 Memory usage before streaming download: 933.9 MB
2025-09-21T17:16:11Z   [Warning]   ⚠️ High memory usage detected: 933.9 MB - forcing garbage collection
2025-09-21T17:16:11Z   [Information]   Blob size: 78.45 MB
2025-09-21T17:16:11Z   [Information]   Using optimized streaming for large file
2025-09-21T17:16:14Z   [Information]   ✓ Downloaded to temporary file: 82257756 bytes
2025-09-21T17:16:17Z   [Information]   ✓ Streamed CSV: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv - Shape: (433706, 23)
2025-09-21T17:16:17Z   [Information]   ✓ Temporary file cleaned up
2025-09-21T17:16:17Z   [Information]   ✓ Loaded CSV: (433706, 23)
2025-09-21T17:16:17Z   [Information]   🧠 Memory usage after loading file 2/3: 1011.3 MB
2025-09-21T17:16:17Z   [Warning]   ⚠️ High memory usage detected: 1011.3 MB - forcing garbage collection
2025-09-21T17:16:17Z   [Information]   🔑 Adding surrogate key and composite key to file 2/3
2025-09-21T17:16:17Z   [Information]   === ADDING SURROGATE KEY & COMPOSITE KEY (CONFIGURABLE) ===
2025-09-21T17:16:17Z   [Information]   Original DataFrame shape: (433706, 23)
2025-09-21T17:16:17Z   [Information]   All columns: ['CUST-NO', 'CUST-TYPE', 'FIRST-NAME', 'MIDDLE-NAME', 'LAST-NAME', 'FULL-NAME', 'CUST-BDATE', 'ADDR1-CAT', 'ADDR2-CAT', 'CTY', 'ST', 'CNTY', 'COUNTRY', 'ZIP', 'PHONE-NO-RES', 'PHONE-NO-BUS', 'EMAIL-ADDR', 'SEX', 'IBFLAG', 'OPT-OUT', 'DLR-CONTACT-CALL', 'DLR-CONTACT-EMAIL', 'DLR-CONTACT-MAIL']
2025-09-21T17:16:17Z   [Information]   Surrogate key configuration for job 'CUSTOMER-ACCTG_':
2025-09-21T17:16:17Z   [Information]     - Columns: CUST-NO,,,
2025-09-21T17:16:17Z   [Information]     - Separator: '|'
2025-09-21T17:16:17Z   [Information]     - Hash method: MD5
2025-09-21T17:16:17Z   [Information]   Parsed columns: ['CUST-NO', '', '', '']
2025-09-21T17:16:17Z   [Information]   Surrogate key logic: MD5 hash of 'CUST-NO,,,' with separator '|'
2025-09-21T17:16:17Z   [Information]   Composite key logic: Cleaned concatenated string (trailing empty values removed)
2025-09-21T17:16:17Z   [Information]   Sample surrogate key values (MD5): ['ac4c38f527c97d394be4f9dc0db294d2', 'ac4c38f527c97d394be4f9dc0db294d2', 'a5179ce1beed1fb0ca527764e5fd259c']
2025-09-21T17:16:17Z   [Information]   Sample composite key values (cleaned): ['1', '1', '2']
2025-09-21T17:16:17Z   [Information]   Sample full concatenated strings (for hashing): ['1|||', '1|||', '2|||']
2025-09-21T17:16:17Z   [Information]   ✓ Surrogate Key and Composite Key added - New shape: (433706, 25)
2025-09-21T17:16:17Z   [Information]   ✓ Surrogate key added: (433706, 25)
2025-09-21T17:16:17Z   [Information]   🧠 Memory usage after surrogate key for file 2/3: 934.6 MB
2025-09-21T17:16:17Z   [Warning]   ⚠️ High memory usage detected: 934.6 MB - forcing garbage collection
2025-09-21T17:16:17Z   [Information]   📅 Adding audit columns to file 2/3
2025-09-21T17:16:17Z   [Information]   === APPENDING AUDIT COLUMNS (POLARS) ===
2025-09-21T17:16:17Z   [Information]     Extracting file info from: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv
2025-09-21T17:16:17Z   [Information]     Pattern 1 matched: ('CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_2', '09', '19', '25', '06', '09', '06')
2025-09-21T17:16:17Z   [Information]     Successfully extracted datetime: 09/19/2025 06:09
2025-09-21T17:16:17Z   [Information]   ✓ Audit columns appended - Final shape: (433706, 28)
2025-09-21T17:16:17Z   [Information]   ✓ Audit columns added: (433706, 28)
2025-09-21T17:16:17Z   [Information]   🧠 Memory usage after audit columns for file 2/3: 934.6 MB
2025-09-21T17:16:17Z   [Warning]   ⚠️ High memory usage detected: 934.6 MB - forcing garbage collection
2025-09-21T17:16:17Z   [Information]   🔧 Ensuring consistent schema for file: EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv
2025-09-21T17:16:17Z   [Information]   ✓ Schema alignment completed for EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.csv: (433706, 28)
2025-09-21T17:16:17Z   [Information]   ✓ Schema alignment completed: (433706, 28)
2025-09-21T17:16:17Z   [Information]   🧠 Memory usage after schema alignment for file 2/3: 934.6 MB
2025-09-21T17:16:17Z   [Warning]   ⚠️ High memory usage detected: 934.6 MB - forcing garbage collection
2025-09-21T17:16:17Z   [Information]   📤 Uploading Parquet: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.parquet (433,706 rows)
2025-09-21T17:16:17Z   [Information]   Ensuring destination folder exists: CUSTOMER-ACCTG/edw-customer-acctg-enhanced
2025-09-21T17:16:18Z   [Information]   ✓ Destination folder already exists: CUSTOMER-ACCTG/edw-customer-acctg-enhanced
2025-09-21T17:16:20Z   [Information]   ✅ Uploaded Parquet: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.parquet (46999076 bytes)
2025-09-21T17:16:20Z   [Information]   ✓ Transformed and uploaded: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.parquet
2025-09-21T17:16:20Z   [Information]   🧠 Memory usage after cleanup for file 2/3: 934.7 MB
2025-09-21T17:16:20Z   [Warning]   ⚠️ High memory usage detected: 934.7 MB - forcing garbage collection
2025-09-21T17:16:20Z   [Information]   📄 Processing CSV file 3/3: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv
2025-09-21T17:16:20Z   [Information]   🧠 Memory usage before processing file 3/3: 934.7 MB
2025-09-21T17:16:20Z   [Warning]   ⚠️ High memory usage detected: 934.7 MB - forcing garbage collection
2025-09-21T17:16:20Z   [Information]   📥 Downloading CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv (237.0 MB)
2025-09-21T17:16:20Z   [Information]   Using streaming approach for large file
2025-09-21T17:16:20Z   [Information]   Streaming large CSV from blob: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv
2025-09-21T17:16:20Z   [Information]   🧠 Memory usage before streaming download: 934.7 MB
2025-09-21T17:16:20Z   [Warning]   ⚠️ High memory usage detected: 934.7 MB - forcing garbage collection
2025-09-21T17:16:20Z   [Information]   Blob size: 237.00 MB
2025-09-21T17:16:20Z   [Information]   Using chunked processing for very large file
2025-09-21T17:16:20Z   [Information]   Using chunked processing for extremely large CSV: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv
2025-09-21T17:16:20Z   [Information]   Processing 237.00 MB file in chunks
2025-09-21T17:16:20Z   [Information]   Downloading in chunks...
2025-09-21T17:16:23Z   [Information]   Downloaded 100.0 MB / 237.0 MB (42.2%)
2025-09-21T17:16:28Z   [Information]   Downloaded 200.0 MB / 237.0 MB (84.4%)
2025-09-21T17:16:31Z   [Information]   ✓ Download complete: 248507899 bytes
2025-09-21T17:16:31Z   [Information]   Reading CSV with lazy evaluation...
2025-09-21T17:16:31Z   [Information]   Collecting data with streaming...
2025-09-21T17:16:33Z   [Information]   ✓ Chunked processing complete: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv - Shape: (1273568, 23)
2025-09-21T17:16:33Z   [Information]   ✓ Temporary file cleaned up
2025-09-21T17:16:33Z   [Information]   ✓ Loaded CSV: (1273568, 23)
2025-09-21T17:16:33Z   [Information]   🧠 Memory usage after loading file 3/3: 931.6 MB
2025-09-21T17:16:33Z   [Warning]   ⚠️ High memory usage detected: 931.6 MB - forcing garbage collection
2025-09-21T17:16:33Z   [Information]   🔑 Adding surrogate key and composite key to file 3/3
2025-09-21T17:16:33Z   [Information]   === ADDING SURROGATE KEY & COMPOSITE KEY (CONFIGURABLE) ===
2025-09-21T17:16:33Z   [Information]   Original DataFrame shape: (1273568, 23)
2025-09-21T17:16:33Z   [Information]   All columns: ['CUST-NO', 'CUST-TYPE', 'FIRST-NAME', 'MIDDLE-NAME', 'LAST-NAME', 'FULL-NAME', 'CUST-BDATE', 'ADDR1-CAT', 'ADDR2-CAT', 'CTY', 'ST', 'CNTY', 'COUNTRY', 'ZIP', 'PHONE-NO-RES', 'PHONE-NO-BUS', 'EMAIL-ADDR', 'SEX', 'IBFLAG', 'OPT-OUT', 'DLR-CONTACT-CALL', 'DLR-CONTACT-EMAIL', 'DLR-CONTACT-MAIL']
2025-09-21T17:16:33Z   [Information]   Surrogate key configuration for job 'CUSTOMER-ACCTG_':
2025-09-21T17:16:33Z   [Information]     - Columns: CUST-NO,,,
2025-09-21T17:16:33Z   [Information]     - Separator: '|'
2025-09-21T17:16:33Z   [Information]     - Hash method: MD5
2025-09-21T17:16:33Z   [Information]   Parsed columns: ['CUST-NO', '', '', '']
2025-09-21T17:16:36Z   [Information]   Surrogate key logic: MD5 hash of 'CUST-NO,,,' with separator '|'
2025-09-21T17:16:36Z   [Information]   Composite key logic: Cleaned concatenated string (trailing empty values removed)
2025-09-21T17:16:36Z   [Information]   Sample surrogate key values (MD5): ['ac4c38f527c97d394be4f9dc0db294d2', 'ac4c38f527c97d394be4f9dc0db294d2', 'a5179ce1beed1fb0ca527764e5fd259c']
2025-09-21T17:16:36Z   [Information]   Sample composite key values (cleaned): ['1', '1', '2']
2025-09-21T17:16:36Z   [Information]   Sample full concatenated strings (for hashing): ['1|||', '1|||', '2|||']
2025-09-21T17:16:36Z   [Information]   ✓ Surrogate Key and Composite Key added - New shape: (1273568, 25)
2025-09-21T17:16:36Z   [Information]   ✓ Surrogate key added: (1273568, 25)
2025-09-21T17:16:36Z   [Information]   🧠 Memory usage after surrogate key for file 3/3: 932.7 MB
2025-09-21T17:16:36Z   [Warning]   ⚠️ High memory usage detected: 932.7 MB - forcing garbage collection
2025-09-21T17:16:36Z   [Information]   📅 Adding audit columns to file 3/3
2025-09-21T17:16:36Z   [Information]   === APPENDING AUDIT COLUMNS (POLARS) ===
2025-09-21T17:16:36Z   [Information]     Extracting file info from: CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv
2025-09-21T17:16:36Z   [Information]     Pattern 1 matched: ('CUSTOMER-ACCTG/edw-customer-acctg-decompressed/EDW-CUSTOMER-ACCTG_3', '09', '19', '25', '06', '14', '28')
2025-09-21T17:16:36Z   [Information]     Successfully extracted datetime: 09/19/2025 06:14
2025-09-21T17:16:36Z   [Information]   ✓ Audit columns appended - Final shape: (1273568, 28)
2025-09-21T17:16:36Z   [Information]   ✓ Audit columns added: (1273568, 28)
2025-09-21T17:16:36Z   [Information]   🧠 Memory usage after audit columns for file 3/3: 936.7 MB
2025-09-21T17:16:36Z   [Warning]   ⚠️ High memory usage detected: 936.7 MB - forcing garbage collection
2025-09-21T17:16:36Z   [Information]   🔧 Ensuring consistent schema for file: EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv
2025-09-21T17:16:36Z   [Information]   ✓ Schema alignment completed for EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.csv: (1273568, 28)
2025-09-21T17:16:36Z   [Information]   ✓ Schema alignment completed: (1273568, 28)
2025-09-21T17:16:36Z   [Information]   🧠 Memory usage after schema alignment for file 3/3: 936.7 MB
2025-09-21T17:16:36Z   [Warning]   ⚠️ High memory usage detected: 936.7 MB - forcing garbage collection
2025-09-21T17:16:36Z   [Information]   📤 Uploading Parquet: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.parquet (1,273,568 rows)
2025-09-21T17:16:36Z   [Information]   Ensuring destination folder exists: CUSTOMER-ACCTG/edw-customer-acctg-enhanced
2025-09-21T17:16:36Z   [Information]   ✓ Destination folder already exists: CUSTOMER-ACCTG/edw-customer-acctg-enhanced
2025-09-21T17:16:36Z   [Information]   ⚠️ Large DataFrame detected (1,273,568 rows) - using memory-efficient upload
2025-09-21T17:16:36Z   [Information]   🚀 Starting memory-efficient Parquet upload for large DataFrame
2025-09-21T17:16:36Z   [Information]   📊 DataFrame info: 1,273,568 rows, 28 columns
2025-09-21T17:16:36Z   [Information]   📝 Writing DataFrame to temporary file with streaming...
2025-09-21T17:16:38Z   [Information]   📝 Temporary Parquet file size: 145363843 bytes
2025-09-21T17:16:38Z   [Information]   🚀 Starting chunked blob upload...
2025-09-21T17:16:38Z   [Information]   ✅ Container sftp-landing already exists
2025-09-21T17:16:40Z   [Information]   ✅ Successfully uploaded large Parquet file: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.parquet
2025-09-21T17:16:40Z   [Information]   ✅ Verification - Blob size: 145363843 bytes
2025-09-21T17:16:40Z   [Information]   🎉 SUCCESS: Uploaded Parquet: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.parquet - Size: 145363843 bytes
2025-09-21T17:16:40Z   [Information]   🧹 Temporary file cleaned up
2025-09-21T17:16:40Z   [Information]   ✓ Transformed and uploaded: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.parquet
2025-09-21T17:16:40Z   [Information]   🧠 Memory usage after cleanup for file 3/3: 948.8 MB
2025-09-21T17:16:40Z   [Warning]   ⚠️ High memory usage detected: 948.8 MB - forcing garbage collection
2025-09-21T17:16:40Z   [Information]   ✅ Successfully transformed and converted 3/3 files to Parquet
2025-09-21T17:16:40Z   [Information]   🧠 All files now have surrogate key, composite key, and audit columns
2025-09-21T17:16:40Z   [Information]   🧠 Memory usage after all CSV to Parquet transformations: 948.8 MB
2025-09-21T17:16:40Z   [Warning]   ⚠️ High memory usage detected: 948.8 MB - forcing garbage collection
2025-09-21T17:16:40Z   [Information]   ✅ STEP 1 COMPLETED: Transformed and converted 3 CSV files to individual Parquet files
2025-09-21T17:16:40Z   [Information]   📋 Individual transformed Parquet files: ['CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.parquet', 'CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.parquet', 'CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.parquet']
2025-09-21T17:16:40Z   [Information]   🧠 Each file already has surrogate key, composite key, and audit columns
2025-09-21T17:16:40Z   [Information]   STEP 2: CONSOLIDATING PRE-TRANSFORMED PARQUET FILES
2025-09-21T17:16:40Z   [Information]   --------------------------------------------------
2025-09-21T17:16:40Z   [Information]   🔄 Starting MEMORY-EFFICIENT consolidation of pre-transformed Parquet files...
2025-09-21T17:16:40Z   [Information]   🧠 Using streaming approach to avoid loading all data into memory at once
2025-09-21T17:16:40Z   [Information]   === STREAMING CONSOLIDATION FOR MEMORY EFFICIENCY ===
2025-09-21T17:16:40Z   [Information]   Consolidating 3 Parquet files using streaming approach
2025-09-21T17:16:40Z   [Information]   📥 Reading file 1/3: EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.parquet
2025-09-21T17:16:40Z   [Information]   Streaming Parquet from blob: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.parquet
2025-09-21T17:16:48Z   [Information]   ✓ Streamed Parquet: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_1_09-19-25_05.54.25.parquet - Shape: (1270096, 28)
2025-09-21T17:16:48Z   [Information]      ✓ Loaded: (1270096, 28)
2025-09-21T17:16:48Z   [Information]   🧠 Memory usage after loading file 1/3: 1019.0 MB
2025-09-21T17:16:48Z   [Warning]   ⚠️ High memory usage detected: 1019.0 MB - forcing garbage collection
2025-09-21T17:16:48Z   [Information]   📥 Reading file 2/3: EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.parquet
2025-09-21T17:16:48Z   [Information]   Streaming Parquet from blob: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.parquet
2025-09-21T17:16:51Z   [Information]   ✓ Streamed Parquet: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_2_09-19-25_06.09.06.parquet - Shape: (433706, 28)
2025-09-21T17:16:51Z   [Information]      ✓ Loaded: (433706, 28)
2025-09-21T17:16:51Z   [Information]   🧠 Memory usage after loading file 2/3: 1262.5 MB
2025-09-21T17:16:51Z   [Warning]   ⚠️ High memory usage detected: 1262.5 MB - forcing garbage collection
2025-09-21T17:16:51Z   [Information]   📥 Reading file 3/3: EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.parquet
2025-09-21T17:16:51Z   [Information]   Streaming Parquet from blob: CUSTOMER-ACCTG/edw-customer-acctg-enhanced/EDW-CUSTOMER-ACCTG_3_09-19-25_06.14.28.parquet
2025-09-21T17:17:01Z   [Error]   Executed 'Functions.CSVProcessingPipeline' (Failed, Id=9a0f58dd-25c2-4c7b-93e1-45007e67ea18, Duration=90126ms)