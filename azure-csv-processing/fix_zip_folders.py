#!/usr/bin/env python3
"""
Script to fix zip folder configuration to match azure-ftp-etl folder structure
"""
import os
import sys

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(current_dir, 'shared')
if shared_dir not in sys.path:
    sys.path.append(shared_dir)

try:
    from azure.data.tables import TableServiceClient
    from azure.identity import DefaultAzureCredential
    AZURE_MODULES_AVAILABLE = True
except ImportError:
    AZURE_MODULES_AVAILABLE = False
    print("❌ Azure modules not available")
    sys.exit(1)

def main():
    print("🔧 Fixing zip folder configuration to match azure-ftp-etl structure...")
    
    if not AZURE_MODULES_AVAILABLE:
        print("❌ Azure modules not available")
        return
    
    try:
        # Connect to Azure Table Storage
        storage_account_name = "sagedw"
        table_storage_url = f"https://{storage_account_name}.table.core.windows.net"
        credential = DefaultAzureCredential()
        table_service_client = TableServiceClient(endpoint=table_storage_url, credential=credential)
        table_client = table_service_client.get_table_client(table_name="sftpConfig")
        
        # Configuration fixes based on azure-ftp-etl build_paths_non_dealer() function
        fixes = [
            {
                'entity_key': 'CUSTOMER-ACCTG_',
                'current_zip_source': 'CUSTOMER-ACCTG/edw-customer-acctg-compressed',
                'correct_zip_source': 'CUSTOMER-ACCTG/customer-acctg-compressed',
                'current_zip_pattern': 'CUSTOMER-ACCTG*.zip',
                'correct_zip_pattern': 'CUSTOMER-ACCTG*.zip'  # This might be correct
            },
            {
                'entity_key': 'FIMASTSALES_',
                'current_zip_source': 'FIMAST-SALES/edw-fimastsales-compressed', 
                'correct_zip_source': 'FIMAST-SALES/fimastsales-compressed',
                'current_zip_pattern': 'FIMASTSALES*.zip',
                'correct_zip_pattern': 'FIMASTSALES*.zip'  # This might be correct
            }
        ]
        
        for fix in fixes:
            entity_key = fix['entity_key']
            print(f"\n{'='*60}")
            print(f"🔧 Fixing entity: {entity_key}")
            print(f"{'='*60}")
            
            try:
                # Get the current entity
                entity = table_client.get_entity(partition_key="Reynolds", row_key=entity_key)
                
                print(f"📋 Current Configuration:")
                print(f"   ZipSourceFolder: '{entity.get('ZipSourceFolder', 'NOT_SET')}'")
                print(f"   ZipFilePattern: '{entity.get('ZipFilePattern', 'NOT_SET')}'")
                
                # Update the configuration
                old_zip_source = entity.get('ZipSourceFolder', '')
                new_zip_source = fix['correct_zip_source']
                
                old_zip_pattern = entity.get('ZipFilePattern', '')
                new_zip_pattern = fix['correct_zip_pattern']
                
                print(f"\n🔄 Proposed Changes:")
                print(f"   ZipSourceFolder: '{old_zip_source}' → '{new_zip_source}'")
                print(f"   ZipFilePattern: '{old_zip_pattern}' → '{new_zip_pattern}'")
                
                # Ask for confirmation
                response = input(f"\n❓ Apply these changes to {entity_key}? (y/N): ").strip().lower()
                
                if response == 'y':
                    # Update the entity
                    entity['ZipSourceFolder'] = new_zip_source
                    if old_zip_pattern != new_zip_pattern:
                        entity['ZipFilePattern'] = new_zip_pattern
                    
                    table_client.update_entity(mode="merge", entity=entity)
                    
                    print(f"✅ Successfully updated {entity_key}")
                    
                    # Verify the update
                    updated_entity = table_client.get_entity(partition_key="Reynolds", row_key=entity_key)
                    print(f"🔍 Verified ZipSourceFolder is now: '{updated_entity.get('ZipSourceFolder', 'NOT_SET')}'")
                    print(f"🔍 Verified ZipFilePattern is now: '{updated_entity.get('ZipFilePattern', 'NOT_SET')}'")
                else:
                    print(f"⏭️ Skipped {entity_key}")
                
            except Exception as e:
                print(f"❌ Error updating entity {entity_key}: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n{'='*60}")
        print("🎯 Summary")
        print(f"{'='*60}")
        print("The issue was that azure-csv-processing was looking for zip files in:")
        print("- CUSTOMER-ACCTG/edw-customer-acctg-compressed")
        print("- FIMAST-SALES/edw-fimastsales-compressed")
        print("")
        print("But azure-ftp-etl creates zip files in:")
        print("- CUSTOMER-ACCTG/customer-acctg-compressed")
        print("- FIMAST-SALES/fimastsales-compressed")
        print("")
        print("The 'edw-' prefix was causing the mismatch!")
        
    except Exception as e:
        print(f"❌ Error accessing sftpConfig table: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
