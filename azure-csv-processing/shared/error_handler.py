import logging
import traceback
import json
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
import functools
import time

class ErrorCategory(Enum):
    """Error categories for better error classification"""
    CONFIGURATION = "configuration"
    AUTHENTICATION = "authentication"
    STORAGE_ACCESS = "storage_access"
    DATA_PROCESSING = "data_processing"
    VALIDATION = "validation"
    NETWORK = "network"
    TIMEOUT = "timeout"
    UNKNOWN = "unknown"

class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ProcessingError(Exception):
    """Custom exception for CSV processing errors"""
    
    def __init__(self, message: str, category: ErrorCategory = ErrorCategory.UNKNOWN, 
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM, details: Dict = None):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.details = details or {}
        self.timestamp = datetime.utcnow()

class RetryConfig:
    """Configuration for retry logic"""
    
    def __init__(self, max_attempts: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, backoff_factor: float = 2.0):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor

def retry_with_backoff(retry_config: RetryConfig = None, 
                      retryable_exceptions: tuple = (Exception,)):
    """
    Decorator for implementing retry logic with exponential backoff
    """
    if retry_config is None:
        retry_config = RetryConfig()
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(retry_config.max_attempts):
                try:
                    return func(*args, **kwargs)
                except retryable_exceptions as e:
                    last_exception = e
                    
                    if attempt == retry_config.max_attempts - 1:
                        # Last attempt failed
                        logging.error(f"❌ Function {func.__name__} failed after {retry_config.max_attempts} attempts")
                        raise ProcessingError(
                            f"Function failed after {retry_config.max_attempts} attempts: {str(e)}",
                            category=ErrorCategory.DATA_PROCESSING,
                            severity=ErrorSeverity.HIGH,
                            details={"function": func.__name__, "attempts": retry_config.max_attempts}
                        )
                    
                    # Calculate delay for next attempt
                    delay = min(
                        retry_config.base_delay * (retry_config.backoff_factor ** attempt),
                        retry_config.max_delay
                    )
                    
                    logging.warning(f"⚠️  Attempt {attempt + 1}/{retry_config.max_attempts} failed for {func.__name__}: {str(e)}")
                    logging.info(f"Retrying in {delay:.2f} seconds...")
                    time.sleep(delay)
                
                except Exception as e:
                    # Non-retryable exception
                    logging.error(f"❌ Non-retryable error in {func.__name__}: {str(e)}")
                    raise ProcessingError(
                        f"Non-retryable error: {str(e)}",
                        category=ErrorCategory.UNKNOWN,
                        severity=ErrorSeverity.HIGH,
                        details={"function": func.__name__}
                    )
            
            # This should never be reached, but just in case
            raise last_exception
        
        return wrapper
    return decorator

class ErrorHandler:
    """Centralized error handling and logging"""
    
    @staticmethod
    def categorize_error(error: Exception) -> ErrorCategory:
        """Categorize errors based on their type and message"""
        error_str = str(error).lower()
        error_type = type(error).__name__.lower()
        
        # Authentication errors
        if any(keyword in error_str for keyword in ['unauthorized', 'authentication', 'credential', 'token']):
            return ErrorCategory.AUTHENTICATION
        
        # Storage access errors
        if any(keyword in error_str for keyword in ['blob', 'storage', 'container', 'not found', '404']):
            return ErrorCategory.STORAGE_ACCESS
        
        # Network errors
        if any(keyword in error_str for keyword in ['network', 'connection', 'timeout', 'dns']):
            return ErrorCategory.NETWORK
        
        # Configuration errors
        if any(keyword in error_str for keyword in ['config', 'setting', 'environment', 'variable']):
            return ErrorCategory.CONFIGURATION
        
        # Data processing errors
        if any(keyword in error_str for keyword in ['dataframe', 'csv', 'pandas', 'column', 'data']):
            return ErrorCategory.DATA_PROCESSING
        
        # Validation errors
        if any(keyword in error_str for keyword in ['validation', 'invalid', 'missing', 'required']):
            return ErrorCategory.VALIDATION
        
        return ErrorCategory.UNKNOWN
    
    @staticmethod
    def determine_severity(error: Exception, category: ErrorCategory) -> ErrorSeverity:
        """Determine error severity based on category and error details"""
        error_str = str(error).lower()
        
        # Critical errors that stop processing
        if category == ErrorCategory.AUTHENTICATION:
            return ErrorSeverity.CRITICAL
        
        if category == ErrorCategory.CONFIGURATION:
            return ErrorSeverity.CRITICAL
        
        # High severity errors
        if any(keyword in error_str for keyword in ['failed', 'error', 'exception']):
            if category in [ErrorCategory.STORAGE_ACCESS, ErrorCategory.DATA_PROCESSING]:
                return ErrorSeverity.HIGH
        
        # Medium severity (default)
        return ErrorSeverity.MEDIUM
    
    @staticmethod
    def handle_error(error: Exception, context: str = "", additional_details: Dict = None) -> Dict[str, Any]:
        """
        Comprehensive error handling with categorization and logging
        
        Args:
            error: The exception that occurred
            context: Context where the error occurred
            additional_details: Additional details to include in error report
        
        Returns:
            Dict containing error information for response
        """
        category = ErrorHandler.categorize_error(error)
        severity = ErrorHandler.determine_severity(error, category)
        
        error_details = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "category": category.value,
            "severity": severity.value,
            "context": context,
            "timestamp": datetime.utcnow().isoformat(),
            "traceback": traceback.format_exc()
        }
        
        if additional_details:
            error_details.update(additional_details)
        
        # Log based on severity
        if severity == ErrorSeverity.CRITICAL:
            logging.critical(f"🚨 CRITICAL ERROR in {context}: {str(error)}")
            logging.critical(f"Category: {category.value}, Details: {json.dumps(error_details, indent=2)}")
        elif severity == ErrorSeverity.HIGH:
            logging.error(f"❌ HIGH SEVERITY ERROR in {context}: {str(error)}")
            logging.error(f"Category: {category.value}, Details: {json.dumps(error_details, indent=2)}")
        elif severity == ErrorSeverity.MEDIUM:
            logging.warning(f"⚠️  MEDIUM SEVERITY ERROR in {context}: {str(error)}")
            logging.warning(f"Category: {category.value}")
        else:
            logging.info(f"ℹ️  LOW SEVERITY ERROR in {context}: {str(error)}")
        
        return error_details
    
    @staticmethod
    def create_error_response(error_details: Dict[str, Any], status_code: int = 500) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            "status": "error",
            "timestamp": datetime.utcnow().isoformat(),
            "error": {
                "category": error_details.get("category", "unknown"),
                "severity": error_details.get("severity", "medium"),
                "message": error_details.get("error_message", "Unknown error occurred"),
                "context": error_details.get("context", ""),
                "type": error_details.get("error_type", "Exception")
            },
            "status_code": status_code,
            "retry_recommended": error_details.get("category") in [
                ErrorCategory.NETWORK.value, 
                ErrorCategory.TIMEOUT.value,
                ErrorCategory.STORAGE_ACCESS.value
            ]
        }

def safe_execute(func, context: str = "", default_return=None, **kwargs):
    """
    Safely execute a function with comprehensive error handling
    
    Args:
        func: Function to execute
        context: Context description for logging
        default_return: Default value to return on error
        **kwargs: Arguments to pass to the function
    
    Returns:
        Function result or default_return on error
    """
    try:
        return func(**kwargs)
    except Exception as e:
        error_details = ErrorHandler.handle_error(e, context)
        logging.error(f"Safe execution failed in {context}: {str(e)}")
        return default_return

# Circuit breaker pattern for external service calls
class CircuitBreaker:
    """Circuit breaker pattern implementation"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "HALF_OPEN"
                logging.info("🔄 Circuit breaker moving to HALF_OPEN state")
            else:
                raise ProcessingError(
                    "Circuit breaker is OPEN - service unavailable",
                    category=ErrorCategory.NETWORK,
                    severity=ErrorSeverity.HIGH
                )
        
        try:
            result = func(*args, **kwargs)
            if self.state == "HALF_OPEN":
                self.state = "CLOSED"
                self.failure_count = 0
                logging.info("✅ Circuit breaker reset to CLOSED state")
            return result
        
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = "OPEN"
                logging.error(f"🚨 Circuit breaker OPENED after {self.failure_count} failures")
            
            raise e
