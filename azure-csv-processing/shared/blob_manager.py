import os
import sys
import tempfile
import polars as pl
import logging
import gc
from io import BytesIO, StringIO

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from azure_config import blob_service_client, get_source_blob_path, get_output_blob_path, get_source_folder_path, get_output_folder_path

def log_memory_usage(context=""):
    """Log current memory usage for monitoring"""
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        logging.info(f"🧠 Memory usage {context}: {memory_mb:.1f} MB")

        # If memory usage is very high, force garbage collection
        if memory_mb > 800:  # If using more than 800MB
            logging.warning(f"⚠️ High memory usage detected: {memory_mb:.1f} MB - forcing garbage collection")
            gc.collect()

    except Exception as e:
        logging.debug(f"Could not get memory info: {e}")

def list_parquet_files_in_container(container_name, file_patterns=None, folder_path=None):
    """List all Parquet files in the specified container within the source folder path"""
    try:
        container_client = blob_service_client.get_container_client(container_name)
        parquet_files = []
        source_folder = folder_path if folder_path is not None else get_source_folder_path()

        # Listing Parquet files (suppressed verbose logging)

        # List blobs with the source folder prefix
        for blob in container_client.list_blobs(name_starts_with=source_folder):
            blob_name = blob.name

            # Check if it's a Parquet file
            if blob_name.lower().endswith('.parquet'):
                # Extract just the filename from the full path
                filename = blob_name.split('/')[-1]

                # If file patterns specified, check if filename matches any pattern
                if file_patterns:
                    matches_pattern = any(filename.startswith(pattern) for pattern in file_patterns)
                    if matches_pattern:
                        parquet_files.append(blob_name)  # Keep full path
                else:
                    parquet_files.append(blob_name)  # Keep full path

        logging.info(f"Total Parquet files found: {len(parquet_files)}")
        return parquet_files

    except Exception as e:
        logging.error(f"Error listing Parquet files: {e}")
        raise

def list_csv_files_in_container(container_name, file_patterns=None, folder_path=None, job_key=None):
    """List all CSV files in the specified container - enhanced to handle dealer prefix structure"""
    try:
        container_client = blob_service_client.get_container_client(container_name)
        csv_files = []
        source_folder = folder_path if folder_path is not None else get_source_folder_path(job_key)

        # Check if this job uses dealer prefix structure
        from azure_config import get_dealer_prefix_enabled, get_job_config
        dealer_prefix_enabled = get_dealer_prefix_enabled(job_key)

        # Listing CSV files (suppressed verbose logging)

        if dealer_prefix_enabled:
            # Handle dealer structure (e.g., RO_Time/AO_RO_TIME/ao_ro_time-decompressed/)
            logging.info(f"Using dealer prefix structure for job: {job_key}")
            
            # Extract base folder from source folder (e.g., "RO_Time" from "RO_Time/decompressed")
            base_folder = source_folder.split('/')[0] if '/' in source_folder else source_folder
            logging.info(f"Base folder for dealer search: {base_folder}")
            
            # First, discover all dealer folders
            dealer_folders = set()
            for blob in container_client.list_blobs(name_starts_with=base_folder):
                path_parts = blob.name.split('/')
                # Look for dealer folders (e.g., AO_RO_TIME, BMWL_RO_TIME)
                if len(path_parts) >= 2 and '_RO_TIME' in path_parts[1]:
                    dealer_folders.add(path_parts[1])
            
            logging.info(f"Found {len(dealer_folders)} dealer folders: {sorted(dealer_folders)}")
            
            # Now search for CSV files in each dealer's decompressed folder
            for dealer_folder in dealer_folders:
                dealer_decompressed_path = f"{base_folder}/{dealer_folder}/{dealer_folder.lower()}-decompressed"
                logging.info(f"Searching in dealer path: {dealer_decompressed_path}")
                
                # List files in this dealer's decompressed folder
                for blob in container_client.list_blobs(name_starts_with=dealer_decompressed_path):
                    blob_name = blob.name
                    
                    # Check if it's a CSV file
                    if blob_name.lower().endswith('.csv'):
                        filename = blob_name.split('/')[-1]
                        
                        # Apply file pattern matching for dealer files
                        if file_patterns:
                            # For dealer files, check if pattern exists anywhere in filename (not just at start)
                            # e.g., "AO_RO_TIME_08-20-25.csv" should match pattern "RO_TIME"
                            matches_pattern = any(pattern in filename for pattern in file_patterns)
                            if matches_pattern:
                                csv_files.append(blob_name)
                        else:
                            csv_files.append(blob_name)
        else:
            # Use existing logic for non-dealer files (FIMAST-SALES, CUSTOMER-ACCTG, etc.)
            logging.info(f"Using standard structure for job: {job_key}")
            
            # List blobs with the source folder prefix
            for blob in container_client.list_blobs(name_starts_with=source_folder):
                blob_name = blob.name

                # Check if it's a CSV file
                if blob_name.lower().endswith('.csv'):
                    # Extract just the filename from the full path
                    filename = blob_name.split('/')[-1]

                    # If file patterns specified, check if filename matches any pattern
                    if file_patterns:
                        logging.info(f"🔍 Checking file '{filename}' against patterns: {file_patterns}")
                        matches_pattern = any(filename.startswith(pattern) for pattern in file_patterns)
                        logging.info(f"🔍 File '{filename}' matches pattern: {matches_pattern}")
                        if matches_pattern:
                            csv_files.append(blob_name)  # Keep full path
                            logging.info(f"✅ Added file to processing list: {blob_name}")
                    else:
                        csv_files.append(blob_name)  # Keep full path

        logging.info(f"Total CSV files found: {len(csv_files)}")
        return csv_files

    except Exception as e:
        logging.error(f"Error listing CSV files: {e}")
        raise

def ensure_destination_folder_exists(container_name, job_key=None):
    """Ensure the destination folder exists by creating a placeholder file if needed"""
    try:
        output_folder = get_output_folder_path(job_key)
        placeholder_blob_name = f"{output_folder}/.placeholder"

        logging.info(f"Ensuring destination folder exists: {output_folder}")

        # Check if any files exist in the destination folder
        container_client = blob_service_client.get_container_client(container_name)
        blobs_in_folder = list(container_client.list_blobs(name_starts_with=output_folder))

        if not blobs_in_folder:
            # Create a placeholder file to ensure the folder structure exists
            logging.info(f"Creating destination folder structure with placeholder: {placeholder_blob_name}")
            blob_client = blob_service_client.get_blob_client(container=container_name, blob=placeholder_blob_name)
            blob_client.upload_blob(b"", overwrite=True)
            logging.info(f"✓ Destination folder created: {output_folder}")
        else:
            logging.info(f"✓ Destination folder already exists: {output_folder}")

    except Exception as e:
        logging.error(f"Error ensuring destination folder exists: {e}")
        # Don't raise - this is not critical for the main operation
        pass

def download_parquet_from_blob_streaming(blob_name, container_name):
    """Download Parquet file with streaming and lazy evaluation"""
    try:
        logging.info(f"Streaming Parquet from blob: {blob_name}")

        blob_client = blob_service_client.get_blob_client(
            container=container_name, blob=blob_name
        )

        # Stream download to memory buffer
        blob_data = blob_client.download_blob()
        parquet_buffer = BytesIO()
        blob_data.readinto(parquet_buffer)
        parquet_buffer.seek(0)

        # Use Polars lazy loading for memory efficiency
        df = pl.read_parquet(parquet_buffer)

        logging.info(f"✓ Streamed Parquet: {blob_name} - Shape: {df.shape}")
        return df

    except Exception as e:
        logging.error(f"Error streaming Parquet {blob_name}: {e}")
        raise

def download_parquet_lazy(blob_name, container_name):
    """Download Parquet and return lazy frame for memory efficiency"""
    try:
        logging.info(f"Creating lazy frame for Parquet blob: {blob_name}")

        blob_client = blob_service_client.get_blob_client(
            container=container_name, blob=blob_name
        )

        blob_data = blob_client.download_blob()
        parquet_buffer = BytesIO()
        blob_data.readinto(parquet_buffer)
        parquet_buffer.seek(0)

        # Return lazy frame for memory efficiency
        lazy_df = pl.scan_parquet(parquet_buffer)

        logging.info(f"✓ Created lazy frame for: {blob_name}")
        return lazy_df

    except Exception as e:
        logging.error(f"Error creating lazy frame for {blob_name}: {e}")
        raise

def download_csv_sample_for_schema(blob_name, container_name, sample_rows=10):
    """
    Download only a small sample of CSV for schema discovery (header + few rows)
    This is much more memory efficient than downloading the entire file
    """
    logging.info(f"📋 LIGHTWEIGHT SCHEMA DISCOVERY: Reading only {sample_rows} sample rows from {blob_name}")

    try:
        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

        # Get blob properties to check size
        blob_properties = blob_client.get_blob_properties()
        blob_size_mb = blob_properties.size / (1024 * 1024)
        logging.info(f"📊 Blob size: {blob_size_mb:.2f} MB (will only read header + {sample_rows} rows)")

        # Read only the first chunk of the file (enough to get header + sample rows)
        # For CSV files, reading first 64KB should be more than enough for schema discovery
        chunk_size = 64 * 1024  # 64KB should contain header + many sample rows

        blob_data = blob_client.download_blob(offset=0, length=chunk_size)
        csv_content = blob_data.readall().decode('utf-8')

        # Create a StringIO object from the partial content
        csv_buffer = StringIO(csv_content)

        # Read with Polars, limiting to sample_rows + 1 (for header)
        # Use robust parsing options for potentially malformed CSV data
        try:
            df = pl.read_csv(
                csv_buffer,
                n_rows=sample_rows,
                ignore_errors=True,  # Handle malformed CSV data
                truncate_ragged_lines=True  # Handle inconsistent line lengths
            )
        except Exception as csv_error:
            logging.warning(f"⚠️ CSV parsing failed with ignore_errors=True: {str(csv_error)}")
            # Try with even more lenient settings
            csv_buffer.seek(0)  # Reset buffer
            df = pl.read_csv(
                csv_buffer,
                n_rows=sample_rows,
                ignore_errors=True,
                truncate_ragged_lines=True,
                quote_char=None,  # Disable quote handling for malformed data
                infer_schema_length=1000  # Increase schema inference length
            )

        logging.info(f"✅ LIGHTWEIGHT schema discovery complete: {df.shape} (only {sample_rows} rows)")
        logging.info(f"🔍 Discovered columns: {df.columns}")

        return df

    except Exception as e:
        logging.error(f"❌ Lightweight schema discovery failed for {blob_name}: {str(e)}")
        # Fallback to regular download if lightweight fails
        logging.info(f"🔄 Falling back to regular download for schema discovery...")
        df_full = download_csv_from_blob(blob_name, container_name)
        return df_full.head(sample_rows)

def download_csv_from_blob(blob_name, container_name):
    """Download CSV file from blob storage and return as Polars DataFrame with memory optimization"""
    try:
        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

        # Get blob properties to check size
        blob_properties = blob_client.get_blob_properties()
        blob_size_mb = blob_properties.size / (1024 * 1024)
        logging.info(f"📥 Downloading {blob_name} ({blob_size_mb:.1f} MB)")

        # For large files (>50MB), use streaming approach - lowered threshold for better memory management
        if blob_size_mb > 50:
            logging.info("Using streaming approach for large file")
            return download_csv_from_blob_streaming(blob_name, container_name)

        # For medium files (>10MB), use ultra-memory-efficient approach
        if blob_size_mb > 10:
            logging.info("Using ultra-memory-efficient approach for medium file")
            return download_csv_ultra_memory_efficient(blob_name, container_name)

        # For smaller files, use the original approach
        blob_data = blob_client.download_blob().readall()

        # Convert bytes to string and then to DataFrame using Polars
        csv_string = blob_data.decode('utf-8')
        from io import StringIO

        # Read with Polars - use conservative schema inference for memory efficiency
        df = pl.read_csv(
            StringIO(csv_string),
            infer_schema_length=5000,  # Reduced schema inference for memory efficiency
            try_parse_dates=True,
            ignore_errors=True,  # Allow parsing errors to continue processing
            null_values=["", "NULL", "null", "N/A", "n/a"],  # Handle various null representations
        )

        # Force garbage collection after loading
        import gc
        del csv_string, blob_data
        gc.collect()

        logging.info(f"✓ Downloaded CSV: {blob_name} - Shape: {df.shape}")
        log_memory_usage("after CSV download")
        return df

    except Exception as e:
        logging.error(f"Error downloading CSV {blob_name}: {e}")
        raise


def download_csv_ultra_memory_efficient(blob_name, container_name, max_rows_per_chunk=50000):
    """
    Ultra-memory-efficient CSV download for files with thousands of rows
    Processes CSV in small chunks to prevent memory overflow
    """
    try:
        logging.info(f"🔥 Ultra-memory-efficient CSV download: {blob_name}")
        log_memory_usage("before ultra-efficient download")

        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

        # Get blob properties
        blob_properties = blob_client.get_blob_properties()
        blob_size_mb = blob_properties.size / (1024 * 1024)
        logging.info(f"Blob size: {blob_size_mb:.2f} MB")

        import tempfile
        import os
        import gc

        # Download to temporary file first
        with tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.csv') as temp_file:
            logging.info("📥 Downloading to temporary file...")

            # Stream download in chunks to avoid loading entire file into memory
            download_stream = blob_client.download_blob()
            for chunk in download_stream.chunks():
                temp_file.write(chunk)

            temp_file.flush()
            temp_file_path = temp_file.name

        logging.info(f"✓ Downloaded to temporary file: {temp_file_path}")
        log_memory_usage("after download to temp file")

        try:
            # Use Polars scan_csv for lazy loading with very conservative settings
            logging.info("📊 Reading CSV with ultra-conservative lazy evaluation...")

            lazy_df = pl.scan_csv(
                temp_file_path,
                infer_schema_length=1000,  # Very small schema inference
                try_parse_dates=False,  # Disable date parsing for speed
                ignore_errors=True,
                null_values=["", "NULL", "null", "N/A", "n/a"],
            )

            # Get row count estimate
            try:
                row_count = lazy_df.select(pl.len()).collect().item()
                logging.info(f"📊 Estimated row count: {row_count:,}")

                # If very large, process in chunks
                if row_count > max_rows_per_chunk:
                    logging.warning(f"⚠️ Large dataset ({row_count:,} rows) - processing in chunks")
                    return process_csv_in_memory_safe_chunks(lazy_df, max_rows_per_chunk, blob_name)

            except Exception as e:
                logging.warning(f"⚠️ Could not estimate row count: {e}")

            # For smaller datasets, collect with streaming
            logging.info("📊 Collecting with streaming optimization...")
            df = lazy_df.collect(streaming=True)

            logging.info(f"✓ Ultra-efficient processing complete: {blob_name} - Shape: {df.shape}")
            log_memory_usage("after ultra-efficient processing")

            return df

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
                logging.info("🧹 Temporary file cleaned up")
            except:
                pass

    except Exception as e:
        logging.error(f"❌ Error in ultra-memory-efficient processing: {e}")
        raise


def process_csv_in_memory_safe_chunks(lazy_df, chunk_size, blob_name):
    """
    Process very large CSV in memory-safe chunks
    Returns a representative sample to prevent memory overflow
    """
    try:
        logging.info(f"🔥 Processing {blob_name} in memory-safe chunks of {chunk_size:,} rows")

        # For very large datasets, process in chunks to handle ALL data
        # This prevents memory issues while preserving all data
        logging.warning(f"⚠️ Processing large dataset in memory-safe chunks to preserve all data")

        # Get total row count first
        try:
            total_rows = lazy_df.select(pl.len()).collect().item()
            logging.info(f"Total rows in CSV: {total_rows:,}")

            # Use the chunked collection function from file_consolidator
            from .file_consolidator import collect_large_dataset_in_chunks
            sample_df = collect_large_dataset_in_chunks(lazy_df, total_rows)

            # Add metadata about the full processing
            sample_df = sample_df.with_columns([
                pl.lit("memory_safe_chunked_full").alias("_processing_method"),
                pl.lit(f"Full processing of {total_rows:,} rows using chunked collection").alias("_processing_note")
            ])

        except Exception as e:
            logging.error(f"Error in chunked processing: {e}, falling back to streaming collection")
            # Fallback to streaming collection of all data
            sample_df = lazy_df.collect(streaming=True)

            # Add metadata about the fallback
            sample_df = sample_df.with_columns([
                pl.lit("memory_safe_streaming_full").alias("_processing_method"),
                pl.lit("Full processing using streaming collection fallback").alias("_processing_note")
            ])

        logging.info(f"✓ Memory-safe chunk processing completed - Sample shape: {sample_df.shape}")
        log_memory_usage("after chunk processing")

        return sample_df

    except Exception as e:
        logging.error(f"❌ Error in chunk processing: {e}")
        raise

def download_csv_from_blob_streaming(blob_name, container_name):
    """Download large CSV file using true streaming to minimize memory usage"""
    try:
        logging.info(f"Streaming large CSV from blob: {blob_name}")
        log_memory_usage("before streaming download")

        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

        # Get blob properties to check size
        blob_properties = blob_client.get_blob_properties()
        blob_size_mb = blob_properties.size / (1024 * 1024)
        logging.info(f"Blob size: {blob_size_mb:.2f} MB")

        # For very large files (>200MB), use chunked processing - lowered threshold
        if blob_size_mb > 200:
            logging.info("Using chunked processing for very large file")
            return download_csv_chunked_processing(blob_name, container_name)

        # For moderately large files (100-500MB), use optimized streaming
        logging.info("Using optimized streaming for large file")

        # Use temporary file approach to avoid memory accumulation
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(mode='w+b', delete=False, suffix='.csv') as temp_file:
            try:
                # Stream download directly to temporary file
                blob_stream = blob_client.download_blob()

                # Write in larger chunks for better performance
                chunk_size = 1024 * 1024  # 1MB chunks
                total_written = 0

                for chunk in blob_stream.chunks():
                    temp_file.write(chunk)
                    total_written += len(chunk)

                    # Log progress for very large files
                    if total_written % (50 * 1024 * 1024) == 0:  # Every 50MB
                        progress_mb = total_written / (1024 * 1024)
                        logging.info(f"Downloaded {progress_mb:.1f} MB / {blob_size_mb:.1f} MB")

                temp_file.flush()
                temp_file_path = temp_file.name

                logging.info(f"✓ Downloaded to temporary file: {total_written} bytes")

                # Read CSV from temporary file using Polars
                df = pl.read_csv(
                    temp_file_path,
                    infer_schema_length=10000,
                    try_parse_dates=True,
                    ignore_errors=True,
                    null_values=["", "NULL", "null", "N/A", "n/a"],
                )

                logging.info(f"✓ Streamed CSV: {blob_name} - Shape: {df.shape}")
                return df

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_file_path)
                    logging.info("✓ Temporary file cleaned up")
                except:
                    pass

    except Exception as e:
        logging.error(f"Error streaming CSV {blob_name}: {e}")
        raise

def download_csv_chunked_processing(blob_name, container_name):
    """Process extremely large CSV files in chunks to avoid memory issues"""
    try:
        logging.info(f"Using chunked processing for extremely large CSV: {blob_name}")

        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

        # Get blob properties
        blob_properties = blob_client.get_blob_properties()
        blob_size_mb = blob_properties.size / (1024 * 1024)
        logging.info(f"Processing {blob_size_mb:.2f} MB file in chunks")

        # Use temporary file for streaming
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(mode='w+b', delete=False, suffix='.csv') as temp_file:
            try:
                # Stream download to temporary file
                blob_stream = blob_client.download_blob()

                chunk_size = 2 * 1024 * 1024  # 2MB chunks for very large files
                total_written = 0

                logging.info("Downloading in chunks...")
                for chunk in blob_stream.chunks():
                    temp_file.write(chunk)
                    total_written += len(chunk)

                    # Log progress every 100MB
                    if total_written % (100 * 1024 * 1024) == 0:
                        progress_mb = total_written / (1024 * 1024)
                        progress_pct = (progress_mb / blob_size_mb) * 100
                        logging.info(f"Downloaded {progress_mb:.1f} MB / {blob_size_mb:.1f} MB ({progress_pct:.1f}%)")

                temp_file.flush()
                temp_file_path = temp_file.name

                logging.info(f"✓ Download complete: {total_written} bytes")

                # Read CSV in chunks using Polars lazy evaluation
                logging.info("Reading CSV with lazy evaluation...")

                # Use Polars scan_csv for lazy loading
                lazy_df = pl.scan_csv(
                    temp_file_path,
                    infer_schema_length=5000,  # Reduced for very large files
                    try_parse_dates=True,
                    ignore_errors=True,
                    null_values=["", "NULL", "null", "N/A", "n/a"],
                )

                # Collect with streaming to minimize memory usage
                logging.info("Collecting data with streaming...")
                df = lazy_df.collect(streaming=True)

                logging.info(f"✓ Chunked processing complete: {blob_name} - Shape: {df.shape}")
                return df

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_file_path)
                    logging.info("✓ Temporary file cleaned up")
                except:
                    pass

    except Exception as e:
        logging.error(f"Error in chunked processing for {blob_name}: {e}")
        raise

def upload_large_parquet_streaming(df, blob_name, container_name):
    """
    Memory-efficient upload for large Parquet files using chunked processing
    """
    try:
        import tempfile
        import os

        logging.info(f"🚀 Starting memory-efficient Parquet upload for large DataFrame")

        # Use a temporary file to avoid memory issues
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_file:
            temp_path = temp_file.name

        try:
            # Get DataFrame info before processing
            row_count = df.height
            logging.info(f"📊 DataFrame info: {row_count:,} rows, {df.width} columns")

            # For extremely large DataFrames (>1.5M rows), use batch processing
            if row_count > 1500000:
                logging.info("⚠️ Extremely large DataFrame detected - using batch processing")
                return upload_extremely_large_parquet_batched(df, blob_name, container_name)

            # Write DataFrame to temporary file using Polars streaming with memory optimization
            logging.info("📝 Writing DataFrame to temporary file with streaming...")

            # Force garbage collection before writing
            import gc
            gc.collect()

            # Use streaming write with reduced memory footprint
            df.write_parquet(
                temp_path,
                compression='snappy',
                use_pyarrow=False,  # Use native Polars writer for better memory efficiency
                row_group_size=50000  # Smaller row groups for better memory management
            )

            # Force garbage collection after writing
            gc.collect()

            # Get file size
            file_size = os.path.getsize(temp_path)
            logging.info(f"📝 Temporary Parquet file size: {file_size} bytes")

            # Upload the file in chunks to avoid memory issues
            logging.info("🚀 Starting chunked blob upload...")

            # Check if blob service client is available
            if blob_service_client is None:
                logging.error("❌ ERROR: blob_service_client is None - Azure modules not available")
                return False

            # Create container if it doesn't exist
            try:
                blob_service_client.create_container(container_name)
                logging.info(f"✅ Container {container_name} created")
            except Exception as e:
                if "ContainerAlreadyExists" in str(e):
                    logging.info(f"✅ Container {container_name} already exists")
                else:
                    logging.error(f"❌ Error creating container: {e}")
                    return False

            # Upload file using chunked upload
            blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

            with open(temp_path, 'rb') as data:
                blob_client.upload_blob(data, overwrite=True, max_concurrency=4)

            logging.info(f"✅ Successfully uploaded large Parquet file: {blob_name}")

            # Verify upload
            blob_properties = blob_client.get_blob_properties()
            uploaded_size = blob_properties.size
            logging.info(f"✅ Verification - Blob size: {uploaded_size} bytes")

            if uploaded_size == file_size:
                logging.info(f"🎉 SUCCESS: Uploaded Parquet: {blob_name} - Size: {uploaded_size} bytes")
                return True
            else:
                logging.error(f"❌ Size mismatch: Expected {file_size}, got {uploaded_size}")
                return False

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_path)
                logging.info("🧹 Temporary file cleaned up")
            except Exception as e:
                logging.warning(f"⚠️ Could not delete temporary file: {e}")

    except Exception as e:
        logging.error(f"❌ Error in streaming Parquet upload: {str(e)}")
        return False

def upload_extremely_large_parquet_batched(df, blob_name, container_name):
    """
    Handle extremely large DataFrames (>2M rows) by processing in batches
    This prevents memory overflow by writing smaller chunks
    """
    try:
        import tempfile
        import os
        import gc

        row_count = df.height
        logging.info(f"🔥 Processing extremely large DataFrame: {row_count:,} rows")
        log_memory_usage("before batch processing")

        # Calculate batch size based on available memory
        # For very large datasets, use smaller batches to prevent memory overflow
        if row_count > 2500000:  # For datasets >2.5M rows, use very small batches
            batch_size = min(200000, max(50000, row_count // 20))  # Between 50K-200K rows per batch
        else:
            batch_size = min(400000, max(100000, row_count // 10))  # Between 100K-400K rows per batch
        logging.info(f"📦 Using batch size: {batch_size:,} rows")

        # Create temporary directory for batch files
        temp_dir = tempfile.mkdtemp(prefix='parquet_batch_')
        batch_files = []

        try:
            # Process DataFrame in batches
            num_batches = (row_count + batch_size - 1) // batch_size
            logging.info(f"📊 Processing {num_batches} batches...")

            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, row_count)

                logging.info(f"📦 Processing batch {batch_idx + 1}/{num_batches}: rows {start_idx:,} to {end_idx:,}")

                # Extract batch using slice
                batch_df = df.slice(start_idx, end_idx - start_idx)

                # Write batch to temporary file
                batch_file = os.path.join(temp_dir, f'batch_{batch_idx:04d}.parquet')
                batch_df.write_parquet(
                    batch_file,
                    compression='snappy',
                    use_pyarrow=False,
                    row_group_size=25000  # Smaller row groups for batches
                )

                batch_files.append(batch_file)

                # Clean up batch DataFrame
                del batch_df
                gc.collect()

                logging.info(f"✓ Batch {batch_idx + 1} written to temporary file")

                # Log memory usage every few batches
                if (batch_idx + 1) % 3 == 0:
                    log_memory_usage(f"after batch {batch_idx + 1}/{num_batches}")

            # Now combine all batch files into final Parquet file using streaming approach
            logging.info("🔗 Combining batch files into final Parquet...")
            log_memory_usage("before combining batch files")

            # Instead of loading all data into memory, use Polars lazy concatenation
            # and write directly to the final file without collecting
            import polars as pl
            lazy_frames = []

            for batch_file in batch_files:
                lazy_df = pl.scan_parquet(batch_file)
                lazy_frames.append(lazy_df)

            # Concatenate all lazy frames
            combined_lazy = pl.concat(lazy_frames, how="vertical")

            # Write final combined file directly using lazy evaluation
            final_temp_file = os.path.join(temp_dir, 'final_combined.parquet')

            # Use sink_parquet to write directly without collecting into memory
            logging.info("💾 Writing final combined Parquet file with streaming...")
            try:
                # Try using sink_parquet for true streaming write
                combined_lazy.sink_parquet(
                    final_temp_file,
                    compression='snappy',
                    row_group_size=100000
                )
                logging.info("✓ Successfully used streaming write (sink_parquet)")
            except Exception as sink_error:
                logging.warning(f"⚠️ sink_parquet failed: {sink_error}")
                logging.info("🔄 Falling back to collect with streaming...")
                # Fallback to collect with streaming if sink_parquet fails
                combined_df = combined_lazy.collect(streaming=True)
                combined_df.write_parquet(
                    final_temp_file,
                    compression='snappy',
                    use_pyarrow=False,
                    row_group_size=100000
                )
                del combined_df  # Clean up immediately
                gc.collect()

            log_memory_usage("after combining batch files")

            # Clean up intermediate DataFrames
            try:
                del combined_lazy, lazy_frames
            except:
                pass  # Variables might not exist if sink_parquet was used
            gc.collect()

            # Get final file size
            file_size = os.path.getsize(final_temp_file)
            logging.info(f"📝 Final Parquet file size: {file_size} bytes")

            # Upload the final file
            logging.info("🚀 Uploading final combined Parquet file...")

            if blob_service_client is None:
                logging.error("❌ ERROR: blob_service_client is None")
                return False

            # Create container if it doesn't exist
            try:
                blob_service_client.create_container(container_name)
                logging.info(f"✅ Container {container_name} created")
            except Exception as e:
                if "ContainerAlreadyExists" in str(e):
                    logging.info(f"✅ Container {container_name} already exists")
                else:
                    logging.error(f"❌ Error creating container: {e}")
                    return False

            # Upload final file
            blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

            with open(final_temp_file, 'rb') as data:
                blob_client.upload_blob(data, overwrite=True, max_concurrency=4)

            # Verify upload
            blob_properties = blob_client.get_blob_properties()
            uploaded_size = blob_properties.size
            logging.info(f"✅ Verification - Blob size: {uploaded_size} bytes")

            if uploaded_size == file_size:
                logging.info(f"🎉 SUCCESS: Uploaded batched Parquet: {blob_name} - Size: {uploaded_size} bytes")
                return True
            else:
                logging.error(f"❌ Size mismatch: Expected {file_size}, got {uploaded_size}")
                return False

        finally:
            # Clean up all temporary files and directory
            try:
                import shutil
                shutil.rmtree(temp_dir)
                logging.info("🧹 Temporary batch files cleaned up")
            except Exception as e:
                logging.warning(f"⚠️ Could not clean up temporary directory: {e}")

    except Exception as e:
        logging.error(f"❌ Error in batched Parquet upload: {str(e)}")
        return False

def upload_parquet_to_blob(df, filename, container_name, job_key=None):
    """Upload Polars DataFrame as Parquet to blob storage in the destination folder"""
    try:
        # Get the full blob path using the output folder with job_key
        blob_name = get_output_blob_path(filename, job_key)

        logging.info(f"📤 Uploading Parquet: {blob_name} ({df.shape[0]:,} rows)")

        # Ensure destination folder exists
        ensure_destination_folder_exists(container_name, job_key)

        # Memory-efficient Parquet conversion for large DataFrames
        row_count = df.height
        if row_count > 800000:  # For DataFrames with >800K rows, use streaming approach
            logging.info(f"⚠️ Large DataFrame detected ({row_count:,} rows) - using memory-efficient upload")
            return upload_large_parquet_streaming(df, blob_name, container_name)

        # For smaller DataFrames, use the standard approach
        parquet_buffer = BytesIO()
        df.write_parquet(parquet_buffer, compression='snappy')
        parquet_bytes = parquet_buffer.getvalue()

        # Check if blob service client is available
        if blob_service_client is None:
            logging.error("❌ ERROR: blob_service_client is None - Azure modules not available")
            return False

        # Create container if it doesn't exist
        try:
            container_client = blob_service_client.get_container_client(container_name)
            container_client.create_container()
        except Exception as container_error:
            if "ContainerAlreadyExists" not in str(container_error):
                logging.warning(f"⚠️ Container creation info: {container_error}")

        # Upload the Parquet file
        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)
        upload_result = blob_client.upload_blob(parquet_bytes, overwrite=True)

        # Verify the upload by checking if blob exists
        blob_properties = blob_client.get_blob_properties()

        logging.info(f"✅ Uploaded Parquet: {blob_name} ({blob_properties.size} bytes)")
        return True

    except Exception as e:
        logging.error(f"❌ ERROR uploading Parquet {blob_name}: {e}")
        logging.error(f"❌ Error type: {type(e).__name__}")
        import traceback
        logging.error(f"❌ Full traceback: {traceback.format_exc()}")
        return False

def upload_csv_to_blob_streaming(df, filename, container_name, job_key=None):
    """
    Memory-efficient CSV upload using streaming for large DataFrames
    """
    try:
        import tempfile
        import os
        import gc

        # Get the full blob path using the output folder with job_key
        blob_name = get_output_blob_path(filename, job_key)

        logging.info(f"🔄 Starting memory-efficient CSV upload - Blob: {blob_name}, Container: {container_name}")
        logging.info(f"📊 DataFrame shape: {df.shape}")

        row_count = df.height
        if row_count > 1000000:  # For DataFrames with >1M rows, use batch processing
            logging.info(f"⚠️ Large DataFrame detected ({row_count:,} rows) - using batch processing")
            return upload_extremely_large_csv_batched(df, filename, container_name, job_key)

        # Ensure destination folder exists
        ensure_destination_folder_exists(container_name, job_key)

        # Use temporary file to avoid memory issues
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as temp_file:
            temp_path = temp_file.name

            # Force garbage collection before writing
            gc.collect()
            log_memory_usage("before CSV streaming write")

            # Write DataFrame to temporary CSV file using streaming
            logging.info("📝 Writing DataFrame to temporary CSV file with streaming...")
            df.write_csv(temp_path)

            # Get file size
            file_size = os.path.getsize(temp_path)
            logging.info(f"📝 Temporary CSV file size: {file_size} bytes")

            log_memory_usage("after CSV streaming write")

        try:
            # Check if blob service client is available
            if blob_service_client is None:
                logging.error("❌ ERROR: blob_service_client is None - Azure modules not available")
                return False

            # Create container if it doesn't exist
            try:
                container_client = blob_service_client.get_container_client(container_name)
                container_client.create_container()
                logging.info(f"✅ Created container {container_name}")
            except Exception as container_error:
                if "ContainerAlreadyExists" in str(container_error):
                    logging.info(f"✅ Container {container_name} already exists")
                else:
                    logging.warning(f"⚠️ Container creation info: {container_error}")

            # Upload the CSV file using streaming
            logging.info(f"🚀 Starting streaming CSV upload...")
            blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

            with open(temp_path, 'rb') as csv_file:
                upload_result = blob_client.upload_blob(csv_file, overwrite=True)
                logging.info(f"📤 Upload result: {upload_result}")

            # Verify the upload
            blob_properties = blob_client.get_blob_properties()
            uploaded_size = blob_properties.size
            logging.info(f"✅ Verification - Blob size: {uploaded_size} bytes")

            if uploaded_size == file_size:
                logging.info(f"🎉 SUCCESS: Uploaded CSV: {blob_name} - Shape: {df.shape}")
                return True
            else:
                logging.error(f"❌ Size mismatch: Expected {file_size}, got {uploaded_size}")
                return False

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_path)
                logging.info("🧹 Temporary CSV file cleaned up")
            except Exception as e:
                logging.warning(f"⚠️ Could not delete temporary CSV file: {e}")

    except Exception as e:
        logging.error(f"❌ ERROR in streaming CSV upload: {str(e)}")
        import traceback
        logging.error(f"❌ Full traceback: {traceback.format_exc()}")
        return False

def upload_extremely_large_csv_batched(df, filename, container_name, job_key=None):
    """
    Handle extremely large DataFrames (>1M rows) by processing in batches for CSV
    This prevents memory overflow by writing smaller chunks
    """
    try:
        import tempfile
        import os
        import gc

        # Get the full blob path using the output folder with job_key
        blob_name = get_output_blob_path(filename, job_key)

        row_count = df.height
        logging.info(f"🔥 Processing extremely large CSV DataFrame: {row_count:,} rows")
        log_memory_usage("before CSV batch processing")

        # Calculate batch size based on available memory and row count
        # Aim for batches that use ~200MB of memory each
        estimated_row_size = 500  # bytes per row (conservative estimate)
        target_batch_memory = 200 * 1024 * 1024  # 200MB
        batch_size = min(max(target_batch_memory // estimated_row_size, 50000), 200000)  # Between 50K-200K rows

        num_batches = (row_count + batch_size - 1) // batch_size
        logging.info(f"📦 Using batch size: {batch_size:,} rows")
        logging.info(f"📊 Processing {num_batches} batches...")

        # Create temporary directory for batch files
        temp_dir = tempfile.mkdtemp(prefix='csv_batch_')
        batch_files = []

        try:
            # Process each batch
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, row_count)

                logging.info(f"📦 Processing batch {batch_idx + 1}/{num_batches}: rows {start_idx:,} to {end_idx:,}")

                # Extract batch using slice
                batch_df = df.slice(start_idx, end_idx - start_idx)

                # Write batch to temporary CSV file
                batch_file = os.path.join(temp_dir, f'batch_{batch_idx:04d}.csv')
                batch_df.write_csv(batch_file)
                batch_files.append(batch_file)

                logging.info(f"✓ Batch {batch_idx + 1} written to temporary file")

                # Clean up batch DataFrame and force garbage collection every few batches
                del batch_df
                if (batch_idx + 1) % 3 == 0:
                    gc.collect()
                    log_memory_usage(f"after batch {batch_idx + 1}/{num_batches}")

            # Combine all batch files into final CSV
            logging.info("🔗 Combining batch files into final CSV...")
            log_memory_usage("before combining CSV batch files")

            final_csv_path = os.path.join(temp_dir, 'final_combined.csv')

            # Write header from first batch
            with open(batch_files[0], 'r', encoding='utf-8') as first_file:
                header = first_file.readline()

            with open(final_csv_path, 'w', encoding='utf-8') as final_file:
                # Write header
                final_file.write(header)

                # Append data from all batches (skip header for subsequent files)
                for i, batch_file in enumerate(batch_files):
                    with open(batch_file, 'r', encoding='utf-8') as batch:
                        if i == 0:
                            # For first file, skip header since we already wrote it
                            batch.readline()
                        # Copy the rest of the file
                        for line in batch:
                            final_file.write(line)

                    # Clean up batch file immediately after processing
                    try:
                        os.unlink(batch_file)
                    except:
                        pass

            # Get final file size
            final_file_size = os.path.getsize(final_csv_path)
            logging.info(f"📝 Final CSV file size: {final_file_size} bytes")
            log_memory_usage("after combining CSV batch files")

            # Upload the final combined CSV file
            logging.info("🚀 Uploading final combined CSV file...")

            # Ensure destination folder exists
            ensure_destination_folder_exists(container_name, job_key)

            # Check if blob service client is available
            if blob_service_client is None:
                logging.error("❌ ERROR: blob_service_client is None - Azure modules not available")
                return False

            # Create container if it doesn't exist
            try:
                container_client = blob_service_client.get_container_client(container_name)
                container_client.create_container()
                logging.info(f"✅ Created container {container_name}")
            except Exception as container_error:
                if "ContainerAlreadyExists" in str(container_error):
                    logging.info(f"✅ Container {container_name} already exists")
                else:
                    logging.warning(f"⚠️ Container creation info: {container_error}")

            # Upload using chunked upload for large files
            blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

            with open(final_csv_path, 'rb') as csv_file:
                upload_result = blob_client.upload_blob(csv_file, overwrite=True)
                logging.info(f"📤 Upload result: {upload_result}")

            # Verify upload
            blob_properties = blob_client.get_blob_properties()
            uploaded_size = blob_properties.size
            logging.info(f"✅ Verification - Blob size: {uploaded_size} bytes")

            if uploaded_size == final_file_size:
                logging.info(f"🎉 SUCCESS: Uploaded batched CSV: {blob_name} - Size: {uploaded_size} bytes")
                return True
            else:
                logging.error(f"❌ Size mismatch: Expected {final_file_size}, got {uploaded_size}")
                return False

        finally:
            # Clean up all temporary files
            try:
                import shutil
                shutil.rmtree(temp_dir)
                logging.info("🧹 Temporary CSV batch files cleaned up")
            except Exception as e:
                logging.warning(f"⚠️ Could not clean up temporary directory: {e}")

    except Exception as e:
        logging.error(f"❌ Error in batched CSV upload: {str(e)}")
        import traceback
        logging.error(f"❌ Full traceback: {traceback.format_exc()}")
        return False

def upload_csv_to_blob(df, filename, container_name, job_key=None):
    """Upload Polars DataFrame as CSV to blob storage - automatically chooses best method"""
    try:
        row_count = df.height

        # For large DataFrames, use streaming approach
        if row_count > 500000:  # For DataFrames with >500K rows, use streaming approach
            logging.info(f"⚠️ Large DataFrame detected ({row_count:,} rows) - using memory-efficient upload")
            return upload_csv_to_blob_streaming(df, filename, container_name, job_key)

        # For smaller DataFrames, use the standard approach (but still memory-conscious)
        # Get the full blob path using the output folder with job_key
        blob_name = get_output_blob_path(filename, job_key)

        logging.info(f"🔄 Starting CSV upload - Blob: {blob_name}, Container: {container_name}")
        logging.info(f"📊 DataFrame shape: {df.shape}")

        # Ensure destination folder exists
        ensure_destination_folder_exists(container_name, job_key)

        # Convert DataFrame to CSV string using Polars
        csv_string = df.write_csv()
        csv_bytes = csv_string.encode('utf-8')
        logging.info(f"📝 CSV size: {len(csv_bytes)} bytes")

        # Check if blob service client is available
        if blob_service_client is None:
            logging.error("❌ ERROR: blob_service_client is None - Azure modules not available")
            return False

        # Create container if it doesn't exist
        try:
            container_client = blob_service_client.get_container_client(container_name)
            container_client.create_container()
            logging.info(f"✅ Created container {container_name}")
        except Exception as container_error:
            if "ContainerAlreadyExists" in str(container_error):
                logging.info(f"✅ Container {container_name} already exists")
            else:
                logging.warning(f"⚠️ Container creation info: {container_error}")

        # Upload the CSV
        logging.info(f"🚀 Starting blob upload...")
        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)
        upload_result = blob_client.upload_blob(csv_bytes, overwrite=True)
        logging.info(f"📤 Upload result: {upload_result}")

        # Verify the upload by checking if blob exists
        blob_properties = blob_client.get_blob_properties()
        logging.info(f"✅ Verification - Blob size: {blob_properties.size} bytes")

        logging.info(f"🎉 SUCCESS: Uploaded CSV: {blob_name} - Shape: {df.shape}")
        return True

    except Exception as e:
        logging.error(f"❌ ERROR uploading CSV {blob_name}: {e}")
        logging.error(f"❌ Error type: {type(e).__name__}")
        import traceback
        logging.error(f"❌ Full traceback: {traceback.format_exc()}")
        return False

def get_blob_metadata(blob_name, container_name):
    """Get metadata for a blob (useful for audit columns)"""
    try:
        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)
        properties = blob_client.get_blob_properties()

        return {
            'creation_time': properties.creation_time,
            'last_modified': properties.last_modified,
            'content_length': properties.size
        }

    except Exception as e:
        logging.error(f"Error getting blob metadata for {blob_name}: {e}")
        return None

def delete_blob_file(container_name, blob_name):
    """Delete a specific blob file from Azure Blob Storage"""
    try:
        logging.info(f"Attempting to delete blob file: {blob_name}")

        blob_client = blob_service_client.get_blob_client(
            container=container_name,
            blob=blob_name
        )

        # Check if blob exists before attempting to delete
        if blob_client.exists():
            blob_client.delete_blob()
            logging.info(f"✓ Successfully deleted blob file: {blob_name}")
            return True
        else:
            logging.info(f"Blob file {blob_name} does not exist, skipping deletion")
            return False

    except Exception as e:
        logging.error(f"Error deleting blob file {blob_name}: {e}")
        # Don't raise exception for cleanup operations - just log and continue
        return False

def cleanup_placeholder_files(container_name):
    """Remove .placeholder files from the container after successful processing"""
    try:
        logging.info("Cleaning up .placeholder files...")

        container_client = blob_service_client.get_container_client(container_name)
        placeholder_files = []

        # List all blobs in the container
        blob_list = container_client.list_blobs()

        for blob in blob_list:
            if blob.name.endswith('.placeholder'):
                placeholder_files.append(blob.name)

        # Delete each placeholder file
        deleted_count = 0
        for placeholder_file in placeholder_files:
            if delete_blob_file(container_name, placeholder_file):
                deleted_count += 1

        if deleted_count > 0:
            logging.info(f"✓ Cleaned up {deleted_count} .placeholder file(s)")
        else:
            logging.info("No .placeholder files found to clean up")

        return deleted_count

    except Exception as e:
        logging.error(f"Error during placeholder cleanup: {e}")
        # Don't raise exception for cleanup operations - just log and continue
        return 0


def cleanup_source_csv_files(container_name, successfully_processed_files):
    """
    Delete successfully processed CSV files from the source container after successful processing

    Args:
        container_name (str): Name of the source container
        successfully_processed_files (list): List of CSV filenames that were successfully processed

    Returns:
        dict: Summary of deletion results with counts and details
    """
    try:
        logging.info("CLEANUP: Removing successfully processed source CSV files...")
        logging.info(f"Files to delete: {len(successfully_processed_files)}")

        deletion_results = {
            'total_files': len(successfully_processed_files),
            'successfully_deleted': 0,
            'failed_deletions': 0,
            'deleted_files': [],
            'failed_files': []
        }

        if not successfully_processed_files:
            logging.info("No source files to delete")
            return deletion_results

        # Delete each successfully processed file
        for i, filename in enumerate(successfully_processed_files, 1):
            try:
                logging.info(f"Deleting source file {i}/{len(successfully_processed_files)}: {filename}")

                if delete_blob_file(container_name, filename):
                    deletion_results['successfully_deleted'] += 1
                    deletion_results['deleted_files'].append(filename)
                    logging.info(f"  ✓ Successfully deleted: {filename}")
                else:
                    deletion_results['failed_deletions'] += 1
                    deletion_results['failed_files'].append(filename)
                    logging.warning(f"  ⚠️ Failed to delete (file may not exist): {filename}")

            except Exception as e:
                deletion_results['failed_deletions'] += 1
                deletion_results['failed_files'].append(filename)
                logging.error(f"  ❌ Error deleting {filename}: {str(e)}")

        # Log summary
        logging.info("=" * 50)
        logging.info("SOURCE FILE CLEANUP SUMMARY:")
        logging.info(f"  Total files to delete: {deletion_results['total_files']}")
        logging.info(f"  Successfully deleted: {deletion_results['successfully_deleted']}")
        logging.info(f"  Failed deletions: {deletion_results['failed_deletions']}")

        if deletion_results['successfully_deleted'] > 0:
            logging.info(f"  ✓ Deleted files: {deletion_results['deleted_files']}")

        if deletion_results['failed_deletions'] > 0:
            logging.warning(f"  ⚠️ Failed files: {deletion_results['failed_files']}")

        logging.info("=" * 50)

        return deletion_results

    except Exception as e:
        logging.error(f"Error during source CSV file cleanup: {e}")
        # Don't raise exception for cleanup operations - just log and continue
        return {
            'total_files': len(successfully_processed_files) if successfully_processed_files else 0,
            'successfully_deleted': 0,
            'failed_deletions': len(successfully_processed_files) if successfully_processed_files else 0,
            'deleted_files': [],
            'failed_files': successfully_processed_files if successfully_processed_files else [],
            'error': str(e)
        }


def cleanup_intermediate_parquet_files(container_name, intermediate_parquet_files):
    """
    Delete intermediate parquet files that were created during CSV-to-Parquet conversion
    These are the individual parquet files created before consolidation into a combined file

    Args:
        container_name (str): Name of the container
        intermediate_parquet_files (list): List of intermediate parquet file paths to delete

    Returns:
        int: Number of files successfully deleted
    """
    try:
        logging.info("🧹 CLEANUP: Removing intermediate parquet files...")
        logging.info(f"Files to delete: {len(intermediate_parquet_files)}")

        if not intermediate_parquet_files:
            logging.info("No intermediate parquet files to delete")
            return 0

        deleted_count = 0
        failed_count = 0

        # Delete each intermediate parquet file
        for i, file_path in enumerate(intermediate_parquet_files, 1):
            try:
                # Extract just the filename from the full path
                # file_path format: "CUSTOMER-ACCTG/customer-acctg-enhanced/CUSTOMER-ACCTG_1_08-20-25_05.31.00.parquet"
                blob_name = file_path

                logging.info(f"Deleting intermediate parquet file {i}/{len(intermediate_parquet_files)}: {blob_name}")

                if delete_blob_file(container_name, blob_name):
                    deleted_count += 1
                    logging.info(f"  ✓ Successfully deleted: {blob_name}")
                else:
                    failed_count += 1
                    logging.warning(f"  ⚠️ Failed to delete (file may not exist): {blob_name}")

            except Exception as e:
                failed_count += 1
                logging.error(f"  ❌ Error deleting {file_path}: {str(e)}")

        # Log summary
        logging.info("=" * 50)
        logging.info("INTERMEDIATE PARQUET CLEANUP SUMMARY:")
        logging.info(f"  Total files to delete: {len(intermediate_parquet_files)}")
        logging.info(f"  Successfully deleted: {deleted_count}")
        logging.info(f"  Failed deletions: {failed_count}")
        logging.info("=" * 50)

        return deleted_count

    except Exception as e:
        logging.error(f"Error during intermediate parquet file cleanup: {e}")
        # Don't raise exception for cleanup operations - just log and continue
        return 0


def move_zip_files_to_archive_dealer_aware(container_name, job_key):
    """
    Move zip files to archive using simplified logic based on Folder and SourceSubfolder configuration

    Args:
        container_name (str): Name of the container
        job_key (str): Job key for configuration

    Returns:
        dict: Results of the zip file movement operation
    """
    try:
        from azure_config import get_job_config

        logging.info("=" * 60)
        logging.info("=== SIMPLIFIED ZIP FILE ARCHIVE MOVEMENT START ===")
        logging.info("=" * 60)
        logging.info(f"Job key: {job_key}")
        logging.info(f"Container: {container_name}")

        # Get job configuration
        job_config = get_job_config(job_key)
        if not job_config:
            logging.error(f"❌ No job configuration found for job key: {job_key}")
            return {
                'status': 'error',
                'total_files': 0,
                'successfully_moved': 0,
                'failed_movements': 0,
                'moved_files': [],
                'failed_files': [],
                'error': f'No job configuration found for job key: {job_key}'
            }

        # Extract Folder and SourceSubfolder from configuration
        folder = job_config.get('Folder', '')
        source_subfolder = job_config.get('SourceSubfolder', '')

        if not folder or not source_subfolder:
            logging.error(f"❌ Missing required configuration: Folder='{folder}', SourceSubfolder='{source_subfolder}'")
            return {
                'status': 'error',
                'total_files': 0,
                'successfully_moved': 0,
                'failed_movements': 0,
                'moved_files': [],
                'failed_files': [],
                'error': f'Missing required configuration: Folder or SourceSubfolder'
            }

        # Derive source folder: change "decompressed" to "compressed" in SourceSubfolder
        if 'decompressed' in source_subfolder:
            compressed_subfolder = source_subfolder.replace('decompressed', 'compressed')
        else:
            logging.warning(f"⚠️ SourceSubfolder '{source_subfolder}' does not contain 'decompressed', using as-is with '-compressed' suffix")
            compressed_subfolder = f"{source_subfolder}-compressed"

        # Build source and destination paths
        source_folder = f"{folder}/{compressed_subfolder}"
        archive_folder = f"{folder}/archive"

        logging.info(f"📁 Source folder: {source_folder}")
        logging.info(f"📁 Archive folder: {archive_folder}")
        logging.info(f"📦 Moving ALL files from compressed folder to archive folder")

        # Use existing move_zip_files_to_archive function with wildcard pattern to move all files
        return move_zip_files_to_archive(
            container_name,
            source_folder,
            archive_folder,
            "*"  # Move all files, not just zip files
        )

    except Exception as e:
        logging.error(f"Error in simplified zip archiving: {e}")
        return {
            'status': 'error',
            'total_files': 0,
            'successfully_moved': 0,
            'failed_movements': 0,
            'moved_files': [],
            'failed_files': [],
            'error': str(e)
        }

def _move_dealer_zip_files_to_archive(container_name, job_key):
    """
    Internal function to handle dealer-specific zip file archiving

    Args:
        container_name (str): Name of the container
        job_key (str): Job key for configuration

    Returns:
        dict: Summary of movement results
    """
    try:
        from azure_config import (
            get_dealer_zip_source_folders,
            get_dealer_zip_archive_folder,
            get_dealer_zip_file_pattern_for_dealer
        )

        # Get all dealer zip source folders
        dealer_zip_folders = get_dealer_zip_source_folders(job_key)

        if not dealer_zip_folders:
            logging.info("No dealer zip folders found for archiving")
            return {
                'status': 'no_dealers',
                'total_files': 0,
                'successfully_moved': 0,
                'failed_movements': 0,
                'moved_files': [],
                'failed_files': [],
                'message': 'No dealer folders found for zip archiving'
            }

        logging.info(f"Found {len(dealer_zip_folders)} dealer zip folders to process:")
        for folder in dealer_zip_folders:
            logging.info(f"  - {folder}")

        # Aggregate results from all dealers
        total_results = {
            'status': 'success',
            'total_files': 0,
            'successfully_moved': 0,
            'failed_movements': 0,
            'moved_files': [],
            'failed_files': [],
            'dealer_results': []
        }

        # Process each dealer folder
        for dealer_zip_source in dealer_zip_folders:
            try:
                # Extract dealer folder name from path (e.g., "RO_Time/AO_RO_TIME/ao_ro_time-compressed" -> "AO_RO_TIME")
                path_parts = dealer_zip_source.split('/')
                if len(path_parts) >= 2:
                    dealer_folder = path_parts[1]  # AO_RO_TIME
                else:
                    logging.warning(f"Could not extract dealer folder from path: {dealer_zip_source}")
                    continue

                # Get dealer-specific archive folder and file pattern
                dealer_archive_folder = get_dealer_zip_archive_folder(dealer_folder, job_key)
                dealer_file_pattern = get_dealer_zip_file_pattern_for_dealer(dealer_folder, job_key)

                logging.info(f"🔄 Processing dealer: {dealer_folder}")
                logging.info(f"  Source: {dealer_zip_source}")
                logging.info(f"  Archive: {dealer_archive_folder}")
                logging.info(f"  Pattern: {dealer_file_pattern}")

                # Move zip files for this dealer using existing function
                dealer_result = move_zip_files_to_archive(
                    container_name,
                    dealer_zip_source,
                    dealer_archive_folder,
                    dealer_file_pattern
                )

                # Add dealer info to result
                dealer_result['dealer_folder'] = dealer_folder
                dealer_result['dealer_source'] = dealer_zip_source
                dealer_result['dealer_archive'] = dealer_archive_folder

                # Aggregate results
                total_results['total_files'] += dealer_result.get('total_files', 0)
                total_results['successfully_moved'] += dealer_result.get('successfully_moved', 0)
                total_results['failed_movements'] += dealer_result.get('failed_movements', 0)
                total_results['moved_files'].extend(dealer_result.get('moved_files', []))
                total_results['failed_files'].extend(dealer_result.get('failed_files', []))
                total_results['dealer_results'].append(dealer_result)

                logging.info(f"✅ Completed dealer {dealer_folder}: {dealer_result.get('successfully_moved', 0)} files moved")

            except Exception as e:
                logging.error(f"❌ Error processing dealer folder {dealer_zip_source}: {e}")
                total_results['failed_movements'] += 1
                total_results['failed_files'].append({
                    'dealer_folder': dealer_zip_source,
                    'error': str(e)
                })

        # Log final summary
        logging.info("=" * 60)
        logging.info("=== DEALER ZIP ARCHIVING SUMMARY ===")
        logging.info(f"Total dealers processed: {len(dealer_zip_folders)}")
        logging.info(f"Total files found: {total_results['total_files']}")
        logging.info(f"Successfully moved: {total_results['successfully_moved']}")
        logging.info(f"Failed movements: {total_results['failed_movements']}")
        logging.info("=" * 60)

        return total_results

    except Exception as e:
        logging.error(f"Error in dealer zip archiving: {e}")
        return {
            'status': 'error',
            'total_files': 0,
            'successfully_moved': 0,
            'failed_movements': 0,
            'moved_files': [],
            'failed_files': [],
            'error': str(e)
        }

def move_zip_files_to_archive(container_name, source_folder, archive_folder, file_pattern):
    """
    Move files from source folder to archive folder within the same container

    Args:
        container_name (str): Name of the container
        source_folder (str): Source folder path (e.g., 'FIMAST-SALES/fimastsales-compressed')
        archive_folder (str): Archive folder path (e.g., 'FIMAST-SALES/archive')
        file_pattern (str): File pattern to match (e.g., 'FIMASTSALES*.zip', '*' for all files)

    Returns:
        dict: Summary of movement results with counts and details
    """
    try:
        logging.info("=" * 60)
        logging.info("=== FILE ARCHIVE MOVEMENT START ===")
        logging.info("=" * 60)
        logging.info(f"Moving files from {source_folder} to {archive_folder}")
        logging.info(f"Container: {container_name}")
        logging.info(f"File pattern: {file_pattern}")

        movement_results = {
            'total_files': 0,
            'successfully_moved': 0,
            'failed_movements': 0,
            'moved_files': [],
            'failed_files': [],
            'source_folder': source_folder,
            'archive_folder': archive_folder,
            'file_pattern': file_pattern
        }

        container_client = blob_service_client.get_container_client(container_name)

        # List all files in the source folder matching the pattern
        files_to_move = []
        blob_list = container_client.list_blobs(name_starts_with=source_folder)

        for blob in blob_list:
            blob_name = blob.name
            # Extract just the filename from the full path
            filename = blob_name.split('/')[-1]

            # Skip if it's a folder (ends with / or has no filename)
            if filename == '' or blob_name.endswith('/'):
                continue

            # Skip if the blob name is exactly the source folder (directory itself)
            if blob_name == source_folder:
                continue

            # Check if it matches the pattern
            if file_pattern == "*":
                # Move all files
                files_to_move.append(blob_name)
            elif file_pattern.endswith('*.zip'):
                # Original zip file logic
                pattern_prefix = file_pattern.replace('*.zip', '')
                if filename.startswith(pattern_prefix) and filename.endswith('.zip'):
                    files_to_move.append(blob_name)
            else:
                # Generic pattern matching for other patterns
                import fnmatch
                if fnmatch.fnmatch(filename, file_pattern):
                    files_to_move.append(blob_name)

        movement_results['total_files'] = len(files_to_move)

        if not files_to_move:
            logging.info(f"No files found matching pattern '{file_pattern}' in {source_folder}")
            logging.info("=== FILE ARCHIVE MOVEMENT END ===")
            return movement_results

        logging.info(f"Found {len(files_to_move)} files to move:")
        for file_to_move in files_to_move:
            logging.info(f"  - {file_to_move}")

        # Ensure archive folder exists by creating a placeholder if needed
        archive_placeholder = f"{archive_folder}/.placeholder"
        try:
            # Check if archive folder exists by trying to list blobs in it
            archive_blobs = list(container_client.list_blobs(name_starts_with=archive_folder))
            # Take only first blob to check if folder exists
            archive_blobs = archive_blobs[:1] if archive_blobs else []
            if not archive_blobs:
                logging.info(f"Creating archive folder: {archive_folder}")
                # Create placeholder to ensure folder exists
                placeholder_client = blob_service_client.get_blob_client(
                    container=container_name,
                    blob=archive_placeholder
                )
                placeholder_client.upload_blob("placeholder", overwrite=True)
                logging.info(f"✓ Archive folder created with placeholder: {archive_placeholder}")
        except Exception as e:
            logging.warning(f"Could not verify/create archive folder: {e}")

        # Move each file
        for i, source_blob_name in enumerate(files_to_move, 1):
            try:
                # Extract filename from source path
                filename = source_blob_name.split('/')[-1]
                destination_blob_name = f"{archive_folder}/{filename}"

                logging.info(f"Moving file {i}/{len(files_to_move)}: {filename}")
                logging.info(f"  Source: {source_blob_name}")
                logging.info(f"  Destination: {destination_blob_name}")

                # Get source blob client
                source_blob_client = blob_service_client.get_blob_client(
                    container=container_name,
                    blob=source_blob_name
                )

                # Get destination blob client
                dest_blob_client = blob_service_client.get_blob_client(
                    container=container_name,
                    blob=destination_blob_name
                )

                # Check if source blob exists
                if not source_blob_client.exists():
                    logging.warning(f"  ⚠️ Source file does not exist: {source_blob_name}")
                    movement_results['failed_movements'] += 1
                    movement_results['failed_files'].append({
                        'filename': filename,
                        'source': source_blob_name,
                        'destination': destination_blob_name,
                        'error': 'Source file does not exist'
                    })
                    continue

                # Copy the blob to the new location
                copy_source = source_blob_client.url
                dest_blob_client.start_copy_from_url(copy_source)

                # Wait for copy to complete (for small files this should be immediate)
                copy_properties = dest_blob_client.get_blob_properties()
                copy_status = copy_properties.copy.status

                if copy_status == 'success':
                    # Delete the original file after successful copy
                    source_blob_client.delete_blob()

                    movement_results['successfully_moved'] += 1
                    movement_results['moved_files'].append({
                        'filename': filename,
                        'source': source_blob_name,
                        'destination': destination_blob_name
                    })
                    logging.info(f"  ✓ Successfully moved: {filename}")
                else:
                    movement_results['failed_movements'] += 1
                    movement_results['failed_files'].append({
                        'filename': filename,
                        'source': source_blob_name,
                        'destination': destination_blob_name,
                        'error': f'Copy failed with status: {copy_status}'
                    })
                    logging.error(f"  ❌ Copy failed for {filename}: {copy_status}")

            except Exception as e:
                movement_results['failed_movements'] += 1
                movement_results['failed_files'].append({
                    'filename': filename if 'filename' in locals() else 'unknown',
                    'source': source_blob_name,
                    'destination': destination_blob_name if 'destination_blob_name' in locals() else 'unknown',
                    'error': str(e)
                })
                logging.error(f"  ❌ Error moving {source_blob_name}: {str(e)}")

        # Clean up the placeholder file if we created it and there are now real files
        try:
            if movement_results['successfully_moved'] > 0:
                placeholder_client = blob_service_client.get_blob_client(
                    container=container_name,
                    blob=archive_placeholder
                )
                if placeholder_client.exists():
                    placeholder_client.delete_blob()
                    logging.info(f"✓ Cleaned up placeholder file: {archive_placeholder}")
        except Exception as e:
            logging.warning(f"Could not clean up placeholder file: {e}")

        # Log final results
        logging.info("=" * 60)
        logging.info("=== ZIP FILE ARCHIVE MOVEMENT SUMMARY ===")
        logging.info(f"Total files found: {movement_results['total_files']}")
        logging.info(f"Successfully moved: {movement_results['successfully_moved']}")
        logging.info(f"Failed movements: {movement_results['failed_movements']}")

        if movement_results['moved_files']:
            logging.info("Successfully moved files:")
            for moved_file in movement_results['moved_files']:
                logging.info(f"  ✓ {moved_file['filename']}")

        if movement_results['failed_files']:
            logging.info("Failed to move files:")
            for failed_file in movement_results['failed_files']:
                logging.info(f"  ❌ {failed_file['filename']}: {failed_file['error']}")

        logging.info("=== FILE ARCHIVE MOVEMENT END ===")
        logging.info("=" * 60)

        return movement_results

    except Exception as e:
        logging.error(f"Error during zip file archive movement: {e}")
        return {
            'total_files': 0,
            'successfully_moved': 0,
            'failed_movements': 0,
            'moved_files': [],
            'failed_files': [],
            'source_folder': source_folder,
            'archive_folder': archive_folder,
            'file_pattern': file_pattern,
            'error': str(e)
        }
