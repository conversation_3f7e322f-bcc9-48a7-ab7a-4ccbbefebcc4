import os
import sys
import polars as pl
import logging
import hashlib
from datetime import datetime

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from azure_config import (
    SURROGATE_KEY_COLUMN,
    AUDIT_COLUMNS,
    get_surrogate_key_columns,
    get_surrogate_key_separator,
    get_surrogate_key_method
)

# Import file_utils from the same directory
try:
    from file_utils import get_file_info_from_name
except ImportError:
    # Fallback function if file_utils is not available
    def get_file_info_from_name(filename):
        return {
            'original_filename': filename,
            'prefix': None,
            'date_part': None,
            'source_date': None
        }

def extract_file_info(filename):
    """Extract file information - wrapper for compatibility"""
    return get_file_info_from_name(filename)

def create_md5_hash(concatenated_string):
    """Create MD5 hash of a concatenated string"""
    return hashlib.md5(concatenated_string.encode('utf-8')).hexdigest()

def create_hash(concatenated_string, method='MD5'):
    """Create hash of a concatenated string using specified method"""
    if method.upper() == 'MD5':
        return hashlib.md5(concatenated_string.encode('utf-8')).hexdigest()
    elif method.upper() == 'SHA256':
        return hashlib.sha256(concatenated_string.encode('utf-8')).hexdigest()
    elif method.upper() == 'SHA1':
        return hashlib.sha1(concatenated_string.encode('utf-8')).hexdigest()
    else:
        # Default to MD5 for unknown methods
        logging.warning(f"Unknown hash method '{method}', defaulting to MD5")
        return hashlib.md5(concatenated_string.encode('utf-8')).hexdigest()

def clean_composite_key_string(concatenated_string, separator='|'):
    """
    Clean composite key string by removing trailing empty values and separators

    Args:
        concatenated_string: The full concatenated string (e.g., "11|22|||")
        separator: The separator character used (e.g., "|")

    Returns:
        Cleaned string without trailing empty values and separators (e.g., "11|22")
    """
    if not concatenated_string:
        return ""

    # Split by separator and remove trailing empty strings
    parts = concatenated_string.split(separator)

    # Remove trailing empty parts
    while parts and parts[-1] == "":
        parts.pop()

    # Rejoin with separator
    return separator.join(parts)

def add_surrogate_key_polars(df):
    """Add composite key using MD5 hash of concatenated values - HARD-CODED LOGIC"""
    try:
        logging.info("=== ADDING COMPOSITE KEY (POLARS) - MD5 HASH ===")

        original_shape = df.shape
        logging.info(f"Original DataFrame shape: {original_shape}")

        # Get all columns (regardless of data type)
        all_columns = df.columns
        logging.info(f"All columns: {all_columns}")

        # HARD-CODED COMPOSITE KEY LOGIC
        # Create MD5 hash of concatenated values with pipe separator:
        # S = ST/BR + "|" + DEAL_NO + "|" + "" + "|" + ""
        # Composite Key = MD5(S)

        required_columns = ["ST/BR", "DEAL NO"]
        missing_columns = [col for col in required_columns if col not in all_columns]

        if missing_columns:
            logging.warning(f"⚠️ Missing required columns for composite key: {missing_columns}")
            logging.warning("Available columns: " + str(all_columns))
            # Fallback to row index if required columns are missing
            df_with_key = df.with_row_index(SURROGATE_KEY_COLUMN, offset=1)
            logging.info("Composite key logic: Row index (required columns missing)")
        else:
            # Create concatenated string with pipe separators
            # S = ST/BR + "|" + DEAL_NO + "|" + "" + "|" + ""
            concatenated_col = (
                pl.col("ST/BR").cast(pl.Utf8).fill_null("") +
                pl.lit("|") +
                pl.col("DEAL NO").cast(pl.Utf8).fill_null("") +
                pl.lit("|") +
                pl.lit("") +  # placeholder column 3
                pl.lit("|") +
                pl.lit("")    # placeholder column 4
            )

            # Apply MD5 hash to the concatenated string
            df_with_key = df.with_columns([
                concatenated_col.map_elements(create_md5_hash, return_dtype=pl.Utf8).alias(SURROGATE_KEY_COLUMN)
            ])

            logging.info("Composite key logic: MD5 hash of 'ST/BR|DEAL_NO||' concatenation")

        # Move composite key to first position
        columns = df_with_key.columns
        surrogate_col = [SURROGATE_KEY_COLUMN]
        other_cols = [col for col in columns if col != SURROGATE_KEY_COLUMN]
        df_reordered = df_with_key.select(surrogate_col + other_cols)

        # Log sample values for verification
        if df_reordered.height > 0:
            sample_values = df_reordered.select(pl.col(SURROGATE_KEY_COLUMN).head(3)).to_series().to_list()
            logging.info(f"Sample composite key values (MD5): {sample_values}")

            # Also log the original concatenated strings for debugging
            if not missing_columns:
                sample_concat = df.select(
                    (pl.col("ST/BR").cast(pl.Utf8).fill_null("") +
                     pl.lit("|") +
                     pl.col("DEAL NO").cast(pl.Utf8).fill_null("") +
                     pl.lit("||")).head(3)
                ).to_series().to_list()
                logging.info(f"Sample concatenated strings: {sample_concat}")

        logging.info(f"✓ Composite key added - New shape: {df_reordered.shape}")
        return df_reordered

    except Exception as e:
        logging.error(f"❌ Error adding composite key: {str(e)}")
        raise

def add_surrogate_key_configurable(df, job_key=None):
    """Add surrogate key and composite key columns using configurable logic from job configuration"""
    try:
        logging.info("=== ADDING SURROGATE KEY & COMPOSITE KEY (CONFIGURABLE) ===")

        original_shape = df.shape
        logging.info(f"Original DataFrame shape: {original_shape}")

        # Get all columns
        all_columns = df.columns
        logging.info(f"All columns: {all_columns}")

        # Get configuration for this job
        surrogate_columns_str = get_surrogate_key_columns(job_key)
        separator = get_surrogate_key_separator(job_key)
        hash_method = get_surrogate_key_method(job_key)

        logging.info(f"Surrogate key configuration for job '{job_key}':")
        logging.info(f"  - Columns: {surrogate_columns_str}")
        logging.info(f"  - Separator: '{separator}'")
        logging.info(f"  - Hash method: {hash_method}")

        # Parse the comma-separated column names
        surrogate_columns = [col.strip() for col in surrogate_columns_str.split(',')]
        logging.info(f"Parsed columns: {surrogate_columns}")

        # Check if required columns exist (ignore empty strings)
        required_columns = [col for col in surrogate_columns if col]
        missing_columns = [col for col in required_columns if col not in all_columns]

        if missing_columns:
            logging.warning(f"⚠️ Missing required columns for composite key: {missing_columns}")
            logging.warning("Available columns: " + str(all_columns))
            # Fallback to row index if required columns are missing
            df_with_key = df.with_columns([
                pl.int_range(pl.len()).add(1).alias("Surrogate Key"),
                pl.lit("").alias("Composite Key")
            ])
            logging.info("Composite key logic: Row index (required columns missing)")
        else:
            # Build concatenation expression dynamically
            concat_parts = []
            for i, col in enumerate(surrogate_columns):
                if i > 0:
                    concat_parts.append(pl.lit(separator))

                if col:  # Non-empty column name
                    concat_parts.append(pl.col(col).cast(pl.Utf8).fill_null(""))
                else:  # Empty column (placeholder)
                    concat_parts.append(pl.lit(""))

            # Create concatenated string
            concatenated_col = concat_parts[0]
            for part in concat_parts[1:]:
                concatenated_col = concatenated_col + part

            # Create both columns:
            # 1. Surrogate Key: hashed value
            # 2. Composite Key: cleaned concatenated string (without trailing empty values)
            df_with_key = df.with_columns([
                concatenated_col.map_elements(
                    lambda x: create_hash(x, hash_method),
                    return_dtype=pl.Utf8
                ).alias("Surrogate Key"),
                concatenated_col.map_elements(
                    lambda x: clean_composite_key_string(x, separator),
                    return_dtype=pl.Utf8
                ).alias("Composite Key")
            ])

            logging.info(f"Surrogate key logic: {hash_method} hash of '{surrogate_columns_str}' with separator '{separator}'")
            logging.info("Composite key logic: Cleaned concatenated string (trailing empty values removed)")

        # Move both keys to first positions (Surrogate Key first, then Composite Key)
        columns = df_with_key.columns
        key_cols = ["Surrogate Key", "Composite Key"]
        other_cols = [col for col in columns if col not in key_cols]
        df_reordered = df_with_key.select(key_cols + other_cols)

        # Log sample values for verification
        if df_reordered.height > 0:
            sample_surrogate = df_reordered.select(pl.col("Surrogate Key").head(3)).to_series().to_list()
            sample_composite = df_reordered.select(pl.col("Composite Key").head(3)).to_series().to_list()
            logging.info(f"Sample surrogate key values ({hash_method}): {sample_surrogate}")
            logging.info(f"Sample composite key values (cleaned): {sample_composite}")

            # Also log the original concatenated strings for debugging
            if not missing_columns:
                sample_concat = df.select(concatenated_col.head(3)).to_series().to_list()
                logging.info(f"Sample full concatenated strings (for hashing): {sample_concat}")

        logging.info(f"✓ Surrogate Key and Composite Key added - New shape: {df_reordered.shape}")
        return df_reordered

    except Exception as e:
        logging.error(f"❌ Error adding configurable composite key: {str(e)}")
        raise

def append_audit_columns_polars(df, source_files=None):
    """Append audit columns using Polars expressions"""
    try:
        logging.info("=== APPENDING AUDIT COLUMNS (POLARS) ===")

        current_timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')

        # Extract source datetime from filename if available
        source_datetime_str = current_timestamp
        if source_files and len(source_files) > 0:
            # Use existing filename parsing logic
            file_info = extract_file_info(source_files[0])
            if file_info.get('source_date'):
                source_datetime_str = file_info['source_date']

        # Add audit columns using Polars expressions
        df_with_audit = df.with_columns([
            pl.lit(source_datetime_str).alias(AUDIT_COLUMNS[0]),  # Source Date/Time
            pl.lit(current_timestamp).alias(AUDIT_COLUMNS[1]),    # Created Date/Time
            pl.lit(current_timestamp).alias(AUDIT_COLUMNS[2])     # Modified Date/Time
        ])

        logging.info(f"✓ Audit columns appended - Final shape: {df_with_audit.shape}")
        return df_with_audit

    except Exception as e:
        logging.error(f"❌ Error appending audit columns: {str(e)}")
        raise

def add_surrogate_key(df):
    """
    Legacy function - Add Composite Key as the FIRST column (leftmost position)
    Updated to use MD5 hash-based composite key logic

    Critical Requirement: Must be added as the FIRST column, not the typical rightmost position

    Args:
        df: Polars DataFrame

    Returns:
        Polars DataFrame with composite key as the first column
    """

    try:
        logging.info("=" * 40)
        logging.info("=== ADDING COMPOSITE KEY START (LEGACY) ===")
        logging.info("=" * 40)

        # Use the new MD5 hash-based logic
        return add_surrogate_key_polars(df)

    except Exception as e:
        logging.error(f"❌ Failed to add surrogate key: {str(e)}")
        raise

def append_audit_columns(df, source_filenames=None):
    """
    Legacy function - Add audit columns to the rightmost positions
    Updated to use Polars instead of pandas

    Audit Columns (added to the right side):
    1. Source Date/Time: Extract from original filename and/or file metadata
    2. Created Date/Time: Current timestamp when the function app processes the file
    3. Modified Date/Time: Same value as Created Date/Time

    Args:
        df: Polars DataFrame
        source_filenames: List of source filenames or single filename

    Returns:
        Polars DataFrame with audit columns appended to the right
    """

    try:
        logging.info("=" * 40)
        logging.info("=== APPENDING AUDIT COLUMNS START ===")
        logging.info("=" * 40)

        original_shape = df.shape
        original_columns = df.columns

        logging.info(f"Original DataFrame shape: {original_shape}")
        logging.info(f"Original columns ({len(original_columns)}): {original_columns}")

        # Get current timestamp for Created and Modified Date/Time
        current_timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        logging.info(f"Current timestamp: {current_timestamp}")

        # Handle source filenames
        if source_filenames is None:
            # Try to get from _source_file column if it exists
            if "_source_file" in df.columns:
                source_filenames = df.select(pl.col("_source_file").unique()).to_series().to_list()
            else:
                source_filenames = ['unknown']

        if isinstance(source_filenames, str):
            source_filenames = [source_filenames]

        logging.info(f"Source filenames: {source_filenames}")

        # Extract source date/time from filenames
        source_datetime_str = "Unknown"
        if source_filenames and source_filenames[0] != 'unknown':
            # Try to extract date from the first filename
            file_info = extract_file_info(source_filenames[0])
            if file_info.get('source_date'):
                # Use the properly parsed datetime from the filename
                source_datetime_str = file_info['source_date']
                logging.info(f"Extracted and parsed source date from filename: {source_datetime_str}")
            elif file_info.get('date_part'):
                # Fallback to raw date part if parsing failed
                source_datetime_str = f"Raw date: {file_info['date_part']}"
                logging.info(f"Using raw date part from filename: {source_datetime_str}")
            else:
                source_datetime_str = f"From file: {source_filenames[0]}"
                logging.info(f"Could not extract date, using filename: {source_datetime_str}")

        # Add audit columns using Polars expressions
        df_with_audit = df.with_columns([
            pl.lit(source_datetime_str).alias(AUDIT_COLUMNS[0]),  # Source Date/Time
            pl.lit(current_timestamp).alias(AUDIT_COLUMNS[1]),    # Created Date/Time
            pl.lit(current_timestamp).alias(AUDIT_COLUMNS[2])     # Modified Date/Time
        ])

        for col_name in AUDIT_COLUMNS:
            logging.info(f"✓ Added audit column: '{col_name}'")

        new_shape = df_with_audit.shape
        new_columns = df_with_audit.columns

        logging.info(f"New DataFrame shape: {new_shape}")
        logging.info(f"New columns ({len(new_columns)}): {new_columns}")

        # Verify audit columns are at the end
        last_three_columns = new_columns[-3:]
        if last_three_columns == AUDIT_COLUMNS:
            logging.info("✓ VERIFICATION PASSED: Audit columns are the last three columns")
        else:
            logging.warning(f"⚠️  VERIFICATION WARNING: Expected {AUDIT_COLUMNS} as last columns, got {last_three_columns}")

        logging.info("=== APPENDING AUDIT COLUMNS END ===")
        logging.info("=" * 40)

        return df_with_audit

    except Exception as e:
        logging.error(f"❌ Failed to append audit columns: {str(e)}")
        import traceback
        logging.error(f"Full traceback: {traceback.format_exc()}")
        logging.error(f"DataFrame shape at error: {df.shape if 'df' in locals() else 'N/A'}")
        logging.error(f"DataFrame columns at error: {df.columns if 'df' in locals() else 'N/A'}")
        raise

def log_raw_csv_enhanced(df):
    """
    Raw CSV Enhanced (GREEN BOX - Logging Required)
    Updated to work with Polars DataFrames

    Log the successful addition of the Surrogate Key column and validate enhanced CSV structure
    """

    try:
        # GREEN BOX LOGGING START
        logging.info("=" * 50)
        logging.info("=== RAW CSV ENHANCED START ===")
        logging.info("=" * 50)

        # Validate surrogate key presence
        columns = df.columns

        if SURROGATE_KEY_COLUMN in columns:
            surrogate_position = columns.index(SURROGATE_KEY_COLUMN)
            logging.info(f"✓ Surrogate key column found: '{SURROGATE_KEY_COLUMN}'")
            logging.info(f"✓ Surrogate key position: {surrogate_position} (0-based index)")

            if surrogate_position == 0:
                logging.info("✓ VALIDATION PASSED: Surrogate key is in the correct position (first column)")
            else:
                logging.error(f"❌ VALIDATION FAILED: Surrogate key should be at position 0, found at position {surrogate_position}")
        else:
            logging.error(f"❌ VALIDATION FAILED: Surrogate key column '{SURROGATE_KEY_COLUMN}' not found")

        # Log enhanced CSV structure
        logging.info(f"Enhanced CSV structure:")
        logging.info(f"  - Total rows: {df.height}")
        logging.info(f"  - Total columns: {df.width}")
        logging.info(f"  - Surrogate key column: '{SURROGATE_KEY_COLUMN}' (position 0)")
        logging.info(f"  - Data columns: {df.width - 1}")

        # Log column summary
        logging.info("Column summary:")
        for i, col in enumerate(columns):
            col_type = "SURROGATE KEY" if col == SURROGATE_KEY_COLUMN else "DATA"
            logging.info(f"  {i:2d}. {col} ({col_type})")

        # Sample data preview
        if df.height > 0:
            logging.info("Sample surrogate key values:")
            sample_values = df.select(pl.col(SURROGATE_KEY_COLUMN).head(3)).to_series().to_list()
            for i, val in enumerate(sample_values):
                logging.info(f"  Row {i}: {val}")

        logging.info("✓ Raw CSV enhancement validation completed successfully")
        logging.info("=== RAW CSV ENHANCED END ===")
        logging.info("=" * 50)
        # GREEN BOX LOGGING END

        return True

    except Exception as e:
        logging.error(f"❌ Raw CSV enhanced validation failed: {str(e)}")
        raise

def ensure_consistent_schema(df, expected_schema=None, file_name="unknown"):
    """
    Ensure DataFrame has consistent schema for consolidation

    Args:
        df: Polars DataFrame to align
        expected_schema: Dict of column_name -> polars_dtype (optional)
        file_name: Name of file for logging

    Returns:
        Polars DataFrame with aligned schema
    """
    try:
        logging.info(f"🔧 Ensuring consistent schema for file: {file_name}")

        # If no expected schema provided, use current schema as baseline
        if expected_schema is None:
            expected_schema = {col: df[col].dtype for col in df.columns}
            logging.info(f"📋 Using current schema as baseline: {list(expected_schema.keys())}")
            return df, expected_schema

        # Check for missing columns and add them with null values
        current_columns = set(df.columns)
        expected_columns = set(expected_schema.keys())

        missing_columns = expected_columns - current_columns
        extra_columns = current_columns - expected_columns

        if missing_columns:
            logging.info(f"➕ Adding missing columns to {file_name}: {missing_columns}")
            for col in missing_columns:
                df = df.with_columns([pl.lit(None).cast(expected_schema[col]).alias(col)])

        if extra_columns:
            logging.info(f"➕ Adding extra columns from {file_name} to expected schema: {extra_columns}")
            # Add extra columns to expected schema for future files
            for col in extra_columns:
                expected_schema[col] = df[col].dtype

        # Ensure ALL files have the same columns by adding any missing ones
        all_columns = set(df.columns) | set(expected_schema.keys())
        for col in all_columns:
            if col not in df.columns:
                # Determine appropriate dtype
                if col in expected_schema:
                    dtype = expected_schema[col]
                else:
                    dtype = pl.Utf8  # Default to string
                df = df.with_columns([pl.lit(None).cast(dtype).alias(col)])
                expected_schema[col] = dtype

        # Ensure column order matches expected schema (surrogate key and composite key first, audit columns last)
        key_columns = ["Surrogate Key", "Composite Key"]
        audit_columns = AUDIT_COLUMNS

        # Get columns in the right order
        ordered_columns = []

        # Add key columns first
        for col in key_columns:
            if col in df.columns:
                ordered_columns.append(col)

        # Add middle columns (everything except keys and audit) in consistent order
        middle_columns = sorted([col for col in df.columns
                               if col not in key_columns and col not in audit_columns])
        ordered_columns.extend(middle_columns)

        # Add audit columns last
        for col in audit_columns:
            if col in df.columns:
                ordered_columns.append(col)

        # Reorder columns
        df = df.select(ordered_columns)

        # Type coercion for consistency
        for col in df.columns:
            if col in expected_schema:
                current_dtype = df[col].dtype
                expected_dtype = expected_schema[col]

                if current_dtype != expected_dtype:
                    logging.info(f"🔄 Converting {col} from {current_dtype} to {expected_dtype} in {file_name}")
                    try:
                        df = df.with_columns([pl.col(col).cast(expected_dtype)])
                    except Exception as cast_error:
                        logging.warning(f"⚠️ Could not cast {col} to {expected_dtype}: {cast_error}")

        logging.info(f"✓ Schema alignment completed for {file_name}: {df.shape}")
        return df, expected_schema

    except Exception as e:
        logging.error(f"❌ Schema alignment failed for {file_name}: {str(e)}")
        raise
