"""
File utilities for extracting information from filenames
"""
import re
import logging
from datetime import datetime


def get_file_info_from_name(filename):
    """
    Dynamically extract date/time and other info from various filename patterns
    
    Args:
        filename (str): The filename to parse
        
    Returns:
        dict: Dictionary containing extracted file information
    """
    try:
        logging.info(f"  Extracting file info from: {filename}")
        
        # Remove file extension
        base_name = filename.replace('.csv', '').replace('.CSV', '')
        
        # Initialize result
        result = {
            'original_filename': filename,
            'prefix': None,
            'date_part': None,
            'source_date': None
        }
        
        # Define multiple regex patterns for different filename formats
        patterns = [
            # Pattern 1: MM-DD-YY_HH.MM.SS (e.g., FIMASTSALES2TEST_08-18-25_11.40.30)
            {
                'regex': r'(.+?)_(\d{2})-(\d{2})-(\d{2})_(\d{2})\.(\d{2})\.(\d{2})',
                'groups': ['prefix', 'month', 'day', 'year', 'hour', 'minute', 'second'],
                'format_func': lambda g: f"{g[1]}/{g[2]}/20{g[3]} {g[4]}:{g[5]}"
            },
            
            # Pattern 2: YYYY-MM-DD (e.g., combined_sales_data_2025-08-29)
            {
                'regex': r'(.+?)_(\d{4})-(\d{2})-(\d{2})',
                'groups': ['prefix', 'year', 'month', 'day'],
                'format_func': lambda g: f"{g[2]}/{g[3]}/{g[1]}"
            },
            
            # Pattern 3: MM-DD-YYYY (e.g., sales_08-18-2025)
            {
                'regex': r'(.+?)_(\d{2})-(\d{2})-(\d{4})',
                'groups': ['prefix', 'month', 'day', 'year'],
                'format_func': lambda g: f"{g[1]}/{g[2]}/{g[3]}"
            },
            
            # Pattern 4: YYYYMMDD (e.g., sales_20250818)
            {
                'regex': r'(.+?)_(\d{8})',
                'groups': ['prefix', 'date_str'],
                'format_func': lambda g: f"{g[1][4:6]}/{g[1][6:8]}/{g[1][:4]}"
            },
            
            # Pattern 5: MM/DD/YY or MM/DD/YYYY (e.g., sales_08/18/25) - handle URL encoding
            {
                'regex': r'(.+?)_(\d{2})[\/](\d{2})[\/](\d{2,4})',
                'groups': ['prefix', 'month', 'day', 'year'],
                'format_func': lambda g: f"{g[1]}/{g[2]}/{('20' + g[3]) if len(g[3]) == 2 else g[3]}"
            },
            
            # Pattern 6: DD-MM-YY or DD-MM-YYYY (European format)
            {
                'regex': r'(.+?)_(\d{2})-(\d{2})-(\d{2,4})',
                'groups': ['prefix', 'day', 'month', 'year'],
                'format_func': lambda g: f"{g[2]}/{g[1]}/{('20' + g[3]) if len(g[3]) == 2 else g[3]}"
            },
            
            # Pattern 7: Any 6-8 digit sequence that could be a date
            {
                'regex': r'(.+?)_(\d{6,8})',
                'groups': ['prefix', 'date_str'],
                'format_func': lambda g: parse_digit_sequence(g[1])
            },
            
            # Pattern 8: Date anywhere in filename (not necessarily after underscore)
            {
                'regex': r'(\d{4})-(\d{2})-(\d{2})',
                'groups': ['year', 'month', 'day'],
                'format_func': lambda g: f"{g[1]}/{g[2]}/{g[0]}"
            },
            
            # Pattern 9: MM-DD-YY anywhere in filename
            {
                'regex': r'(\d{2})-(\d{2})-(\d{2})',
                'groups': ['month', 'day', 'year'],
                'format_func': lambda g: f"{g[0]}/{g[1]}/20{g[2]}"
            }
        ]
        
        for i, pattern_info in enumerate(patterns, 1):
            match = re.search(pattern_info['regex'], base_name)
            if match:
                groups = match.groups()
                logging.info(f"  Pattern {i} matched: {groups}")
                
                try:
                    # Extract prefix if available
                    if 'prefix' in pattern_info['groups']:
                        prefix_index = pattern_info['groups'].index('prefix')
                        result['prefix'] = groups[prefix_index]
                    
                    # Format the date
                    formatted_date = pattern_info['format_func'](groups)
                    result['source_date'] = formatted_date
                    result['date_part'] = '_'.join(groups[1:]) if len(groups) > 1 else groups[0]
                    
                    logging.info(f"  Successfully extracted datetime: {formatted_date}")
                    return result
                    
                except (ValueError, IndexError) as e:
                    logging.warning(f"  Pattern {i} matched but failed to parse: {e}")
                    continue
        
        # If no pattern matches, return filename with prefix
        logging.warning(f"  No datetime pattern found in filename: {filename}")
        result['source_date'] = f"From file: {filename}"
        return result
        
    except Exception as e:
        logging.error(f"  Error extracting file info from {filename}: {str(e)}")
        return {
            'original_filename': filename,
            'prefix': None,
            'date_part': None,
            'source_date': f"From file: {filename}"
        }


def parse_digit_sequence(date_str):
    """Parse a sequence of digits as a date"""
    try:
        if len(date_str) == 6:  # MMDDYY
            month = date_str[:2]
            day = date_str[2:4]
            year = f"20{date_str[4:6]}"
            return f"{month}/{day}/{year}"
        elif len(date_str) == 8:  # YYYYMMDD or MMDDYYYY
            # Try YYYYMMDD first
            if int(date_str[:4]) > 1900:  # Likely YYYYMMDD
                year = date_str[:4]
                month = date_str[4:6]
                day = date_str[6:8]
            else:  # Likely MMDDYYYY
                month = date_str[:2]
                day = date_str[2:4]
                year = date_str[4:8]
            return f"{month}/{day}/{year}"
        else:
            return f"From file: {date_str}"
    except (ValueError, IndexError):
        return f"From file: {date_str}"


def extract_datetime_from_blob_metadata(blob_client):
    """
    Extract datetime from blob metadata as fallback
    
    Args:
        blob_client: Azure blob client
        
    Returns:
        str: Formatted datetime string or None
    """
    try:
        properties = blob_client.get_blob_properties()
        
        # Try last_modified first
        if hasattr(properties, 'last_modified') and properties.last_modified:
            return properties.last_modified.strftime('%m/%d/%Y %H:%M')
        
        # Try creation_time as fallback
        if hasattr(properties, 'creation_time') and properties.creation_time:
            return properties.creation_time.strftime('%m/%d/%Y %H:%M')
            
        return None
        
    except Exception as e:
        logging.warning(f"Could not extract datetime from blob metadata: {str(e)}")
        return None
