import os
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

try:
    from azure.storage.blob import BlobServiceClient
    from azure.data.tables import TableServiceClient
    from azure.identity import DefaultAzureCredential
    AZURE_MODULES_AVAILABLE = True
except ImportError:
    # For local testing when Azure modules aren't available
    BlobServiceClient = None
    TableServiceClient = None
    DefaultAzureCredential = None
    AZURE_MODULES_AVAILABLE = False

# Quiet Azure SDK HTTP request/response logging to avoid noisy 200/201 lines
def setup_quiet_azure_logging():
    """Suppress verbose Azure SDK HTTP logging to reduce log noise"""
    try:
        logging.getLogger("azure").setLevel(logging.WARNING)
        logging.getLogger("azure.core.pipeline.policies.http_logging_policy").setLevel(logging.WARNING)
        logging.getLogger("azure.identity").setLevel(logging.WARNING)
        logging.getLogger("azure.storage").setLevel(logging.WARNING)
        logging.getLogger("azure.storage.blob").setLevel(logging.WARNING)
        logging.getLogger("azure.data.tables").setLevel(logging.WARNING)
    except Exception:
        pass

# Table-Driven Configuration System
# Configuration is now loaded dynamically from Azure Table Storage (sftpConfig table)

# Storage Account Configuration
STORAGE_ACCOUNT_NAME = "sagedw"  # This could also be moved to table if needed
STORAGE_ACCOUNT_URL = f"https://{STORAGE_ACCOUNT_NAME}.blob.core.windows.net"
TABLE_STORAGE_URL = f"https://{STORAGE_ACCOUNT_NAME}.table.core.windows.net"

# Initialize Azure clients
if AZURE_MODULES_AVAILABLE:
    credential = DefaultAzureCredential()
    blob_service_client = BlobServiceClient(account_url=STORAGE_ACCOUNT_URL, credential=credential)
    table_service_client = TableServiceClient(endpoint=TABLE_STORAGE_URL, credential=credential)
else:
    credential = None
    blob_service_client = None
    table_service_client = None

# Configuration cache to avoid repeated table queries
_config_cache = {}
_cache_expiry = None
CACHE_DURATION_MINUTES = 5  # Cache configuration for 5 minutes

# Table-driven configuration functions
def _is_cache_valid() -> bool:
    """Check if configuration cache is still valid"""
    global _cache_expiry
    if _cache_expiry is None:
        return False
    return datetime.now() < _cache_expiry

def _refresh_cache():
    """Refresh configuration cache from Azure Table Storage"""
    global _config_cache, _cache_expiry

    if not AZURE_MODULES_AVAILABLE or table_service_client is None:
        # Fallback to hardcoded values for local testing
        _config_cache = {
            'job_configs': {}
        }
        _cache_expiry = datetime.now() + timedelta(minutes=CACHE_DURATION_MINUTES)
        return

    try:
        table_client = table_service_client.get_table_client(table_name="sftpConfig")

        # Load job configurations (enabled jobs only)
        # Handle both boolean and string boolean values for isEnabled
        job_configs = {}

        # Query ONLY entities from Reynolds partition key and filter for enabled jobs
        # This ensures we only load Reynolds configuration entities
        reynolds_entities = table_client.query_entities(query_filter="PartitionKey eq 'Reynolds'")
        for entity in reynolds_entities:
            is_enabled = entity.get('isEnabled', False)
            # Handle various boolean formats
            if (is_enabled is True or
                (isinstance(is_enabled, str) and is_enabled.lower() in ('true', '1', 'yes')) or
                (isinstance(is_enabled, str) and is_enabled.startswith('true@'))):
                job_configs[entity['RowKey']] = entity

        _config_cache = {
            'job_configs': job_configs
        }
        _cache_expiry = datetime.now() + timedelta(minutes=CACHE_DURATION_MINUTES)

        logging.info(f"Configuration cache refreshed. Found {len(job_configs)} enabled jobs.")

        # Debug: Log FilePrefix values for each job
        for job_key, job_config in job_configs.items():
            file_prefix = job_config.get('FilePrefix', 'NOT_SET')
            logging.info(f"🔍 Job {job_key}: FilePrefix = '{file_prefix}'")

    except Exception as e:
        logging.error(f"Failed to refresh configuration cache: {e}")
        # Keep existing cache if refresh fails
        if not _config_cache:
            # Initialize with empty cache if no cache exists and refresh fails
            _config_cache = {
                'job_configs': {},
                'last_refresh': datetime.now()
            }



def get_job_configs() -> Dict[str, Dict]:
    """Get all enabled job configurations"""
    if not _is_cache_valid():
        _refresh_cache()

    return _config_cache.get('job_configs', {})

def get_job_config(job_key: str) -> Optional[Dict]:
    """Get specific job configuration - handles both with and without underscores"""
    if not job_key:
        return None
        
    job_configs = get_job_configs()
    
    # First, try the exact job_key
    config = job_configs.get(job_key)
    if config:
        return config
    
    # If not found and job_key ends with underscore, try without underscore
    if job_key.endswith('_'):
        job_key_without_underscore = job_key[:-1]
        config = job_configs.get(job_key_without_underscore)
        if config:
            return config
    
    # If not found and job_key doesn't end with underscore, try with underscore
    else:
        job_key_with_underscore = job_key + '_'
        config = job_configs.get(job_key_with_underscore)
        if config:
            return config
    
    # Not found with either variation
    return None

class Config:
    """Table-driven Configuration class for CSV Processing Pipeline"""

    def __init__(self, job_key: str = None):
        """Initialize configuration for a specific job or system-wide settings"""
        self.job_key = job_key
        self.job_config = get_job_config(job_key) if job_key else None

        # Load configuration from job entity properties (with sensible defaults)
        self.storage_account_url = STORAGE_ACCOUNT_URL
        if self.job_config:
            self.storage_account_name = self.job_config.get('StorageAccountName', STORAGE_ACCOUNT_NAME)
            self.source_container = self.job_config.get('SourceContainer', 'sftp-landing')
            self.output_container = self.job_config.get('OutputContainer', 'sftp-landing')
            self.surrogate_key_column = self.job_config.get('SurrogateKeyColumn', 'Composite Key')
            self.audit_columns = self.job_config.get('AuditColumns', 'Source Date/Time,Created Date/Time,Modified Date/Time').split(',')
            self.default_consolidation_method = self.job_config.get('ConsolidationMethod', 'concat')
            self.default_timeout = int(self.job_config.get('TimeoutMinutes', 10))
            self.default_retries = int(self.job_config.get('MaxRetries', 3))
        else:
            # Fallback defaults when no job config is provided
            self.storage_account_name = STORAGE_ACCOUNT_NAME
            self.source_container = 'sftp-landing'
            self.output_container = 'sftp-landing'
            self.surrogate_key_column = 'Composite Key'
            self.audit_columns = ['Source Date/Time', 'Created Date/Time', 'Modified Date/Time']
            self.default_consolidation_method = 'concat'
            self.default_timeout = 10
            self.default_retries = 3

        # Job-specific configuration (if job_key provided)
        if self.job_config:
            # Auto-generate subfolder names if not provided
            folder = self.job_config.get('Folder', '')
            source_subfolder = self.job_config.get('SourceSubfolder', '')
            output_subfolder = self.job_config.get('OutputSubfolder', '')
            
            # If SourceSubfolder is missing, auto-generate it
            if not source_subfolder:
                # For dealer jobs, use generic 'decompressed', for regular jobs use specific pattern
                if self.job_config.get('DealerPrefix', False):
                    source_subfolder = 'decompressed'  # Generic for dealer structure
                else:
                    # Generate from folder name (e.g., 'FIMAST-SALES' -> 'fimastsales-decompressed')
                    base_name = folder.lower().replace('-', '')
                    source_subfolder = f"{base_name}-decompressed"
            
            # If OutputSubfolder is missing, auto-generate it  
            if not output_subfolder:
                # For dealer jobs, use generic 'enhanced', for regular jobs use specific pattern
                if self.job_config.get('DealerPrefix', False):
                    output_subfolder = 'enhanced'  # Generic for dealer structure
                else:
                    # Generate from folder name (e.g., 'FIMAST-SALES' -> 'fimastsales-enhanced')
                    base_name = folder.lower().replace('-', '')
                    output_subfolder = f"{base_name}-enhanced"
            
            self.source_folder_path = f"{folder}/{source_subfolder}".strip('/')
            self.output_folder_path = f"{folder}/{output_subfolder}".strip('/')
            self.file_patterns = [self.job_config.get('FilePrefix', '').rstrip('_')]
            self.consolidation_method = self.job_config.get('ConsolidationMethod', self.default_consolidation_method)
            self.surrogate_key_logic = self.job_config.get('SurrogateKeyLogic', 'placeholder')
            self.max_retries = int(self.job_config.get('MaxRetries', self.default_retries))
            self.timeout_minutes = int(self.job_config.get('TimeoutMinutes', self.default_timeout))
            self.processing_order = int(self.job_config.get('ProcessingOrder', 1))
            self.validation_rules = self._parse_json_field('ValidationRules')
            self.transformation_rules = self._parse_json_field('TransformationRules')

            # Zip file movement configuration - read directly from job config properties
            # Handle boolean conversion for ZipMovementEnabled (Azure Table Storage may return string with type annotation)
            zip_enabled = self.job_config.get('ZipMovementEnabled', False)
            if isinstance(zip_enabled, str):
                # Handle Azure Table Storage boolean format like "<EMAIL>"
                if '@Edm.Boolean' in zip_enabled:
                    self.zip_movement_enabled = zip_enabled.split('@')[0].lower() == 'true'
                else:
                    self.zip_movement_enabled = zip_enabled.lower() in ('true', '1', 'yes')
            else:
                self.zip_movement_enabled = bool(zip_enabled)

            # Use job config properties directly, with fallback to constructed paths
            self.zip_source_folder = self.job_config.get('ZipSourceFolder')
            if not self.zip_source_folder:
                folder = self.job_config.get('Folder', 'FIMAST-SALES')
                self.zip_source_folder = f"{folder}/fimastsales-compressed"

            self.zip_archive_folder = self.job_config.get('ZipArchiveFolder')
            if not self.zip_archive_folder:
                folder = self.job_config.get('Folder', 'FIMAST-SALES')
                self.zip_archive_folder = f"{folder}/archive"

            self.zip_file_pattern = self.job_config.get('ZipFilePattern')
            if not self.zip_file_pattern:
                prefix = self.job_config.get('FilePrefix', 'FIMASTSALES')
                self.zip_file_pattern = f"{prefix.rstrip('_')}*.zip"

            # Surrogate key configuration - read directly from job config properties
            self.surrogate_key_columns = self.job_config.get('SurrogateKeyColumns', 'ST/BR,DEAL NO,,')
            self.surrogate_key_separator = self.job_config.get('SurrogateKeySeparator', 'PIPE')
            # Convert "PIPE" to actual pipe character for backward compatibility
            if self.surrogate_key_separator == 'PIPE':
                self.surrogate_key_separator = '|'
            self.surrogate_key_method = self.job_config.get('SurrogateKeyMethod', 'MD5')
        else:
            # Fallback values for backward compatibility
            self.source_folder_path = "FIMAST-SALES/fimastsales-decompressed"
            self.output_folder_path = "FIMAST-SALES/fimastsales-enhanced"
            self.file_patterns = ['FIMASTSALES']
            self.consolidation_method = self.default_consolidation_method
            self.surrogate_key_logic = 'placeholder'
            self.max_retries = self.default_retries
            self.timeout_minutes = self.default_timeout
            self.processing_order = 1
            self.validation_rules = {}
            self.transformation_rules = {}

            # Zip file movement fallback configuration
            self.zip_movement_enabled = False
            self.zip_source_folder = 'FIMAST-SALES/fimastsales-compressed'
            self.zip_archive_folder = 'FIMAST-SALES/archive'
            self.zip_file_pattern = 'FIMASTSALES*.zip'

            # Surrogate key fallback configuration
            self.surrogate_key_columns = 'ST/BR,DEAL NO,,'
            self.surrogate_key_separator = '|'
            self.surrogate_key_method = 'MD5'

    def _parse_json_field(self, field_name: str) -> Dict:
        """Parse JSON field from job configuration"""
        if not self.job_config:
            return {}

        json_str = self.job_config.get(field_name, '{}')
        if not json_str or json_str == '{}':
            return {}

        try:
            # First try to parse as-is
            return json.loads(json_str)
        except json.JSONDecodeError:
            # If that fails, it might be escaped JSON from Azure Table Storage
            try:
                # Simple unescape for common patterns
                if '\\' in json_str:
                    unescaped = json_str.replace('\\', '')
                    return json.loads(unescaped)
            except json.JSONDecodeError:
                pass

            # If all parsing fails, log warning and return empty dict
            logging.info(f"Could not parse JSON in {field_name} for job {self.job_key}. Using empty configuration. Raw value: {json_str}")
            return {}

    def validate(self):
        """Validate configuration settings"""
        try:
            # Check if Azure modules are available and we can create blob service client
            if not AZURE_MODULES_AVAILABLE:
                # In local testing mode, just validate basic config
                return self.storage_account_url is not None

            test_client = BlobServiceClient(account_url=self.storage_account_url, credential=credential)
            return True
        except Exception as e:
            logging.error(f"Configuration validation failed: {e}")
            return False

    def get_processing_config(self) -> Dict:
        """Get processing configuration dictionary for backward compatibility"""
        return {
            'source_container': self.source_container,
            'output_container': self.output_container,
            'source_folder_path': self.source_folder_path,
            'output_folder_path': self.output_folder_path,
            'file_patterns': self.file_patterns,
            'surrogate_key_logic': self.surrogate_key_logic,
            'consolidation_method': self.consolidation_method,
            'max_retries': self.max_retries,
            'timeout_minutes': self.timeout_minutes,
            'validation_rules': self.validation_rules,
            'transformation_rules': self.transformation_rules,
            'zip_movement_enabled': self.zip_movement_enabled,
            'zip_source_folder': self.zip_source_folder,
            'zip_archive_folder': self.zip_archive_folder,
            'zip_file_pattern': self.zip_file_pattern
        }

# Table-driven configuration functions (backward compatibility)
def get_output_container(job_key: str = None):
    """Returns output container from table configuration"""
    if job_key:
        config = Config(job_key)
        return config.output_container
    return 'sftp-landing'  # Default fallback

def get_source_container(job_key: str = None):
    """Returns source container from table configuration"""
    if job_key:
        config = Config(job_key)
        return config.source_container
    return 'sftp-landing'  # Default fallback

# Removed duplicate function - kept the one below with proper return type

def get_source_blob_path(filename: str, job_key: str = None):
    """Returns full blob path for source files"""
    if job_key:
        config = Config(job_key)
        return f"{config.source_folder_path}/{filename}"
    # Fallback for backward compatibility
    return f"FIMAST-SALES/fimastsales-decompressed/{filename}"

def get_output_blob_path(filename: str, job_key: str = None):
    """Returns full blob path for output files - enhanced for dealer support"""
    if job_key:
        config = Config(job_key)
        
        # Check if dealer prefix enabled and filename contains dealer info
        if config.job_config and config.job_config.get('DealerPrefix', False):
            # Extract dealer prefix from filename (e.g., AO_RO_TIME_08-20-25.csv -> AO)
            if '_' in filename:
                # For dealer files, construct the dealer-specific enhanced path
                filename_parts = filename.split('_')
                if len(filename_parts) >= 2:
                    potential_dealer = filename_parts[0].upper()
                    # Construct dealer folder name (e.g., AO -> AO_RO_TIME)
                    dealer_folder = f"{potential_dealer}_RO_TIME"
                    # Get base folder from config (e.g., "RO_Time")
                    base_folder = config.source_folder_path.split('/')[0] if '/' in config.source_folder_path else config.source_folder_path
                    # Construct dealer enhanced path
                    dealer_enhanced_path = f"{base_folder}/{dealer_folder}/{dealer_folder.lower()}-enhanced"
                    return f"{dealer_enhanced_path}/{filename}"
        
        # Default behavior for non-dealer files
        return f"{config.output_folder_path}/{filename}"
    
    # Fallback for backward compatibility
    return f"FIMAST-SALES/fimastsales-enhanced/{filename}"

def get_source_folder_path(job_key: str = None):
    """Returns source folder path"""
    if job_key:
        config = Config(job_key)
        return config.source_folder_path
    # Fallback for backward compatibility
    return "FIMAST-SALES/fimastsales-decompressed"

def get_output_folder_path(job_key: str = None):
    """Returns output folder path"""
    if job_key:
        config = Config(job_key)
        return config.output_folder_path
    # Fallback for backward compatibility
    return "FIMAST-SALES/fimastsales-enhanced"

def get_file_patterns(job_key: str = None):
    """Returns file patterns for matching"""
    if job_key:
        config = Config(job_key)
        return config.file_patterns
    # Fallback for backward compatibility
    return ['FIMASTSALES']

def get_enabled_jobs() -> List[str]:
    """Returns list of enabled job keys"""
    job_configs = get_job_configs()
    return list(job_configs.keys())

def refresh_configuration():
    """Force refresh of configuration cache"""
    global _cache_expiry
    _cache_expiry = None  # Invalidate cache
    _refresh_cache()

# Zip file movement configuration functions
def get_zip_movement_enabled(job_key: str = None) -> bool:
    """Returns whether zip file movement to archive is enabled"""
    if job_key:
        config = Config(job_key)
        return config.zip_movement_enabled
    return False  # Default fallback

def get_zip_source_folder(job_key: str = None) -> str:
    """Returns source folder path for zip files"""
    if job_key:
        config = Config(job_key)
        return config.zip_source_folder
    return 'FIMAST-SALES/fimastsales-compressed'  # Default fallback

def get_zip_archive_folder(job_key: str = None) -> str:
    """Returns archive folder path for zip files"""
    if job_key:
        config = Config(job_key)
        return config.zip_archive_folder
    return 'FIMAST-SALES/archive'  # Default fallback

def get_zip_file_pattern(job_key: str = None) -> str:
    """Returns file pattern for zip files to move"""
    if job_key:
        config = Config(job_key)
        return config.zip_file_pattern
    return 'FIMASTSALES*.zip'  # Default fallback

# Enhanced dealer-aware zip archiving functions
def get_dealer_zip_archiving_enabled(job_key: str = None) -> bool:
    """Returns whether dealer-aware zip archiving is enabled for this job"""
    if job_key:
        job_config = get_job_config(job_key)
        if job_config:
            # If DealerPrefix is enabled and ZipMovementEnabled is true, use dealer-aware archiving
            dealer_prefix_enabled = job_config.get('DealerPrefix', False)
            zip_movement_enabled = get_zip_movement_enabled(job_key)

            return dealer_prefix_enabled and zip_movement_enabled
    return False

def get_dealer_zip_source_folders(job_key: str = None) -> List[str]:
    """Returns list of dealer-specific zip source folders for dynamic archiving"""
    if not job_key or not get_dealer_zip_archiving_enabled(job_key):
        return []

    try:
        config = Config(job_key)
        container_name = get_source_container(job_key)
        container_client = blob_service_client.get_container_client(container_name)

        # Get base folder from source folder path (e.g., "RO_Time" from "RO_Time/decompressed")
        base_folder = config.source_folder_path.split('/')[0] if '/' in config.source_folder_path else config.source_folder_path

        # Discover all dealer folders dynamically
        dealer_folders = set()
        for blob in container_client.list_blobs(name_starts_with=base_folder):
            path_parts = blob.name.split('/')
            # Look for dealer folders (e.g., AO_RO_TIME, BMWL_RO_TIME)
            if len(path_parts) >= 2 and '_RO_TIME' in path_parts[1]:
                dealer_folders.add(path_parts[1])

        # Construct zip source folders for each dealer using the same pattern as CSV files
        zip_source_folders = []
        for dealer_folder in dealer_folders:
            # Pattern: RO_Time/AO_RO_TIME/ao_ro_time-compressed (same as CSV source structure)
            dealer_zip_source = f"{base_folder}/{dealer_folder}/{dealer_folder.lower()}-compressed"
            zip_source_folders.append(dealer_zip_source)

        return zip_source_folders

    except Exception as e:
        logging.error(f"Error discovering dealer zip source folders for job {job_key}: {e}")
        return []

def get_dealer_zip_archive_folder(dealer_folder: str, job_key: str = None) -> str:
    """Returns archive folder path for a specific dealer using existing ZipArchiveFolder pattern"""
    if not job_key:
        return f"archive/{dealer_folder}"

    config = Config(job_key)
    job_config = get_job_config(job_key)

    if job_config:
        # Get the configured ZipArchiveFolder (e.g., "RO_Time/archive")
        zip_archive_folder = config.zip_archive_folder

        # Parse the pattern to understand the structure
        if '/' in zip_archive_folder:
            base_folder, archive_subfolder = zip_archive_folder.rsplit('/', 1)
            # Create dealer-specific archive: RO_Time/AO_RO_TIME/archive
            return f"{base_folder}/{dealer_folder}/{archive_subfolder}"
        else:
            # If no slash, treat as simple archive folder
            return f"{zip_archive_folder}/{dealer_folder}"

    # Fallback
    return f"archive/{dealer_folder}"

def get_dealer_zip_file_pattern_for_dealer(dealer_folder: str, job_key: str = None) -> str:
    """Returns file pattern for zip files for a specific dealer using existing ZipFilePattern"""
    if not job_key:
        return f"{dealer_folder}*.zip"

    config = Config(job_key)
    job_config = get_job_config(job_key)

    if job_config:
        # Get the configured ZipFilePattern (e.g., "*RO_TIME*.zip")
        zip_file_pattern = config.zip_file_pattern

        # Extract dealer prefix from dealer folder (e.g., AO_RO_TIME -> AO)
        dealer_prefix = dealer_folder.split('_')[0] if '_' in dealer_folder else dealer_folder

        # If pattern contains wildcards, adapt it for the specific dealer
        if '*' in zip_file_pattern:
            # Replace generic pattern with dealer-specific pattern
            # "*RO_TIME*.zip" becomes "AO_RO_TIME*.zip" for dealer AO
            if 'RO_TIME' in zip_file_pattern:
                return zip_file_pattern.replace('*RO_TIME*', f"{dealer_folder}*")
            else:
                # Generic fallback
                return f"{dealer_prefix}*.zip"
        else:
            # If no wildcards, use dealer prefix
            return f"{dealer_prefix}*.zip"

    # Fallback: extract dealer prefix
    dealer_prefix = dealer_folder.split('_')[0] if '_' in dealer_folder else dealer_folder
    return f"{dealer_prefix}*.zip"

# Surrogate key configuration functions
def get_surrogate_key_columns(job_key: str = None) -> str:
    """Returns comma-separated list of columns for surrogate key generation"""
    if job_key:
        config = Config(job_key)
        return config.surrogate_key_columns
    return 'ST/BR,DEAL NO,,'  # Default fallback

def get_surrogate_key_separator(job_key: str = None) -> str:
    """Returns separator character for surrogate key concatenation"""
    if job_key:
        config = Config(job_key)
        return config.surrogate_key_separator
    return '|'  # Default fallback

def get_surrogate_key_method(job_key: str = None) -> str:
    """Returns hash method for surrogate key generation"""
    if job_key:
        config = Config(job_key)
        return config.surrogate_key_method
    return 'MD5'  # Default fallback

# Dealer prefix configuration functions
def get_dealer_prefix_enabled(job_key: str = None) -> bool:
    """Returns whether dealer prefix processing is enabled for this job"""
    if job_key:
        job_config = get_job_config(job_key)
        return job_config.get('DealerPrefix', False) if job_config else False
    return False

# Backward compatibility - use fallback values during import to avoid Azure connection issues
try:
    # Try to load from table, but don't fail if it's not available
    SOURCE_CONTAINER = 'sftp-landing'
    OUTPUT_CONTAINER = 'sftp-landing'
    SOURCE_FOLDER_PATH = get_source_folder_path()
    OUTPUT_FOLDER_PATH = get_output_folder_path()
    FILE_PATTERNS = get_file_patterns()
    SURROGATE_KEY_COLUMN = 'Composite Key'
    AUDIT_COLUMNS = ['Source Date/Time', 'Created Date/Time', 'Modified Date/Time']
except Exception as e:
    # Fallback values for Azure Functions startup or when table is not available
    logging.warning(f"Failed to load table configuration during import, using fallback values: {e}")
    SOURCE_CONTAINER = 'sftp-landing'
    OUTPUT_CONTAINER = 'sftp-landing'
    SOURCE_FOLDER_PATH = 'FIMAST-SALES/fimastsales-decompressed'
    OUTPUT_FOLDER_PATH = 'FIMAST-SALES/fimastsales-enhanced'
    FILE_PATTERNS = ['FIMASTSALES']
    SURROGATE_KEY_COLUMN = 'Composite Key'
    AUDIT_COLUMNS = ['Source Date/Time', 'Created Date/Time', 'Modified Date/Time']

# Legacy processing config for backward compatibility
try:
    PROCESSING_CONFIG = {
        'source_container': SOURCE_CONTAINER,
        'output_container': OUTPUT_CONTAINER,
        'source_folder_path': SOURCE_FOLDER_PATH,
        'output_folder_path': OUTPUT_FOLDER_PATH,
        'file_patterns': FILE_PATTERNS,
        'surrogate_key_logic': 'placeholder',
        'consolidation_method': 'concat'  # Default fallback
    }
except Exception:
    PROCESSING_CONFIG = {
        'source_container': 'sftp-landing',
        'output_container': 'sftp-landing',
        'source_folder_path': 'FIMAST-SALES/fimastsales-decompressed',
        'output_folder_path': 'FIMAST-SALES/fimastsales-enhanced',
        'file_patterns': ['FIMASTSALES'],
        'surrogate_key_logic': 'placeholder',
        'consolidation_method': 'concat'
    }
