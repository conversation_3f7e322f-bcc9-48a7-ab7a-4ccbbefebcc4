import os
import sys
import polars as pl
import logging

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from azure_config import FILE_PATTERNS

def validate_csv_structure(df, filename=""):
    """Validate CSV structure and data integrity - Updated for Polars"""
    try:
        logging.info(f"=== CSV VALIDATION START ===")
        logging.info(f"Validating CSV structure for: {filename}")
        logging.info(f"DataFrame shape: {df.shape}")
        logging.info(f"Columns: {list(df.columns)}")

        # Basic validation checks
        if df.height == 0:
            raise ValueError(f"CSV file {filename} is empty")

        if df.width == 0:
            raise ValueError(f"CSV file {filename} has no columns")

        # Check for completely empty columns using Polars
        empty_columns = []
        for col in df.columns:
            if df.select(pl.col(col).is_null().all()).item():
                empty_columns.append(col)

        if empty_columns:
            print(f"Warning: Found completely empty columns: {empty_columns}")
        
        # Check data types
        print(f"Data types:")
        for col in df.columns:
            print(f"  {col}: {df[col].dtype}")
        
        print(f"✓ CSV validation passed for: {filename}")
        print(f"=== CSV VALIDATION END ===")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV validation failed for {filename}: {e}")
        raise

def detect_numeric_columns(df):
    """Detect numeric columns in the DataFrame"""
    try:
        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
        print(f"Detected numeric columns: {numeric_columns}")
        return numeric_columns
        
    except Exception as e:
        print(f"Error detecting numeric columns: {e}")
        return []

def get_file_info_from_name(filename):
    """Extract information from filename for audit purposes"""
    try:
        import re
        from datetime import datetime

        # Extract date and prefix information from filename
        # Expected format: PREFIX_DATE_*.csv

        file_info = {
            'original_filename': filename,
            'prefix': None,
            'date_part': None,
            'source_date': None
        }

        # Remove .csv extension
        name_without_ext = filename.replace('.csv', '')

        # Try to extract prefix and date
        for prefix in FILE_PATTERNS:
            if name_without_ext.startswith(prefix):
                file_info['prefix'] = prefix
                # Try to extract date part (assuming format: PREFIX_MM-DD-YY_HH-MM)
                remaining = name_without_ext[len(prefix):].lstrip('_')

                # Look for date pattern MM-DD-YY (with optional time HH-MM)
                date_pattern = r'(\d{2}-\d{2}-\d{2})(?:_(\d{2}-\d{2}))?'
                match = re.search(date_pattern, remaining)

                if match:
                    date_part = match.group(1)  # MM-DD-YY
                    time_part = match.group(2)  # HH-MM (optional)

                    file_info['date_part'] = date_part

                    # Parse the date and convert to proper datetime format
                    try:
                        # Parse MM-DD-YY format
                        parsed_date = datetime.strptime(date_part, '%m-%d-%y')

                        # If time part exists, add it
                        if time_part:
                            time_str = time_part.replace('-', ':')
                            time_obj = datetime.strptime(time_str, '%H:%M').time()
                            parsed_datetime = datetime.combine(parsed_date.date(), time_obj)
                        else:
                            parsed_datetime = parsed_date

                        # Format as readable datetime string
                        file_info['source_date'] = parsed_datetime.strftime('%Y-%m-%d %H:%M:%S')

                    except ValueError as ve:
                        print(f"Could not parse date '{date_part}': {ve}")
                        file_info['source_date'] = f"Unparsed: {date_part}"

                break

        print(f"Extracted file info: {file_info}")
        return file_info

    except Exception as e:
        print(f"Error extracting file info from {filename}: {e}")
        return {'original_filename': filename, 'prefix': None, 'date_part': None, 'source_date': None}

def clean_column_names(df):
    """Clean and standardize column names"""
    try:
        original_columns = df.columns.tolist()
        
        # Remove leading/trailing whitespace
        df.columns = df.columns.str.strip()
        
        # Log any changes
        new_columns = df.columns.tolist()
        if original_columns != new_columns:
            print(f"Column names cleaned:")
            for old, new in zip(original_columns, new_columns):
                if old != new:
                    print(f"  '{old}' -> '{new}'")
        
        return df
        
    except Exception as e:
        print(f"Error cleaning column names: {e}")
        return df

def preview_dataframe(df, filename="", max_rows=5):
    """Print a preview of the DataFrame for debugging"""
    try:
        print(f"=== DATAFRAME PREVIEW: {filename} ===")
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        print("\nFirst {max_rows} rows:")
        print(df.head(max_rows).to_string())
        print(f"=== END PREVIEW ===")
        
    except Exception as e:
        print(f"Error previewing DataFrame: {e}")
