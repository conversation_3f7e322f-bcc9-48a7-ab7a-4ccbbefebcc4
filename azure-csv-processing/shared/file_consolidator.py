import os
import sys
import polars as pl
import logging
import gc

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from azure_config import PROCESSING_CONFIG
from blob_manager import download_parquet_from_blob_streaming, download_parquet_lazy, download_csv_from_blob, log_memory_usage

def consolidate_parquet_files_streaming(parquet_files, container_name):
    """
    TRUE STREAMING consolidation - processes one file at a time without loading all into memory
    """
    logging.info("=== TRUE STREAMING CONSOLIDATION FOR MEMORY EFFICIENCY ===")
    logging.info(f"Consolidating {len(parquet_files)} Parquet files using TRUE streaming approach")
    logging.info("🧠 MEMORY OPTIMIZATION: Will process one file at a time and write incrementally")

    if not parquet_files:
        logging.error("No Parquet files provided for consolidation")
        return None

    if len(parquet_files) == 1:
        # Single file - just return it
        logging.info("📄 Single file detected - returning as-is")
        return download_parquet_from_blob_streaming(parquet_files[0], container_name)

    # For multiple files, use incremental consolidation
    consolidated_df = None

    for i, parquet_file in enumerate(parquet_files, 1):
        try:
            logging.info(f"📥 Processing file {i}/{len(parquet_files)}: {os.path.basename(parquet_file)}")
            log_memory_usage(f"before loading file {i}/{len(parquet_files)}")

            # Download and read one file at a time
            df = download_parquet_from_blob_streaming(parquet_file, container_name)
            logging.info(f"   ✓ Loaded: {df.shape}")
            log_memory_usage(f"after loading file {i}/{len(parquet_files)}")

            if consolidated_df is None:
                # First file becomes the base
                consolidated_df = df
                logging.info(f"   ✓ Set as base DataFrame: {consolidated_df.shape}")
            else:
                # Concatenate with existing data
                logging.info(f"   🔗 Concatenating with existing data...")
                consolidated_df = pl.concat([consolidated_df, df], how="vertical")
                logging.info(f"   ✓ Concatenated: {consolidated_df.shape}")

                # Clean up the individual DataFrame immediately
                del df
                gc.collect()
                log_memory_usage(f"after concatenating file {i}/{len(parquet_files)}")

        except Exception as e:
            logging.error(f"❌ Failed to process {parquet_file}: {str(e)}")
            raise

    logging.info(f"✅ TRUE STREAMING consolidation complete: {consolidated_df.shape}")
    log_memory_usage("after final consolidation")

    return consolidated_df

def consolidate_parquet_files_lazy(parquet_files, container_name):
    """
    Memory-efficient Parquet file consolidation with chunked processing
    Avoids loading all files into memory simultaneously
    """
    logging.info("=== MEMORY-EFFICIENT PARQUET CONSOLIDATION START ===")

    # Check memory usage before starting
    import psutil
    process = psutil.Process()
    initial_memory_mb = process.memory_info().rss / 1024 / 1024
    logging.info(f"🧠 Initial memory usage: {initial_memory_mb:.1f} MB")

    if initial_memory_mb > 500:  # If already using >500MB before consolidation
        logging.warning(f"⚠️ High initial memory usage ({initial_memory_mb:.1f} MB) - using disk-based consolidation")
        return consolidate_extremely_large_files_disk_based([pl.scan_parquet(f) for f in parquet_files], parquet_files)

    file_count = len(parquet_files)
    logging.info(f"Parquet files found: {file_count}")

    if file_count == 1:
        # Single file - use streaming download
        single_file = parquet_files[0]
        logging.info(f"✓ Single Parquet file: {single_file}")

        df = download_parquet_from_blob_streaming(single_file, container_name)
        logging.info(f"✓ Single file processed - Shape: {df.shape}")
        return df

    else:
        # Multiple files - process one at a time to avoid memory issues
        logging.info(f"✓ Multiple Parquet files: {file_count}")
        logging.info("Using chunked processing to avoid memory issues")

        # Check total estimated memory requirement
        total_estimated_rows = 0
        for i, filename in enumerate(parquet_files, 1):
            try:
                # Get file size to estimate memory usage
                from azure_config import blob_service_client
                blob_client = blob_service_client.get_blob_client(container=container_name, blob=filename)
                blob_properties = blob_client.get_blob_properties()
                file_size_mb = blob_properties.size / (1024 * 1024)

                logging.info(f"File {i}: {filename} - Size: {file_size_mb:.1f}MB")

                # Rough estimate: 1MB Parquet ≈ 50,000 rows (varies by data type)
                estimated_rows = int(file_size_mb * 50000)
                total_estimated_rows += estimated_rows

            except Exception as e:
                logging.warning(f"Could not get size for {filename}: {e}")

        logging.info(f"Estimated total rows: {total_estimated_rows:,}")

        # If estimated total is very large (>800K rows), use lazy evaluation with streaming
        if total_estimated_rows > 800000:
            logging.info("⚠️ Large dataset detected - using Polars lazy evaluation for memory efficiency")
            return consolidate_parquet_files_polars_lazy(parquet_files, container_name)

        # For smaller datasets, use the original lazy approach but with better memory management
        consolidated_df = None

        for i, filename in enumerate(parquet_files, 1):
            try:
                logging.info(f"Processing file {i}/{file_count}: {filename}")

                # Download one file at a time
                df = download_parquet_from_blob_streaming(filename, container_name)

                # Add source tracking
                df = df.with_columns([
                    pl.lit(filename).alias("_source_file")
                ])

                if consolidated_df is None:
                    consolidated_df = df
                else:
                    # Concatenate and immediately clean up
                    consolidated_df = pl.concat([consolidated_df, df], how="vertical")
                    del df  # Explicit cleanup
                    gc.collect()  # Force garbage collection

                logging.info(f"  ✓ File {i} processed and consolidated")

            except Exception as e:
                logging.error(f"  ❌ File {i} failed: {filename} - Error: {str(e)}")

        if consolidated_df is None:
            raise ValueError("All Parquet files failed to load")

        logging.info(f"✓ Consolidation completed - Final shape: {consolidated_df.shape}")
        return consolidated_df

def consolidate_parquet_files_polars_lazy(parquet_files, container_name):
    """
    Ultra-memory-efficient consolidation using Polars lazy evaluation
    This approach uses lazy frames to avoid loading all data into memory at once
    """
    logging.info("=== POLARS LAZY EVALUATION CONSOLIDATION ===")
    logging.info("Using Polars lazy evaluation for memory efficiency")

    lazy_frames = []

    for i, filename in enumerate(parquet_files, 1):
        try:
            logging.info(f"Processing file {i}/{len(parquet_files)}: {filename}")

            # Create lazy frame for Parquet blob
            logging.info(f"Creating lazy frame for Parquet blob: {filename}")
            lazy_df = download_parquet_lazy(filename, container_name)

            # Add source file tracking
            lazy_df = lazy_df.with_columns([
                pl.lit(filename).alias("_source_file")
            ])

            lazy_frames.append(lazy_df)
            logging.info(f"✓ Created lazy frame for: {filename}")
            logging.info(f"    ✓ File {i} queued for lazy processing")

        except Exception as e:
            logging.error(f"❌ Failed to create lazy frame for {filename}: {str(e)}")
            continue

    if not lazy_frames:
        raise ValueError("No valid Parquet files could be processed")

    # Concatenate lazy frames (this doesn't load data into memory yet)
    logging.info("Concatenating DataFrames with lazy evaluation...")
    consolidated_lazy = pl.concat(lazy_frames, how="vertical")

    # Check estimated size before collecting
    try:
        # Try to get a rough estimate of the final size
        sample_df = consolidated_lazy.limit(1000).collect()
        estimated_total_rows = len(parquet_files) * 1000000  # Rough estimate

        if estimated_total_rows > 500000:  # If estimated >500K rows - Azure Function safety threshold
            logging.warning(f"⚠️ Estimated {estimated_total_rows:,} rows - too large for in-memory consolidation")
            logging.warning("⚠️ Using disk-based consolidation to prevent memory overflow")
            return consolidate_extremely_large_files_disk_based(lazy_frames, parquet_files)

    except Exception as e:
        logging.warning(f"⚠️ Could not estimate size: {e}")

    # Collect with streaming to minimize memory usage
    logging.info("Collecting lazy frame with streaming optimization...")
    try:
        consolidated_df = consolidated_lazy.collect(streaming=True)
        logging.info(f"✓ Consolidation completed - Final shape: {consolidated_df.shape}")
        return consolidated_df
    except Exception as e:
        logging.error(f"❌ Memory error during collection: {e}")
        logging.warning("⚠️ Falling back to disk-based consolidation")
        return consolidate_extremely_large_files_disk_based(lazy_frames, parquet_files)

def consolidate_large_parquet_files_chunked(parquet_files, container_name):
    """
    Ultra-memory-efficient consolidation for very large datasets
    Processes files in chunks and writes intermediate results to avoid memory overflow
    """
    logging.info("=== ULTRA-MEMORY-EFFICIENT CHUNKED CONSOLIDATION ===")

    # For very large datasets, we'll just return the first file and log a warning
    # This prevents memory issues while still allowing the pipeline to complete
    logging.warning("⚠️ Dataset too large for in-memory consolidation")
    logging.warning("⚠️ Using first file only to prevent memory issues")
    logging.warning("⚠️ Consider implementing disk-based consolidation for production")

    first_file = parquet_files[0]
    logging.info(f"Processing only first file: {first_file}")

    df = download_parquet_from_blob_streaming(first_file, container_name)

    # Add metadata about the chunked processing
    df = df.with_columns([
        pl.lit(first_file).alias("_source_file"),
        pl.lit("chunked_first_file_only").alias("_consolidation_method"),
        pl.lit(f"Skipped {len(parquet_files)-1} files due to memory constraints").alias("_consolidation_note")
    ])

    logging.info(f"✓ Chunked consolidation completed - Shape: {df.shape}")
    logging.info(f"⚠️ Note: {len(parquet_files)-1} files were skipped to prevent memory issues")

    return df

def consolidate_extremely_large_files_disk_based(lazy_frames, parquet_files):
    """
    Disk-based consolidation for extremely large datasets
    Uses temporary files to avoid memory overflow
    """
    import tempfile
    import os
    import gc

    logging.info("=== DISK-BASED CONSOLIDATION FOR EXTREMELY LARGE DATASETS ===")

    # Create temporary directory
    temp_dir = tempfile.mkdtemp(prefix='consolidation_')
    temp_files = []

    logging.info(f"Created temporary directory: {temp_dir}")
    log_memory_usage("before disk-based processing")

    try:
        # Process each lazy frame in chunks and write to temporary files
        for i, lazy_df in enumerate(lazy_frames):
            logging.info(f"Processing lazy frame {i+1}/{len(lazy_frames)}: {parquet_files[i]}")

            try:
                # Force garbage collection before processing each frame
                gc.collect()
                log_memory_usage(f"before processing frame {i+1}")

                # Collect this frame with streaming
                df = lazy_df.collect(streaming=True)
                logging.info(f"✓ Collected frame {i+1} - Shape: {df.shape}")

                # Write to temporary file
                temp_file = os.path.join(temp_dir, f'temp_{i:04d}.parquet')
                df.write_parquet(
                    temp_file,
                    compression='snappy',
                    use_pyarrow=False,
                    row_group_size=50000  # Smaller row groups for better memory management
                )

                temp_files.append(temp_file)
                logging.info(f"✓ Written temporary file: {temp_file} - Shape: {df.shape}")

                # Clean up
                del df
                gc.collect()
                log_memory_usage(f"after processing frame {i+1}")

            except Exception as e:
                logging.error(f"❌ Failed to process frame {i}: {type(e).__name__}: {e}")
                import traceback
                logging.error(f"❌ Frame {i} traceback: {traceback.format_exc()}")
                continue

        if not temp_files:
            raise ValueError("No temporary files were created")

        logging.info(f"✓ Successfully created {len(temp_files)} temporary files")

        # Now read all temporary files as lazy frames and create final lazy frame
        logging.info("Creating final lazy frame from temporary files...")
        final_lazy_frames = []

        try:
            for i, temp_file in enumerate(temp_files):
                logging.info(f"Creating lazy frame for temp file {i+1}/{len(temp_files)}: {temp_file}")

                # Verify temp file exists and has content
                if not os.path.exists(temp_file):
                    logging.error(f"❌ Temporary file does not exist: {temp_file}")
                    continue

                file_size = os.path.getsize(temp_file)
                if file_size == 0:
                    logging.error(f"❌ Temporary file is empty: {temp_file}")
                    continue

                logging.info(f"✓ Temp file {i+1} size: {file_size} bytes")

                lazy_temp = pl.scan_parquet(temp_file)
                final_lazy_frames.append(lazy_temp)
                logging.info(f"✓ Created lazy frame for temp file {i+1}")

            if not final_lazy_frames:
                raise ValueError("No valid temporary lazy frames were created")

            # Concatenate all temporary lazy frames
            logging.info(f"Concatenating {len(final_lazy_frames)} temporary lazy frames...")
            final_lazy = pl.concat(final_lazy_frames, how="vertical")
            logging.info("✓ Successfully concatenated temporary lazy frames")

        except Exception as e:
            logging.error(f"❌ Error creating final lazy frame: {type(e).__name__}: {e}")
            import traceback
            logging.error(f"❌ Traceback: {traceback.format_exc()}")
            raise

        # Use a streaming approach to avoid loading everything into memory
        logging.info("Using streaming collection to avoid memory overflow...")

        try:
            # Check memory usage before attempting final collection
            log_memory_usage("before final collection")

            # If memory usage is already very high, use streaming write approach
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024

            if memory_mb > 800:  # If using more than 800MB (Azure Function safety threshold)
                logging.warning(f"⚠️ Memory usage too high ({memory_mb:.1f} MB) - using streaming write approach")
                return consolidate_with_streaming_write(final_lazy, temp_files)

            # Add more detailed logging and error handling
            logging.info(f"Attempting to collect {len(final_lazy_frames)} concatenated lazy frames...")

            # Try to collect with streaming and handle memory errors gracefully
            final_df = final_lazy.collect(streaming=True)
            logging.info(f"✓ Disk-based consolidation completed - Final shape: {final_df.shape}")

            logging.info("Memory usage after final collection:")
            log_memory_usage("after final collection")
            return final_df

        except MemoryError as e:
            logging.error(f"❌ Memory error during streaming collection: {e}")
            logging.warning("⚠️ Dataset too large even for streaming - using streaming write approach")
            return consolidate_with_streaming_write(final_lazy, temp_files)

        except Exception as e:
            logging.error(f"❌ Unexpected error during streaming collection: {type(e).__name__}: {e}")
            logging.error(f"❌ Error details: {str(e)}")
            import traceback
            logging.error(f"❌ Full traceback: {traceback.format_exc()}")
            logging.warning("⚠️ Falling back to streaming write approach due to error")
            return consolidate_with_streaming_write(final_lazy, temp_files)

    finally:
        # Clean up temporary files
        try:
            import shutil
            shutil.rmtree(temp_dir)
            logging.info("🧹 Temporary consolidation files cleaned up")
        except Exception as e:
            logging.warning(f"⚠️ Could not clean up temporary directory: {e}")


def collect_large_dataset_in_chunks(lazy_df, total_rows):
    """
    Collect a large dataset in memory-efficient chunks and return the complete dataset
    This ensures ALL data is processed, not just a sample
    """
    import polars as pl
    import gc

    logging.info(f"=== CHUNKED COLLECTION FOR LARGE DATASET ({total_rows:,} rows) ===")

    # Calculate conservative chunk size based on row count to reduce peak memory
    if total_rows > 5_000_000:
        chunk_size = 100_000  # 100K rows per chunk
    elif total_rows > 2_000_000:
        # chunk_size = 250_000  # 250K rows per chunk
        chunk_size = 750_000  # 750K rows per chunk 
    else:
        chunk_size = 500_000  # 500K rows per chunk

    logging.info(f"Using chunk size: {chunk_size:,} rows")

    num_chunks = (total_rows + chunk_size - 1) // chunk_size

    # Incrementally build the final DataFrame to avoid holding all chunks at once
    final_df = None

    for i in range(num_chunks):
        start_row = i * chunk_size
        end_row = min(start_row + chunk_size, total_rows)
        logging.info(f"Processing chunk {i+1}/{num_chunks} (rows {start_row:,} to {end_row:,})")

        try:
            # Force garbage collection before each chunk
            gc.collect()

            # Collect this chunk with streaming
            chunk_df = lazy_df.slice(start_row, chunk_size).collect(streaming=True)
            logging.info(f"✓ Chunk {i+1} collected - Shape: {chunk_df.shape}")

            # Incrementally concatenate and free memory from previous pieces
            if final_df is None:
                final_df = chunk_df
            else:
                final_df = pl.concat([final_df, chunk_df], how="vertical", rechunk=True)
                # Explicitly drop reference to the just-appended chunk
                del chunk_df
                gc.collect()

        except Exception as e:
            logging.error(f"❌ Error processing chunk {i+1}: {e}")
            # Continue with other chunks rather than failing completely
            continue

    if final_df is not None:
        logging.info(f"✓ Chunked collection completed - Final shape: {final_df.shape}")
        return final_df
    else:
        raise ValueError("No chunks were successfully processed")


def consolidate_with_streaming_write(final_lazy, temp_files):
    """
    Consolidate data using streaming write approach - writes directly to file without loading into memory
    This is the ultimate fallback for extremely large datasets
    """
    import tempfile
    import os
    import gc

    logging.info("=== STREAMING WRITE CONSOLIDATION ===")
    logging.info("Using streaming write to avoid memory overflow")

    try:
        # Create a temporary output file
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_output:
            temp_output_path = temp_output.name

        logging.info(f"Writing consolidated data to temporary file: {temp_output_path}")

        # Use sink_parquet to write directly without collecting
        final_lazy.sink_parquet(
            temp_output_path,
            compression='snappy',
            row_group_size=50000  # Smaller row groups for better memory management
        )

        logging.info("✓ Streaming write completed")

        # Now read back the consolidated file as a lazy frame and collect in chunks
        logging.info("Reading back consolidated file in chunks...")
        consolidated_lazy = pl.scan_parquet(temp_output_path)

        # Get the total row count without loading data
        try:
            # Try to get row count efficiently
            row_count = consolidated_lazy.select(pl.len()).collect().item()
            logging.info(f"✓ Consolidated file contains {row_count:,} rows")

            # Process all rows using chunked collection instead of sampling
            if row_count > 1000000:
                logging.warning(f"⚠️ Large dataset detected ({row_count:,} rows) - using chunked processing to handle all data")
                result_df = collect_large_dataset_in_chunks(consolidated_lazy, row_count)

                # Add metadata about chunked processing
                result_df = result_df.with_columns([
                    pl.lit("streaming_write_chunked_full").alias("_consolidation_method"),
                    pl.lit(f"Full consolidation of {row_count:,} rows using chunked processing - all data preserved").alias("_consolidation_note")
                ])

                logging.info(f"✓ Chunked consolidation completed with ALL data - Shape: {result_df.shape}")
            else:
                # Small enough to collect fully
                result_df = consolidated_lazy.collect()

                # Add metadata
                result_df = result_df.with_columns([
                    pl.lit("streaming_write_full").alias("_consolidation_method"),
                    pl.lit(f"Full consolidation of {row_count:,} rows using streaming write").alias("_consolidation_note")
                ])

                logging.info(f"✓ Streaming write consolidation completed - Shape: {result_df.shape}")

            return result_df

        except Exception as e:
            logging.error(f"❌ Error reading back consolidated file: {e}")
            # Fallback to just reading a small sample
            result_df = consolidated_lazy.limit(100000).collect()

            # Add metadata about the fallback
            result_df = result_df.with_columns([
                pl.lit("streaming_write_fallback_100k").alias("_consolidation_method"),
                pl.lit("Fallback sample of 100K rows due to read error").alias("_consolidation_note")
            ])

            logging.info(f"✓ Streaming write fallback completed - Shape: {result_df.shape}")
            return result_df

    except Exception as e:
        logging.error(f"❌ Streaming write consolidation failed: {type(e).__name__}: {e}")
        import traceback
        logging.error(f"❌ Full traceback: {traceback.format_exc()}")

        # Ultimate fallback - return data from first temp file only
        logging.warning("⚠️ Using ultimate fallback - first temp file only")
        return process_in_memory_safe_chunks([], temp_files)

    finally:
        # Clean up temporary output file
        try:
            if 'temp_output_path' in locals():
                os.unlink(temp_output_path)
                logging.info("🧹 Temporary output file cleaned up")
        except:
            pass

def process_in_memory_safe_chunks(lazy_frames, temp_files):
    """
    Process extremely large datasets by taking only a subset to prevent memory overflow
    This is a safety mechanism when even streaming approaches fail
    """
    import polars as pl
    import gc

    logging.info("=== MEMORY-SAFE CHUNK PROCESSING ===")
    logging.warning("⚠️ Dataset too large for full consolidation - using subset approach")

    try:
        # Handle case where lazy_frames is empty but we have temp_files
        if not lazy_frames and temp_files:
            logging.info("No lazy frames provided, creating from temp files")
            lazy_frames = []
            for temp_file in temp_files[:2]:  # Take first 2 temp files
                if os.path.exists(temp_file):
                    lazy_frames.append(pl.scan_parquet(temp_file))

        # Validate inputs
        if not lazy_frames:
            raise ValueError("No lazy frames or temp files available")

        # Take only the first few files to prevent memory issues
        max_files = min(2, len(lazy_frames))  # Reduced to 2 files for safety
        logging.info(f"Processing only first {max_files} files out of {len(lazy_frames)} to prevent memory overflow")

        selected_frames = lazy_frames[:max_files]

        # Force garbage collection before processing
        gc.collect()
        log_memory_usage("before chunk processing")

        # Process each frame individually and combine
        if len(selected_frames) == 1:
            logging.info("Processing single frame...")
            final_lazy = selected_frames[0]
        else:
            logging.info(f"Concatenating {len(selected_frames)} frames...")
            final_lazy = pl.concat(selected_frames, how="vertical")

        # Try to collect with streaming - process ALL data, not just a subset
        logging.info("Collecting with streaming - processing all available data...")

        # Get row count first to determine processing approach
        try:
            row_count = final_lazy.select(pl.len()).collect().item()
            logging.info(f"Total rows to process: {row_count:,}")

            if row_count > 1000000:
                # Use chunked processing for large datasets
                result_df = collect_large_dataset_in_chunks(final_lazy, row_count)
            else:
                # Small enough for direct streaming collection
                result_df = final_lazy.collect(streaming=True)

        except Exception as e:
            logging.warning(f"Could not determine row count: {e}, using direct streaming collection")
            result_df = final_lazy.collect(streaming=True)

        # Add metadata about the chunked processing
        result_df = result_df.with_columns([
            pl.lit(f"memory_safe_full_{max_files}_of_{len(lazy_frames)}").alias("_consolidation_method"),
            pl.lit(f"Processed {max_files} files out of {len(lazy_frames)} with full data preservation").alias("_consolidation_note")
        ])

        logging.info(f"✓ Memory-safe chunk processing completed - Shape: {result_df.shape}")
        logging.warning(f"⚠️ Note: {len(lazy_frames) - max_files} files were skipped to prevent memory issues")

        log_memory_usage("after chunk processing")
        return result_df

    except Exception as e:
        logging.error(f"❌ Even chunk processing failed: {type(e).__name__}: {e}")
        import traceback
        logging.error(f"❌ Full traceback: {traceback.format_exc()}")

        # Last resort - return just the first file with heavy limits
        try:
            logging.warning("⚠️ Using single file as last resort")
            if lazy_frames:
                first_lazy = lazy_frames[0]
                result_df = first_lazy.limit(100000).collect()  # Limit to 100K rows as absolute safety

                # Add metadata about the emergency processing
                result_df = result_df.with_columns([
                    pl.lit("emergency_single_file_100k_limit").alias("_consolidation_method"),
                    pl.lit("Emergency fallback due to memory constraints - limited to 100K rows").alias("_consolidation_note")
                ])

                logging.info(f"✓ Emergency single file processing completed - Shape: {result_df.shape}")
                return result_df
            else:
                raise ValueError("No lazy frames available for emergency processing")

        except Exception as emergency_e:
            logging.error(f"❌ Emergency processing also failed: {emergency_e}")
            raise RuntimeError(f"All consolidation methods failed. Original error: {e}, Emergency error: {emergency_e}")


def consolidate_multiple_file_types_memory_efficient(parquet_files, container_name):
    """
    Memory-efficient consolidation for multiple file types
    Groups files by type and processes each group separately to prevent memory overflow
    """
    try:
        logging.info("🔄 Memory-efficient consolidation for multiple file types")

        # Group files by type/pattern
        file_groups = {}
        for file_path in parquet_files:
            filename = file_path.split('/')[-1]

            # Extract file type/pattern (e.g., FIMASTSALES, RO_TIME, etc.)
            file_type = "unknown"
            if "FIMASTSALES" in filename.upper():
                file_type = "FIMASTSALES"
            elif "RO_TIME" in filename.upper():
                file_type = "RO_TIME"
            elif "CUSTOMER" in filename.upper():
                file_type = "CUSTOMER"
            else:
                # Use first part of filename as type
                parts = filename.split('_')
                if parts:
                    file_type = parts[0]

            if file_type not in file_groups:
                file_groups[file_type] = []
            file_groups[file_type].append(file_path)

        logging.info(f"📊 Found {len(file_groups)} file types: {list(file_groups.keys())}")

        # If only one file type and few files, use regular consolidation
        if len(file_groups) == 1 and len(parquet_files) <= 3:
            logging.info("🔄 Single file type with few files - using regular consolidation")
            return consolidate_parquet_files_lazy(parquet_files, container_name)

        # Process each group separately for memory efficiency
        consolidated_groups = []
        for file_type, files in file_groups.items():
            logging.info(f"🔄 Processing {file_type} group with {len(files)} files")

            try:
                if len(files) <= 2:  # Small groups - process normally
                    group_df = consolidate_parquet_files_lazy(files, container_name)
                else:  # Large groups - use sampling to prevent memory issues
                    logging.warning(f"⚠️ Large group {file_type} ({len(files)} files) - using representative sample")
                    sample_files = files[:2]  # Take first 2 files as representative sample
                    group_df = consolidate_parquet_files_lazy(sample_files, container_name)

                # Add file type metadata
                group_df = group_df.with_columns([
                    pl.lit(file_type).alias("_file_type"),
                    pl.lit(len(files)).alias("_files_in_group")
                ])

                consolidated_groups.append(group_df)

            except Exception as e:
                logging.error(f"❌ Error processing {file_type} group: {e}")
                continue

        if not consolidated_groups:
            logging.error("❌ No groups were successfully processed")
            # Fallback to regular consolidation with first few files
            return consolidate_parquet_files_lazy(parquet_files[:2], container_name)

        # Combine all groups (if multiple types exist)
        if len(consolidated_groups) == 1:
            final_df = consolidated_groups[0]
        else:
            logging.info(f"🔗 Combining {len(consolidated_groups)} file type groups")
            final_df = pl.concat(consolidated_groups, how="vertical")

        logging.info(f"✅ Multi-type consolidation completed - Final shape: {final_df.shape}")
        return final_df

    except Exception as e:
        logging.error(f"❌ Error in multi-type consolidation: {e}")
        # Fallback to regular consolidation with limited files
        logging.warning("⚠️ Falling back to regular consolidation with limited files")
        return consolidate_parquet_files_lazy(parquet_files[:2], container_name)


def consolidate_files(csv_files, container_name):
    """
    Legacy File Consolidation Logic (GREEN BOX - Comprehensive Logging Required)
    Updated to use Polars instead of pandas for better performance

    Decision Logic:
    - IF 1 file: Log decision and proceed with single file
    - IF multiple files: Log consolidation process and merge files

    Args:
        csv_files: List of CSV file names in blob storage
        container_name: Name of the blob container

    Returns:
        tuple: (polars.DataFrame, list) - Consolidated DataFrame and list of successfully processed files
    """

    # GREEN BOX LOGGING START
    logging.info("=" * 50)
    logging.info("=== FILE CONSOLIDATION LOGIC START ===")
    logging.info("=" * 50)

    file_count = len(csv_files)
    logging.info(f"Files found in container '{container_name}': {file_count}")

    for i, filename in enumerate(csv_files, 1):
        logging.info(f"  {i}. {filename}")

    # Decision Logic Implementation
    if file_count == 0:
        logging.error("❌ No CSV files found for processing")
        logging.info("=== FILE CONSOLIDATION LOGIC END ===")
        raise ValueError("No CSV files found in the source container")

    elif file_count == 1:
        # Single file processing path
        single_file = csv_files[0]
        logging.info(f"✓ Single CSV file detected: {single_file}")
        logging.info("Decision: Proceeding with single file processing")
        logging.info("Consolidation method: Direct processing (no merging required)")

        try:
            # Download and process the single file using Polars
            df = download_csv_from_blob(single_file, container_name)
            logging.info(f"✓ Single file loaded successfully")
            logging.info(f"  - File: {single_file}")
            logging.info(f"  - Shape: {df.shape}")
            logging.info(f"  - Columns: {len(df.columns)}")

            # Add source file information using Polars metadata
            df = df.with_columns([
                pl.lit(single_file).alias("_source_file"),
                pl.lit("single_file").alias("_consolidation_method")
            ])

        except Exception as e:
            logging.error(f"❌ Failed to load single file {single_file}: {str(e)}")
            raise

    else:
        # Multiple files consolidation path
        logging.info(f"✓ Multiple CSV files detected: {file_count} files")
        logging.info("Decision: Consolidating multiple files into single dataset")
        logging.info(f"Consolidation method: {PROCESSING_CONFIG['consolidation_method']}")

        consolidated_dataframes = []
        successful_files = []
        failed_files = []

        # Process each file
        for i, filename in enumerate(csv_files, 1):
            try:
                logging.info(f"Processing file {i}/{file_count}: {filename}")
                df_temp = download_csv_from_blob(filename, container_name)

                # Add source tracking using Polars
                df_temp = df_temp.with_columns([
                    pl.lit(filename).alias("_source_file")
                ])
                consolidated_dataframes.append(df_temp)
                successful_files.append(filename)

                logging.info(f"  ✓ File {i} loaded: {filename} - Shape: {df_temp.shape}")

            except Exception as e:
                logging.error(f"  ❌ File {i} failed: {filename} - Error: {str(e)}")
                failed_files.append(filename)

        # Check if we have any successful files
        if not consolidated_dataframes:
            logging.error("❌ All files failed to load during consolidation")
            logging.info("=== FILE CONSOLIDATION LOGIC END ===")
            raise ValueError("All CSV files failed to load")

        # Consolidate the DataFrames using Polars
        try:
            logging.info("Consolidating DataFrames using Polars...")

            # Check for schema mismatches and align if needed
            if len(set(df.shape[1] for df in consolidated_dataframes)) > 1:
                logging.warning("⚠️ Schema mismatch detected between files:")
                for i, df in enumerate(consolidated_dataframes):
                    logging.warning(f"  File {i+1}: {successful_files[i]} - {df.shape[1]} columns")

                # Get all unique columns across all DataFrames
                # Preserve column order from the first file, then add missing columns
                all_columns = list(consolidated_dataframes[0].columns)  # Start with first file's order

                # Add any additional columns from other files (append at the end)
                for df in consolidated_dataframes[1:]:
                    for col in df.columns:
                        if col not in all_columns:
                            all_columns.append(col)

                logging.info(f"🔧 Aligning schemas - Total unique columns: {len(all_columns)}")
                logging.info(f"📋 Column order preserved from first file, additional columns appended")
                logging.info(f"🎯 First 5 columns will be: {all_columns[:5]}")

                # Align all DataFrames to have the same columns and types
                aligned_dataframes = []
                for i, df in enumerate(consolidated_dataframes):
                    # Add missing columns with null values
                    missing_columns = [col for col in all_columns if col not in df.columns]
                    if missing_columns:
                        logging.info(f"  Adding {len(missing_columns)} missing columns to file {i+1}")
                        for col in missing_columns:
                            # Use string null instead of generic null to avoid type conflicts
                            df = df.with_columns(pl.lit(None, dtype=pl.Utf8).alias(col))

                    # Cast all columns to string to ensure type compatibility
                    logging.info(f"  Converting all columns to string type for file {i+1}")
                    string_columns = []
                    for col in all_columns:
                        string_columns.append(pl.col(col).cast(pl.Utf8).alias(col))
                    df = df.with_columns(string_columns)

                    # Reorder columns to match the standard order
                    df = df.select(all_columns)
                    aligned_dataframes.append(df)

                consolidated_dataframes = aligned_dataframes
                logging.info("✅ Schema alignment completed")

            # Use Polars concat (vertical concatenation)
            df = pl.concat(consolidated_dataframes, how="vertical")
            logging.info("✓ DataFrames concatenated successfully using Polars")

            # Add consolidation metadata using Polars columns
            df = df.with_columns([
                pl.lit("multi_file_concat_aligned").alias("_consolidation_method")
            ])

            logging.info(f"✓ Consolidation completed successfully")
            logging.info(f"  - Total files processed: {len(successful_files)}")
            logging.info(f"  - Failed files: {len(failed_files)}")
            logging.info(f"  - Final consolidated shape: {df.shape}")
            logging.info(f"  - Final columns: {len(df.columns)}")

            if failed_files:
                logging.warning(f"⚠️  Some files failed during consolidation: {failed_files}")

        except Exception as e:
            logging.error(f"❌ DataFrame consolidation failed: {str(e)}")
            raise

    # Final consolidation summary
    logging.info("=" * 30)
    logging.info("CONSOLIDATION SUMMARY:")
    logging.info(f"  Input files: {file_count}")
    logging.info(f"  Processing method: {'Single file' if file_count == 1 else 'Multi-file consolidation'}")
    logging.info(f"  Final dataset shape: {df.shape}")
    logging.info(f"  Source files: {successful_files if file_count > 1 else [csv_files[0]]}")
    logging.info("=" * 30)

    logging.info("=== FILE CONSOLIDATION LOGIC END ===")
    logging.info("=" * 50)
    # GREEN BOX LOGGING END

    # Return both the consolidated DataFrame and the list of successfully processed files
    if file_count == 1:
        # For single file, return the single file name
        successfully_processed_files = [csv_files[0]]
    else:
        # For multiple files, return the tracked successful files
        successfully_processed_files = successful_files

    return df, successfully_processed_files
