import os
import sys
import logging
from datetime import datetime

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from azure_config import (
    get_job_configs,
    get_output_container,
    get_source_folder_path,
    get_output_folder_path,
    get_zip_movement_enabled,
    get_zip_source_folder,
    get_zip_archive_folder,
    get_zip_file_pattern,
    SOURCE_CONTAINER,
    OUTPUT_CONTAINER,
    SOURCE_FOLDER_PATH,
    OUTPUT_FOLDER_PATH,
    FILE_PATTERNS
)
from blob_manager import (
    list_csv_files_in_container, upload_csv_to_blob,
    list_parquet_files_in_container, upload_parquet_to_blob, cleanup_placeholder_files,
    cleanup_source_csv_files, move_zip_files_to_archive, move_zip_files_to_archive_dealer_aware, cleanup_intermediate_parquet_files,
    log_memory_usage, download_csv_sample_for_schema
)

def process_enabled_jobs():
    """
    Process all enabled jobs from Azure Table Storage configuration

    This function:
    1. Loads all enabled job configurations from sftpConfig table
    2. For each enabled job, processes files using job-specific settings
    3. Uses configurable paths, patterns, and surrogate key settings

    Returns:
        dict: Processing results for all jobs
    """
    logging.info("🚀 Starting dynamic job processing based on Azure Table Storage configuration")

    # Load enabled job configurations
    job_configs = get_job_configs()

    if not job_configs:
        logging.warning("⚠️ No enabled jobs found in Azure Table Storage")
        return {
            "status": "warning",
            "message": "No enabled jobs found",
            "jobs_processed": 0,
            "results": {}
        }

    logging.info(f"📋 Found {len(job_configs)} enabled jobs: {list(job_configs.keys())}")

    all_results = {}
    successful_jobs = 0
    failed_jobs = 0

    # Process each enabled job
    for job_key, job_config in job_configs.items():
        logging.info(f"🔄 Processing job: {job_key}")

        try:
            # Extract job-specific configuration
            source_container = job_config.get('SourceContainer', 'sftp-landing')
            output_container = job_config.get('OutputContainer', 'sftp-landing')
            source_subfolder = job_config.get('SourceSubfolder', '')
            output_subfolder = job_config.get('OutputSubfolder', '')
            file_prefix = job_config.get('FilePrefix', job_key)

            # Build full folder paths
            source_folder = f"{job_config.get('Folder', '')}/{source_subfolder}".strip('/')
            output_folder = f"{job_config.get('Folder', '')}/{output_subfolder}".strip('/')

            logging.info(f"📁 Job {job_key}: {source_container}/{source_folder} → {output_container}/{output_folder}")
            logging.info(f"🔍 Job {job_key}: Using FilePrefix = '{file_prefix}' for file pattern matching")

            # Process this specific job
            job_result = process_single_job(
                job_key=job_key,
                source_container=source_container,
                output_container=output_container,
                source_folder=source_folder,
                output_folder=output_folder,
                file_patterns=[file_prefix]
            )

            all_results[job_key] = job_result

            if job_result.get('status') == 'success':
                successful_jobs += 1
                logging.info(f"✅ Job {job_key} completed successfully")
            else:
                failed_jobs += 1
                logging.error(f"❌ Job {job_key} failed: {job_result.get('error_message', 'Unknown error')}")

        except Exception as e:
            failed_jobs += 1
            error_msg = f"Job {job_key} processing failed: {str(e)}"
            logging.error(error_msg)
            all_results[job_key] = {
                "status": "error",
                "error_message": error_msg,
                "processing_stats": {}
            }

    # Summary
    total_jobs = len(job_configs)
    logging.info(f"🎯 Job processing summary: {successful_jobs}/{total_jobs} successful, {failed_jobs} failed")

    return {
        "status": "success" if failed_jobs == 0 else "partial_success",
        "message": f"Processed {total_jobs} jobs: {successful_jobs} successful, {failed_jobs} failed",
        "jobs_processed": total_jobs,
        "successful_jobs": successful_jobs,
        "failed_jobs": failed_jobs,
        "results": all_results
    }

def process_single_job(job_key, source_container, output_container, source_folder, output_folder, file_patterns):
    """
    Process a single job with specific configuration

    Args:
        job_key (str): Job identifier (e.g., 'FIMASTSALES_', 'CUSTOMER-ACCTG_')
        source_container (str): Source blob container name
        output_container (str): Output blob container name
        source_folder (str): Source folder path
        output_folder (str): Output folder path
        file_patterns (list): List of file patterns to match

    Returns:
        dict: Processing results for this job
    """
    logging.info(f"🔄 Processing job {job_key}: {source_container}/{source_folder} → {output_container}/{output_folder}")

    try:
        # Check for Parquet files first (preferred)
        parquet_files = list_parquet_files_in_container(source_container, file_patterns, folder_path=source_folder)

        if parquet_files:
            logging.info(f"✓ Found {len(parquet_files)} Parquet files, using Parquet pipeline")
            return run_parquet_processing_pipeline_for_job(
                job_key=job_key,
                source_container=source_container,
                output_container=output_container,
                source_folder=source_folder,
                output_folder=output_folder,
                file_patterns=file_patterns
            )

        # Fallback to CSV files - use hybrid CSV-to-Parquet pipeline
        csv_files = list_csv_files_in_container(source_container, file_patterns, folder_path=source_folder, job_key=job_key)

        if csv_files:
            logging.info(f"✓ Found {len(csv_files)} CSV files, using hybrid CSV-to-Parquet pipeline")
            return run_csv_to_parquet_pipeline_for_job(
                job_key=job_key,
                source_container=source_container,
                output_container=output_container,
                source_folder=source_folder,
                output_folder=output_folder,
                file_patterns=file_patterns,
                csv_files=csv_files
            )

        # No files found for this job
        logging.warning(f"⚠️ No CSV or Parquet files found for job {job_key} in {source_container}/{source_folder}")
        return {
            "status": "warning",
            "message": f"No files found for job {job_key}",
            "processing_stats": {
                "files_found": 0,
                "files_processed": 0
            }
        }

    except Exception as e:
        error_msg = f"Job {job_key} processing failed: {str(e)}"
        logging.error(error_msg)
        return {
            "status": "error",
            "error_message": error_msg,
            "processing_stats": {}
        }

def convert_csv_files_to_parquet(csv_files, source_container, output_container, output_folder, job_key=None):
    """
    Convert CSV files to Parquet individually with per-file transformation for memory efficiency

    NEW APPROACH: Transform each file (add surrogate key + audit columns) BEFORE combining
    This reduces peak memory usage compared to combining first then transforming

    Args:
        csv_files (list): List of CSV file paths
        source_container (str): Source container name
        output_container (str): Output container name
        output_folder (str): Output folder path
        job_key (str): Job key for configuration

    Returns:
        list: List of created Parquet file paths (already transformed)
    """
    from blob_manager import download_csv_from_blob, upload_parquet_to_blob
    from data_transformer import add_surrogate_key_configurable, append_audit_columns_polars, ensure_consistent_schema
    import os
    import gc

    logging.info(f"🔄 Converting and transforming {len(csv_files)} CSV files to Parquet individually...")
    logging.info("🧠 MEMORY-EFFICIENT APPROACH: Transform each file before combining")

    # Initialize variables for both phases
    failed_files = []
    parquet_files = []
    expected_schema = None

    # Phase 1: Quick schema discovery pass (lightweight)
    logging.info("📋 Phase 1: Schema discovery across all files...")

    for i, csv_file in enumerate(csv_files, 1):
        try:
            logging.info(f"🔍 Discovering schema for file {i}/{len(csv_files)}: {os.path.basename(csv_file)}")

            # LIGHTWEIGHT schema discovery - only read header + few sample rows
            logging.info(f"📋 Reading only header and sample rows for schema discovery...")
            df_sample = download_csv_sample_for_schema(csv_file, source_container, sample_rows=10)
            logging.info(f"✓ Schema sample loaded: {df_sample.shape} (lightweight)")

            # Add transformation columns to get complete schema
            df_with_surrogate = add_surrogate_key_configurable(df_sample, job_key=job_key)
            df_with_audit = append_audit_columns_polars(df_with_surrogate, [csv_file])

            # Update expected schema
            _, expected_schema = ensure_consistent_schema(df_with_audit, expected_schema, f"{os.path.basename(csv_file)} (schema discovery)")

            # Clean up sample data
            del df_sample, df_with_surrogate, df_with_audit
            gc.collect()

        except Exception as e:
            logging.error(f"❌ Schema discovery failed for {csv_file}: {str(e)}")
            failed_files.append(csv_file)

    if failed_files:
        error_msg = f"Schema discovery failed for {len(failed_files)} files: {failed_files}"
        logging.error(f"❌ {error_msg}")
        raise ValueError(error_msg)

    logging.info(f"✅ Schema discovery completed. Final schema has {len(expected_schema)} columns")
    logging.info(f"📋 Final column order: {list(expected_schema.keys())}")

    # Phase 2: Transform and upload files with consistent schema
    logging.info("🔄 Phase 2: Transform and upload files with consistent schema...")
    failed_files.clear()  # Clear the list from Phase 1, reuse for Phase 2

    for i, csv_file in enumerate(csv_files, 1):
        try:
            logging.info(f"📄 Processing CSV file {i}/{len(csv_files)}: {csv_file}")
            log_memory_usage(f"before processing file {i}/{len(csv_files)}")

            # Step 1: Download CSV file
            df = download_csv_from_blob(csv_file, source_container)
            logging.info(f"✓ Loaded CSV: {df.shape}")
            log_memory_usage(f"after loading file {i}/{len(csv_files)}")

            # Step 2: Add surrogate key and composite key (FIRST columns)
            logging.info(f"🔑 Adding surrogate key and composite key to file {i}/{len(csv_files)}")
            df_with_surrogate = add_surrogate_key_configurable(df, job_key=job_key)
            logging.info(f"✓ Surrogate key added: {df_with_surrogate.shape}")
            log_memory_usage(f"after surrogate key for file {i}/{len(csv_files)}")

            # Step 3: Add audit columns (LAST columns)
            logging.info(f"📅 Adding audit columns to file {i}/{len(csv_files)}")
            df_transformed = append_audit_columns_polars(df_with_surrogate, [csv_file])
            logging.info(f"✓ Audit columns added: {df_transformed.shape}")
            log_memory_usage(f"after audit columns for file {i}/{len(csv_files)}")

            # Step 4: Ensure consistent schema across all files
            csv_basename = os.path.basename(csv_file)
            df_transformed, expected_schema = ensure_consistent_schema(
                df_transformed, expected_schema, csv_basename
            )
            logging.info(f"✓ Schema alignment completed: {df_transformed.shape}")
            log_memory_usage(f"after schema alignment for file {i}/{len(csv_files)}")

            # Clean up intermediate DataFrames
            del df, df_with_surrogate
            gc.collect()

            # Step 5: Generate Parquet filename and upload
            parquet_name = csv_basename.replace('.csv', '.parquet')

            # Get the actual path where the file will be uploaded (dealer-aware)
            from azure_config import get_output_blob_path
            actual_parquet_path = get_output_blob_path(parquet_name, job_key)

            # Upload transformed Parquet
            success = upload_parquet_to_blob(df_transformed, parquet_name, output_container, job_key)
            if success:
                parquet_files.append(actual_parquet_path)
                logging.info(f"✓ Transformed and uploaded: {actual_parquet_path}")
            else:
                error_msg = f"Failed to upload transformed Parquet: {parquet_name}"
                logging.error(f"❌ {error_msg}")
                failed_files.append(csv_file)

            # Explicitly delete DataFrame and force garbage collection to free memory
            del df_transformed
            gc.collect()
            log_memory_usage(f"after cleanup for file {i}/{len(csv_files)}")

        except Exception as e:
            error_msg = f"Failed to transform {csv_file}: {str(e)}"
            logging.error(f"❌ {error_msg}")
            import traceback
            logging.error(f"Full traceback: {traceback.format_exc()}")
            failed_files.append(csv_file)
            # Force garbage collection even on error
            gc.collect()

    # Check for failures and fail the whole batch if any file failed
    if failed_files:
        error_msg = f"Batch processing failed. {len(failed_files)} out of {len(csv_files)} files failed: {failed_files}"
        logging.error(f"❌ {error_msg}")
        raise ValueError(error_msg)

    logging.info(f"✅ Successfully transformed and converted {len(parquet_files)}/{len(csv_files)} files to Parquet")
    logging.info("🧠 All files now have surrogate key, composite key, and audit columns")

    # Final memory cleanup
    gc.collect()
    log_memory_usage("after all CSV to Parquet transformations")

    return parquet_files

def run_csv_to_parquet_pipeline_for_job(job_key, source_container, output_container, source_folder, output_folder, file_patterns, csv_files):
    """
    Job-specific CSV-to-Parquet pipeline using configurable settings

    Args:
        job_key (str): Job identifier for configuration lookup
        source_container (str): Source blob container
        output_container (str): Output blob container
        source_folder (str): Source folder path
        output_folder (str): Output folder path
        file_patterns (list): File patterns to match
        csv_files (list): List of CSV files to process

    Returns:
        dict: Processing results
    """
    from file_consolidator import consolidate_files, consolidate_parquet_files_streaming
    from data_transformer import add_surrogate_key_configurable, append_audit_columns

    logging.info("🚀 Starting CSV-to-Parquet Hybrid Pipeline...")
    logging.info("=" * 80)

    try:
        # Check if this is dealer-aware processing
        from azure_config import get_dealer_prefix_enabled
        is_dealer_aware = get_dealer_prefix_enabled(job_key)
        
        if is_dealer_aware:
            # DEALER-AWARE PROCESSING: Process each file individually (like FIMASTSALES)
            logging.info("🏢 DEALER-AWARE PROCESSING: Processing each dealer file individually")
            logging.info("=" * 60)
            
            total_files_processed = 0
            successful_uploads = []
            
            for csv_file in csv_files:
                csv_basename = os.path.basename(csv_file)
                logging.info(f"🔄 Processing individual dealer file: {csv_basename}")
                
                # Process single file
                try:
                    # Load and process the CSV file
                    from blob_manager import download_csv_from_blob
                    df = download_csv_from_blob(csv_file, source_container)
                    
                    if df is None or df.height == 0:
                        logging.warning(f"⚠️ Skipping empty file: {csv_basename}")
                        continue
                    
                    # Add surrogate key
                    df = add_surrogate_key_configurable(df, job_key=job_key)
                    
                    # Add audit columns  
                    df = append_audit_columns(df, [csv_file])
                    
                    # Generate output filename
                    if csv_basename.lower().endswith('.csv'):
                        base_name = csv_basename[:-4]  # Remove .csv/.CSV
                    else:
                        base_name = csv_basename
                    
                    parquet_filename = f"{base_name}.parquet"
                    csv_filename = f"{base_name}.csv"
                    
                    # Upload both formats
                    parquet_success = upload_parquet_to_blob(df, parquet_filename, output_container, job_key)
                    csv_success = upload_csv_to_blob(df, csv_filename, output_container, job_key)
                    
                    if parquet_success and csv_success:
                        successful_uploads.append(csv_file)
                        total_files_processed += 1
                        logging.info(f"✅ Successfully processed dealer file: {csv_basename}")
                    else:
                        logging.error(f"❌ Failed to upload files for: {csv_basename}")
                        
                except Exception as e:
                    logging.error(f"❌ Error processing dealer file {csv_basename}: {e}")
                    continue
            
            logging.info(f"🎯 DEALER PROCESSING SUMMARY: {total_files_processed}/{len(csv_files)} files processed successfully")
            
            # CLEANUP FOR DEALER PROCESSING
            logging.info("🧹 CLEANUP: Removing successfully processed source CSV files...")
            cleanup_source_csv_files(source_container, successful_uploads)

            # Store processing results for zip archiving
            processing_results = {
                'status': 'success',
                'files_processed': total_files_processed,
                'total_files': len(csv_files),
                'successful_files': successful_uploads,
                'processing_type': 'individual_dealer_files'
            }
            
        else:
            # STANDARD PROCESSING: Consolidate multiple files (existing logic)
            logging.info("📊 STANDARD PROCESSING: Consolidating multiple files")
            logging.info("=" * 60)
            
            logging.info(f"Found {len(csv_files)} CSV files to process")

            # STEP 1: Convert CSV files to Parquet individually with per-file transformation
            parquet_files = convert_csv_files_to_parquet(csv_files, source_container, output_container, output_folder, job_key)
            logging.info(f"✅ STEP 1 COMPLETED: Transformed and converted {len(parquet_files)} CSV files to individual Parquet files")
            logging.info(f"📋 Individual transformed Parquet files: {parquet_files}")
            logging.info("🧠 Each file already has surrogate key, composite key, and audit columns")

            # STEP 2: MEMORY-EFFICIENT CONSOLIDATION - Combine pre-transformed files
            logging.info("STEP 2: CONSOLIDATING PRE-TRANSFORMED PARQUET FILES")
            logging.info("-" * 50)
            logging.info("🔄 Starting MEMORY-EFFICIENT consolidation of pre-transformed Parquet files...")
            logging.info("🧠 Using streaming approach to avoid loading all data into memory at once")

            # Use simple streaming consolidation - read one file at a time and append
            try:
                consolidated_df = consolidate_parquet_files_streaming(parquet_files, output_container)

                if consolidated_df is None:
                    raise ValueError("Consolidation returned None - no data consolidated from pre-transformed Parquet files")
                if consolidated_df.height == 0:
                    raise ValueError("Consolidation returned empty DataFrame - no rows in consolidated data")

                logging.info(f"✅ STEP 2 COMPLETED: Consolidated pre-transformed files to shape: {consolidated_df.shape}")
                logging.info("🧠 MEMORY EFFICIENCY: Used streaming consolidation to avoid memory overflow")

                # STEP 3: UPLOAD FINAL FILES (no additional transformation needed)
                logging.info("STEP 3: UPLOAD FINAL PARQUET AND CSV FILES")
                logging.info("-" * 50)
                logging.info("ℹ️ No additional transformation needed - files already have all required columns")

            except Exception as e:
                logging.error(f"❌ Consolidation failed: {str(e)}")
                import traceback
                logging.error(f"Consolidation traceback: {traceback.format_exc()}")
                raise ValueError(f"Failed to consolidate pre-transformed Parquet files: {str(e)}")

        # Generate output filename based on number of files

            if len(csv_files) == 1:
                # Single file: use original filename
                csv_basename = os.path.basename(csv_files[0])
                # Handle both .csv and .CSV extensions (case-insensitive)
                if csv_basename.lower().endswith('.csv'):
                    base_name = csv_basename[:-4]  # Remove last 4 characters (.csv or .CSV)
                else:
                    base_name = csv_basename
                parquet_filename = f"{base_name}.parquet"
                csv_filename = f"{base_name}.csv"
                logging.info(f"📄 Single file detected: Using original filename '{base_name}'")
            else:
                # Multiple files: use combined naming
                current_date = datetime.now().strftime("%Y-%m-%d")
                parquet_filename = f"combined_sales_data_{current_date}.parquet"
                csv_filename = f"combined_sales_data_{current_date}.csv"
                logging.info(f"📄 Multiple files detected ({len(csv_files)}): Using combined filename 'combined_sales_data_{current_date}'")

            # Upload Parquet file
            try:
                logging.info(f"🚀 Starting Parquet upload: {parquet_filename}")
                parquet_result = upload_parquet_to_blob(
                    consolidated_df,
                    parquet_filename,
                    output_container,
                    job_key
                )
                logging.info(f"✅ Parquet upload result: {parquet_result}")
            except Exception as e:
                logging.error(f"❌ Parquet upload failed: {e}")
                import traceback
                logging.error(f"Parquet upload traceback: {traceback.format_exc()}")
                parquet_result = False

            # Upload CSV file
            try:
                logging.info(f"🚀 Starting CSV upload: {csv_filename}")
                csv_result = upload_csv_to_blob(
                    consolidated_df,
                    csv_filename,
                    output_container,
                    job_key
                )
                logging.info(f"✅ CSV upload result: {csv_result}")
            except Exception as e:
                logging.error(f"❌ CSV upload failed: {e}")
                import traceback
                logging.error(f"CSV upload traceback: {traceback.format_exc()}")
                csv_result = False

            # STEP 5: CLEANUP AND ZIP MOVEMENT (STANDARD PROCESSING)
            logging.info("STEP 5: CLEANUP AND ZIP MOVEMENT")
            logging.info("-" * 50)

            try:
                # Clean up placeholder files
                logging.info("🧹 Cleaning up placeholder files...")
                cleanup_placeholder_files(output_container)
                logging.info("✅ Placeholder files cleaned up")
            except Exception as e:
                logging.error(f"❌ Placeholder cleanup failed: {e}")

            try:
                # Clean up intermediate parquet files (only for multiple file scenarios)
                if len(csv_files) > 1:
                    logging.info("🧹 Cleaning up intermediate parquet files...")
                    intermediate_cleanup_count = cleanup_intermediate_parquet_files(output_container, parquet_files)
                    logging.info(f"✅ Cleaned up {intermediate_cleanup_count} intermediate parquet files")
                else:
                    logging.info("ℹ️ Single file processing - no intermediate parquet cleanup needed")
            except Exception as e:
                logging.error(f"❌ Intermediate parquet cleanup failed: {e}")

            try:
                # Clean up source CSV files
                logging.info("🧹 Cleaning up source CSV files...")
                cleanup_source_csv_files(source_container, csv_files)
                logging.info("✅ Source CSV files cleaned up")
            except Exception as e:
                logging.error(f"❌ Source CSV cleanup failed: {e}")

        # Store processing results for zip archiving
        processing_results = {
            "status": "success",
            "message": f"Job {job_key} processed successfully",
            "processing_stats": {
                "job_key": job_key,
                "files_processed": len(csv_files),
                "total_rows": consolidated_df.height,
                "total_columns": consolidated_df.width,
                "parquet_uploaded": parquet_result is not None,
                "csv_uploaded": csv_result is not None,
                "source_files": csv_files,
                "output_parquet": f"{output_folder}/{parquet_filename}",
                "output_csv": f"{output_folder}/{csv_filename}"
            }
        }

        # Move zip files if enabled for this job (dealer-aware) - MOVED OUTSIDE BOTH PROCESSING BLOCKS
        zip_movement_results = move_zip_files_to_archive_dealer_aware(source_container, job_key)
        if zip_movement_results.get('status') not in ['disabled', 'no_dealers']:
            logging.info(f"📦 Zip archiving completed for job {job_key}: {zip_movement_results.get('successfully_moved', 0)} files moved")

        # Add zip results to processing results
        processing_results['zip_movement'] = zip_movement_results

        if is_dealer_aware:
            logging.info("🎉 Dealer-aware CSV-to-Parquet pipeline completed successfully!")
            logging.info(f"📊 Processed {processing_results['files_processed']} dealer files")
        else:
            logging.info("🎉 Standard CSV-to-Parquet pipeline completed successfully!")
            logging.info(f"📊 Final dataset: {consolidated_df.height} rows × {consolidated_df.width} columns")

        return processing_results

    except Exception as e:
        error_msg = f"CSV-to-Parquet pipeline failed for job {job_key}: {str(e)}"
        logging.error(error_msg)

        processing_stats = {
            "job_key": job_key,
            "files_found": len(csv_files) if csv_files else 0,
            "files_processed": 0,
            "error": str(e)
        }

        return {
            "status": "error",
            "error_message": error_msg,
            "processing_stats": processing_stats
        }

def run_parquet_processing_pipeline_for_job(job_key, source_container, output_container, source_folder, output_folder, file_patterns):
    """
    Job-specific Parquet processing pipeline using configurable settings

    Args:
        job_key (str): Job identifier for configuration lookup
        source_container (str): Source blob container
        output_container (str): Output blob container
        source_folder (str): Source folder path
        output_folder (str): Output folder path
        file_patterns (list): File patterns to match

    Returns:
        dict: Processing results
    """
    logging.info(f"🚀 Starting Parquet processing pipeline for job: {job_key}")

    try:
        # For now, delegate to the existing parquet pipeline
        # This can be enhanced later with job-specific logic
        return run_parquet_processing_pipeline()

    except Exception as e:
        error_msg = f"Parquet pipeline failed for job {job_key}: {str(e)}"
        logging.error(error_msg)

        return {
            "status": "error",
            "error_message": error_msg,
            "processing_stats": {"job_key": job_key, "error": str(e)}
        }

def _determine_job_key_from_files(csv_files):
    """
    Determine the job key from processed CSV files by matching file patterns

    Args:
        csv_files (list): List of CSV file paths that were processed

    Returns:
        str: Job key (e.g., 'FIMASTSALES_') or None if no match found
    """
    if not csv_files:
        return None

    # Get all job configurations to match against
    job_configs = get_job_configs()

    for job_key, job_config in job_configs.items():
        # Get file patterns for this job
        file_patterns = job_config.get('FilePatterns', [])
        if isinstance(file_patterns, str):
            file_patterns = [file_patterns]

        # Check if any processed file matches this job's patterns
        for csv_file in csv_files:
            filename = csv_file.split('/')[-1].upper()  # Get filename and normalize case
            for pattern in file_patterns:
                if pattern.upper() in filename:
                    logging.info(f"Determined job key '{job_key}' from file '{csv_file}' matching pattern '{pattern}'")
                    return job_key

    # Fallback: try to match common patterns
    for csv_file in csv_files:
        filename = csv_file.split('/')[-1].upper()
        if 'FIMASTSALES' in filename:
            return 'FIMASTSALES_'
        elif 'CUSTOMER' in filename and 'ACCTG' in filename:
            return 'CUSTOMER-ACCTG_'
        elif 'RO_TIME' in filename:
            return 'RO_TIME'

    logging.warning(f"Could not determine job key from processed files: {csv_files}")
    return None
from csv_processor import validate_csv_structure, clean_column_names, preview_dataframe
from file_consolidator import consolidate_files, consolidate_parquet_files_lazy, consolidate_multiple_file_types_memory_efficient
from data_transformer import (
    add_surrogate_key, append_audit_columns, log_raw_csv_enhanced,
    add_surrogate_key_polars, append_audit_columns_polars,
    add_surrogate_key_configurable
)

def log_transformation_completion_polars(df, processing_stats):
    """
    Transformation Completion Logging for Polars (GREEN BOX - Logging Required)

    Log "Landed Data to Bronze Completed" status and processing summary
    """

    try:
        # GREEN BOX LOGGING START
        logging.info("=" * 60)
        logging.info("=== TRANSFORMATION COMPLETION START (POLARS) ===")
        logging.info("=" * 60)

        # Log completion status
        logging.info("🎉 LANDED DATA TO BRONZE COMPLETED")
        logging.info("✓ All data transformations have been successfully applied using Polars")

        # Log final dataset summary
        logging.info("Final dataset summary:")
        logging.info(f"  - Final shape: {df.shape}")
        logging.info(f"  - Total rows: {df.height}")
        logging.info(f"  - Total columns: {df.width}")

        # Log processing statistics
        logging.info("Processing statistics:")
        for key, value in processing_stats.items():
            logging.info(f"  - {key}: {value}")

        # Log column structure
        columns = df.columns
        logging.info(f"Final column structure ({len(columns)} columns):")
        for i, col in enumerate(columns):
            if i == 0:
                logging.info(f"  {i:2d}. {col} (SURROGATE KEY - LEFTMOST)")
            elif col in ["Source Date/Time", "Created Date/Time", "Modified Date/Time"]:
                logging.info(f"  {i:2d}. {col} (AUDIT COLUMN - RIGHTMOST)")
            else:
                logging.info(f"  {i:2d}. {col} (DATA COLUMN)")

        # Log source information from processing stats (Polars doesn't use attrs)
        source_files = processing_stats.get('source_files', ['unknown'])
        consolidation_method = processing_stats.get('consolidation_method', 'unknown')

        logging.info("Source information:")
        logging.info(f"  - Source files: {source_files}")
        logging.info(f"  - Consolidation method: {consolidation_method}")
        logging.info(f"  - Processing timestamp: {datetime.now()}")

        # Placeholder for future Bronze layer requirements
        logging.info("Bronze layer status:")
        logging.info("  - Data quality: VALIDATED")
        logging.info("  - Schema compliance: VERIFIED")
        logging.info("  - Audit trail: COMPLETE")
        logging.info("  - Ready for downstream processing: YES")
        logging.info("  - Memory efficiency: OPTIMIZED (Polars)")

        logging.info("🏆 TRANSFORMATION PIPELINE COMPLETED SUCCESSFULLY")
        logging.info("=== TRANSFORMATION COMPLETION END ===")
        logging.info("=" * 60)
        # GREEN BOX LOGGING END

        return True

    except Exception as e:
        logging.error(f"❌ Transformation completion logging failed: {str(e)}")
        raise

def log_transformation_completion(df, processing_stats):
    """
    Legacy Transformation Completion Logging (GREEN BOX - Logging Required)

    Log "Landed Data to Bronze Completed" status and processing summary
    """

    try:
        # GREEN BOX LOGGING START
        logging.info("=" * 60)
        logging.info("=== TRANSFORMATION COMPLETION START ===")
        logging.info("=" * 60)

        # Log completion status
        logging.info("🎉 LANDED DATA TO BRONZE COMPLETED")
        logging.info("✓ All data transformations have been successfully applied")

        # Log final dataset summary
        logging.info("Final dataset summary:")
        logging.info(f"  - Final shape: {df.shape}")
        logging.info(f"  - Total rows: {df.height if hasattr(df, 'height') else len(df)}")
        logging.info(f"  - Total columns: {df.width if hasattr(df, 'width') else len(df.columns)}")

        # Log processing statistics
        logging.info("Processing statistics:")
        for key, value in processing_stats.items():
            logging.info(f"  - {key}: {value}")

        # Log column structure
        columns = df.columns
        logging.info(f"Final column structure ({len(columns)} columns):")
        for i, col in enumerate(columns):
            if i == 0:
                logging.info(f"  {i:2d}. {col} (SURROGATE KEY - LEFTMOST)")
            elif col in ["Source Date/Time", "Created Date/Time", "Modified Date/Time"]:
                logging.info(f"  {i:2d}. {col} (AUDIT COLUMN - RIGHTMOST)")
            else:
                logging.info(f"  {i:2d}. {col} (DATA COLUMN)")

        # Log source information - Polars doesn't have .attrs, use processing_stats
        source_files = processing_stats.get('source_files', ['unknown'])
        consolidation_method = processing_stats.get('consolidation_method', 'unknown')

        logging.info("Source information:")
        logging.info(f"  - Source files: {source_files}")
        logging.info(f"  - Consolidation method: {consolidation_method}")
        logging.info(f"  - Processing timestamp: {datetime.now()}")

        # Placeholder for future Bronze layer requirements
        logging.info("Bronze layer status:")
        logging.info("  - Data quality: VALIDATED")
        logging.info("  - Schema compliance: VERIFIED")
        logging.info("  - Audit trail: COMPLETE")
        logging.info("  - Ready for downstream processing: YES")

        logging.info("🏆 TRANSFORMATION PIPELINE COMPLETED SUCCESSFULLY")
        logging.info("=== TRANSFORMATION COMPLETION END ===")
        logging.info("=" * 60)
        # GREEN BOX LOGGING END

        return True

    except Exception as e:
        logging.error(f"❌ Transformation completion logging failed: {str(e)}")
        raise

def run_processing_pipeline():
    """
    Execute complete CSV processing pipeline
    
    Processing Steps (Sequential):
    1. Process CSV - Read and validate CSV structure
    2. File Consolidation Logic (GREEN BOX) - Single file vs. multi-file decision
    3. Add Surrogate Key - Insert as FIRST column (leftmost position)
    4. Raw CSV Enhanced (GREEN BOX) - Log successful enhancement
    5. Append Audit Columns - Add 3 columns to rightmost positions
    6. Transformation Completion (GREEN BOX) - Log "Landed Data to Bronze Completed"
    
    Returns:
        dict: Processing results and statistics
    """
    
    try:
        logging.info("🚀 Starting CSV Processing Pipeline...")
        logging.info("=" * 80)
        
        pipeline_start_time = datetime.now()
        processing_stats = {
            'pipeline_start_time': pipeline_start_time,
            'source_container': SOURCE_CONTAINER,
            'output_container': get_output_container(),
            'file_patterns': FILE_PATTERNS
        }
        
        # Step 1: Process CSV - List and validate available files
        logging.info("STEP 1: PROCESS CSV - Discovering available files")
        logging.info("-" * 50)
        
        csv_files = list_csv_files_in_container(SOURCE_CONTAINER, FILE_PATTERNS, job_key=None)
        
        if not csv_files:
            raise ValueError(f"No CSV files found in container '{SOURCE_CONTAINER}' matching patterns {FILE_PATTERNS}")
        
        processing_stats['files_found'] = len(csv_files)
        processing_stats['file_list'] = csv_files
        
        logging.info(f"✓ Step 1 completed: Found {len(csv_files)} CSV files")
        
        # Step 2: File Consolidation Logic (GREEN BOX)
        logging.info("STEP 2: FILE CONSOLIDATION LOGIC")
        logging.info("-" * 50)
        
        consolidated_df, successfully_processed_files = consolidate_files(csv_files, SOURCE_CONTAINER)

        # Polars doesn't have .attrs - get metadata from processing_stats or default values
        processing_stats['consolidation_method'] = 'multi_file_concat_aligned'
        processing_stats['source_files'] = csv_files
        processing_stats['successfully_processed_files'] = successfully_processed_files
        processing_stats['original_shape'] = consolidated_df.shape
        
        logging.info(f"✓ Step 2 completed: Consolidated to shape {consolidated_df.shape}")
        
        # Step 3: Validate and clean the consolidated CSV
        logging.info("STEP 3: CSV VALIDATION AND CLEANING")
        logging.info("-" * 50)
        
        # Clean column names
        consolidated_df = clean_column_names(consolidated_df)
        
        # Validate structure
        validate_csv_structure(consolidated_df, "consolidated_data")
        
        # Preview the data
        preview_dataframe(consolidated_df, "consolidated_data", max_rows=3)
        
        logging.info("✓ Step 3 completed: CSV validation and cleaning")
        
        # Step 4: Add Surrogate Key (FIRST column - leftmost position)
        logging.info("STEP 4: ADD SURROGATE KEY")
        logging.info("-" * 50)
        
        df_with_surrogate = add_surrogate_key(consolidated_df)
        
        processing_stats['surrogate_key_added'] = True
        processing_stats['shape_after_surrogate'] = df_with_surrogate.shape
        
        logging.info(f"✓ Step 4 completed: Added surrogate key, new shape {df_with_surrogate.shape}")
        
        # Step 5: Raw CSV Enhanced (GREEN BOX)
        logging.info("STEP 5: RAW CSV ENHANCED VALIDATION")
        logging.info("-" * 50)
        
        log_raw_csv_enhanced(df_with_surrogate)
        
        logging.info("✓ Step 5 completed: Raw CSV enhanced validation")
        
        # Step 6: Append Audit Columns (rightmost positions)
        logging.info("STEP 6: APPEND AUDIT COLUMNS")
        logging.info("-" * 50)
        
        # Polars doesn't have .attrs - get source files from processing_stats
        source_filenames = processing_stats.get('source_files', [])
        final_df = append_audit_columns(df_with_surrogate, source_filenames)
        
        processing_stats['audit_columns_added'] = True
        processing_stats['final_shape'] = final_df.shape
        
        logging.info(f"✓ Step 6 completed: Added audit columns, final shape {final_df.shape}")
        
        # Step 7: Upload processed file to destination as both Parquet and CSV
        logging.info("STEP 7: UPLOAD PROCESSED FILES (PARQUET + CSV)")
        logging.info("-" * 50)

        # Generate output filenames based on number of files

        if len(successfully_processed_files) == 1:
            # Single file: use original filename
            csv_basename = os.path.basename(successfully_processed_files[0])
            # Handle both .csv and .CSV extensions (case-insensitive)
            if csv_basename.lower().endswith('.csv'):
                base_name = csv_basename[:-4]  # Remove last 4 characters (.csv or .CSV)
            else:
                base_name = csv_basename
            parquet_filename = f"{base_name}.parquet"
            csv_filename = f"{base_name}.csv"
            logging.info(f"📄 [run_csv_processing_pipeline] Single file detected: Using original filename '{base_name}'")
        else:
            # Multiple files: use combined naming
            today_date = datetime.now().strftime("%Y-%m-%d")
            parquet_filename = f"combined_sales_data_{today_date}.parquet"
            csv_filename = f"combined_sales_data_{today_date}.csv"
            logging.info(f"📄 [run_csv_processing_pipeline] Multiple files detected ({len(successfully_processed_files)}): Using combined filename 'combined_sales_data_{today_date}'")

        # Upload Parquet file (primary format)
        parquet_success = upload_parquet_to_blob(final_df, parquet_filename, get_output_container())

        # Upload CSV file (for readability)
        csv_success = upload_csv_to_blob(final_df, csv_filename, get_output_container())

        processing_stats['parquet_filename'] = parquet_filename
        processing_stats['csv_filename'] = csv_filename
        processing_stats['parquet_upload_success'] = parquet_success
        processing_stats['csv_upload_success'] = csv_success
        processing_stats['output_format'] = 'Parquet + CSV'

        # Consider overall upload successful if at least one format succeeds
        upload_success = parquet_success or csv_success
        processing_stats['upload_success'] = upload_success

        if upload_success:
            if parquet_success and csv_success:
                logging.info(f"✓ Step 7 completed: Uploaded both {parquet_filename} and {csv_filename}")
            elif parquet_success:
                logging.info(f"✓ Step 7 partially completed: Uploaded {parquet_filename} (CSV failed)")
            else:
                logging.info(f"✓ Step 7 partially completed: Uploaded {csv_filename} (Parquet failed)")
        else:
            logging.error("❌ Step 7 failed: Both uploads unsuccessful")
        
        # Step 8: Transformation Completion (GREEN BOX)
        logging.info("STEP 8: TRANSFORMATION COMPLETION")
        logging.info("-" * 50)
        
        pipeline_end_time = datetime.now()
        processing_stats['pipeline_end_time'] = pipeline_end_time
        processing_stats['total_processing_time'] = str(pipeline_end_time - pipeline_start_time)
        
        log_transformation_completion(final_df, processing_stats)
        
        logging.info("✓ Step 8 completed: Transformation completion logged")
        
        # Final pipeline summary
        logging.info("=" * 80)
        logging.info("🎉 CSV PROCESSING PIPELINE COMPLETED SUCCESSFULLY")
        logging.info("=" * 80)
        
        # Convert datetime objects to ISO format strings for JSON serialization
        serializable_stats = {}
        for key, value in processing_stats.items():
            if isinstance(value, datetime):
                serializable_stats[key] = value.isoformat()
            else:
                serializable_stats[key] = value

        # Clean up .placeholder files after successful processing
        logging.info("CLEANUP: Removing .placeholder files...")
        cleanup_count = cleanup_placeholder_files(SOURCE_CONTAINER)

        # Clean up successfully processed source CSV files after successful processing
        source_cleanup_results = cleanup_source_csv_files(SOURCE_CONTAINER, successfully_processed_files)

        # Move zip files to archive if enabled - determine job key from processed files (dealer-aware)
        zip_movement_results = None
        job_key = _determine_job_key_from_files(csv_files)
        if job_key:
            logging.info(f"ZIP ARCHIVE MOVEMENT: Starting dealer-aware zip file movement for job '{job_key}'...")
            zip_movement_results = move_zip_files_to_archive_dealer_aware(SOURCE_CONTAINER, job_key)
            if zip_movement_results.get('status') == 'success':
                logging.info(f"ZIP ARCHIVE MOVEMENT: Completed - {zip_movement_results.get('successfully_moved', 0)} files moved")
            elif zip_movement_results.get('status') == 'disabled':
                logging.info(f"ZIP ARCHIVE MOVEMENT: Disabled in configuration for job '{job_key}'")
            elif zip_movement_results.get('status') == 'no_dealers':
                logging.info(f"ZIP ARCHIVE MOVEMENT: No dealer folders found for job '{job_key}'")
            else:
                logging.warning(f"ZIP ARCHIVE MOVEMENT: Unexpected status '{zip_movement_results.get('status')}' for job '{job_key}'")
        else:
            logging.info("ZIP ARCHIVE MOVEMENT: Could not determine job key from processed files")

        result = {
            "status": "success",
            "processing_stats": serializable_stats,
            "final_shape": final_df.shape,
            "parquet_file": parquet_filename,
            "csv_file": csv_filename,
            "output_format": "Parquet + CSV",
            "total_time": str(pipeline_end_time - pipeline_start_time),
            "cleanup_placeholder_files": cleanup_count,
            "source_file_cleanup": source_cleanup_results,
            "zip_movement": zip_movement_results
        }

        logging.info(f"Pipeline result: {result}")
        return result
        
    except Exception as e:
        logging.error(f"❌ CSV Processing Pipeline failed: {str(e)}")

        # Convert datetime objects to ISO format strings for JSON serialization
        serializable_stats = {}
        if 'processing_stats' in locals():
            for key, value in processing_stats.items():
                if isinstance(value, datetime):
                    serializable_stats[key] = value.isoformat()
                else:
                    serializable_stats[key] = value

        error_result = {
            "status": "error",
            "error_message": str(e),
            "processing_stats": serializable_stats
        }

        return error_result

def run_csv_to_parquet_pipeline():
    """
    Hybrid pipeline: Read CSV files, process with Polars, output Parquet

    Processing Steps:
    1. Download CSV files and convert to Polars DataFrames
    2. Consolidate multiple files if needed
    3. Add surrogate key using Polars
    4. Add audit columns with intelligent datetime extraction
    5. Upload as Parquet file for optimized storage

    Returns:
        dict: Processing results and statistics
    """

    try:
        logging.info("🚀 Starting CSV-to-Parquet Hybrid Pipeline...")
        logging.info("=" * 80)

        pipeline_start_time = datetime.now()
        processing_stats = {
            'pipeline_start_time': pipeline_start_time.isoformat(),
            'source_container': SOURCE_CONTAINER,
            'pipeline_type': 'csv_to_parquet_hybrid'
        }

        # Step 1: Get CSV files and convert to DataFrames
        logging.info("STEP 1: CSV FILE PROCESSING")
        logging.info("-" * 50)

        csv_files = list_csv_files_in_container(SOURCE_CONTAINER, FILE_PATTERNS, job_key=None)
        if not csv_files:
            raise ValueError(f"No CSV files found in container '{SOURCE_CONTAINER}'")

        logging.info(f"Found {len(csv_files)} CSV files to process")

        # Download and consolidate CSV files using existing logic
        consolidated_df, successfully_processed_files = consolidate_files(csv_files, SOURCE_CONTAINER)
        processing_stats['source_files'] = csv_files
        processing_stats['successfully_processed_files'] = successfully_processed_files
        processing_stats['original_shape'] = consolidated_df.shape

        # Step 2: Add Surrogate Key using Polars (Configurable)
        logging.info("STEP 2: ADD SURROGATE KEY (POLARS)")
        logging.info("-" * 50)

        job_key = _determine_job_key_from_files(csv_files)
        df_with_surrogate = add_surrogate_key_configurable(consolidated_df, job_key)
        processing_stats['surrogate_key_added'] = True
        processing_stats['shape_after_surrogate'] = df_with_surrogate.shape

        # Step 3: Add Audit Columns with intelligent datetime extraction
        logging.info("STEP 3: ADD AUDIT COLUMNS (POLARS)")
        logging.info("-" * 50)

        final_df = append_audit_columns_polars(df_with_surrogate, csv_files)
        processing_stats['audit_columns_added'] = True
        processing_stats['final_shape'] = final_df.shape

        # Step 4: Upload as both Parquet and CSV files
        logging.info("STEP 4: UPLOAD AS PARQUET AND CSV FILES")
        logging.info("-" * 50)

        # Generate output filenames based on number of files

        if len(csv_files) == 1:
            # Single file: use original filename
            csv_basename = os.path.basename(csv_files[0])
            # Handle both .csv and .CSV extensions (case-insensitive)
            if csv_basename.lower().endswith('.csv'):
                base_name = csv_basename[:-4]  # Remove last 4 characters (.csv or .CSV)
            else:
                base_name = csv_basename
            parquet_filename = f"{base_name}.parquet"
            csv_filename = f"{base_name}.csv"
            logging.info(f"📄 [run_csv_to_parquet_pipeline] Single file detected: Using original filename '{base_name}'")
        else:
            # Multiple files: use combined naming
            today_date = datetime.now().strftime("%Y-%m-%d")
            parquet_filename = f"combined_sales_data_{today_date}.parquet"
            csv_filename = f"combined_sales_data_{today_date}.csv"
            logging.info(f"📄 [run_csv_to_parquet_pipeline] Multiple files detected ({len(csv_files)}): Using combined filename 'combined_sales_data_{today_date}'")

        # Upload Parquet file (primary format)
        parquet_success = upload_parquet_to_blob(final_df, parquet_filename, get_output_container())

        # Upload CSV file (for readability)
        csv_success = upload_csv_to_blob(final_df, csv_filename, get_output_container())

        processing_stats['parquet_filename'] = parquet_filename
        processing_stats['csv_filename'] = csv_filename
        processing_stats['parquet_upload_success'] = parquet_success
        processing_stats['csv_upload_success'] = csv_success
        processing_stats['output_format'] = 'Parquet + CSV'

        # Consider overall upload successful if at least one format succeeds
        upload_success = parquet_success or csv_success
        processing_stats['upload_success'] = upload_success

        pipeline_end_time = datetime.now()
        processing_duration = (pipeline_end_time - pipeline_start_time).total_seconds()

        processing_stats['pipeline_end_time'] = pipeline_end_time.isoformat()
        processing_stats['processing_duration_seconds'] = processing_duration

        if upload_success:
            logging.info("🎉 CSV-to-Parquet Pipeline completed successfully!")
            if parquet_success and csv_success:
                logging.info(f"📊 Final outputs: {parquet_filename} and {csv_filename} ({final_df.shape[0]} rows, {final_df.shape[1]} columns)")
            elif parquet_success:
                logging.info(f"📊 Final output: {parquet_filename} ({final_df.shape[0]} rows, {final_df.shape[1]} columns) - CSV failed")
            else:
                logging.info(f"📊 Final output: {csv_filename} ({final_df.shape[0]} rows, {final_df.shape[1]} columns) - Parquet failed")
            logging.info(f"⏱️ Total processing time: {processing_duration:.2f} seconds")

            # Clean up .placeholder files after successful processing
            logging.info("CLEANUP: Removing .placeholder files...")
            cleanup_count = cleanup_placeholder_files(SOURCE_CONTAINER)

            # Clean up successfully processed source CSV files after successful processing
            source_cleanup_results = cleanup_source_csv_files(SOURCE_CONTAINER, successfully_processed_files)

            # Move zip files to archive if enabled - determine job key from processed files (dealer-aware)
            zip_movement_results = None
            job_key = _determine_job_key_from_files(csv_files)
            if job_key:
                logging.info(f"ZIP ARCHIVE MOVEMENT: Starting dealer-aware zip file movement for job '{job_key}'...")
                zip_movement_results = move_zip_files_to_archive_dealer_aware(SOURCE_CONTAINER, job_key)
                if zip_movement_results.get('status') == 'success':
                    logging.info(f"ZIP ARCHIVE MOVEMENT: Completed - {zip_movement_results.get('successfully_moved', 0)} files moved")
                elif zip_movement_results.get('status') == 'disabled':
                    logging.info(f"ZIP ARCHIVE MOVEMENT: Disabled in configuration for job '{job_key}'")
                elif zip_movement_results.get('status') == 'no_dealers':
                    logging.info(f"ZIP ARCHIVE MOVEMENT: No dealer folders found for job '{job_key}'")
                else:
                    logging.warning(f"ZIP ARCHIVE MOVEMENT: Unexpected status '{zip_movement_results.get('status')}' for job '{job_key}'")
            else:
                logging.info("ZIP ARCHIVE MOVEMENT: Could not determine job key from processed files")

            return {
                "status": "success",
                "message": "CSV-to-Parquet pipeline completed successfully",
                "parquet_file": parquet_filename,
                "csv_file": csv_filename,
                "output_format": "Parquet + CSV",
                "processing_stats": processing_stats,
                "cleanup_placeholder_files": cleanup_count,
                "source_file_cleanup": source_cleanup_results,
                "zip_movement": zip_movement_results
            }
        else:
            raise Exception("Failed to upload Parquet file to blob storage")

    except Exception as e:
        logging.error(f"❌ CSV-to-Parquet Pipeline failed: {str(e)}")

        error_result = {
            "status": "error",
            "error_message": str(e),
            "processing_stats": processing_stats
        }

        return error_result

def run_parquet_processing_pipeline():
    """
    Execute complete Parquet processing pipeline using Polars

    Processing Steps (Sequential):
    1. Process Parquet - Read and validate Parquet structure
    2. File Consolidation Logic (GREEN BOX) - Single file vs. multi-file decision with lazy evaluation
    3. Add Surrogate Key - Insert as FIRST column (leftmost position) using Polars
    4. Raw CSV Enhanced (GREEN BOX) - Log successful enhancement
    5. Append Audit Columns - Add 3 columns to rightmost positions using Polars
    6. Transformation Completion (GREEN BOX) - Log "Landed Data to Bronze Completed"

    Returns:
        dict: Processing results and statistics
    """

    try:
        logging.info("🚀 Starting Parquet Processing Pipeline (Polars)...")
        logging.info("=" * 80)

        pipeline_start_time = datetime.now()
        processing_stats = {
            'pipeline_start_time': pipeline_start_time,
            'source_container': SOURCE_CONTAINER,
            'output_container': get_output_container(),
            'file_patterns': FILE_PATTERNS,
            'processing_engine': 'Polars',
            'file_format': 'Parquet'
        }

        # Step 1: Process Parquet - List and validate available files
        logging.info("STEP 1: PROCESS PARQUET - Discovering available files")
        logging.info("-" * 50)

        parquet_files = list_parquet_files_in_container(SOURCE_CONTAINER, FILE_PATTERNS)

        if not parquet_files:
            raise ValueError(f"No Parquet files found in container '{SOURCE_CONTAINER}' matching patterns {FILE_PATTERNS}")

        processing_stats['files_found'] = len(parquet_files)
        processing_stats['file_list'] = parquet_files

        logging.info(f"✓ Step 1 completed: Found {len(parquet_files)} Parquet files")

        # Step 2: File Consolidation Logic (GREEN BOX) with Lazy Evaluation
        logging.info("STEP 2: FILE CONSOLIDATION LOGIC (POLARS LAZY)")
        logging.info("-" * 50)

        # Use memory-efficient consolidation for multiple file types
        if len(parquet_files) > 3:
            logging.info("🔄 Using memory-efficient multi-type consolidation for large dataset")
            consolidated_df = consolidate_multiple_file_types_memory_efficient(parquet_files, SOURCE_CONTAINER)
        else:
            logging.info("🔄 Using standard consolidation for small dataset")
            consolidated_df = consolidate_parquet_files_lazy(parquet_files, SOURCE_CONTAINER)

        processing_stats['consolidation_method'] = 'polars_lazy_evaluation'
        processing_stats['source_files'] = parquet_files
        processing_stats['original_shape'] = consolidated_df.shape

        logging.info(f"✓ Step 2 completed: Consolidated to shape {consolidated_df.shape}")

        # Step 3: Add Surrogate Key (FIRST column - leftmost position) using Polars (Configurable)
        logging.info("STEP 3: ADD SURROGATE KEY (POLARS)")
        logging.info("-" * 50)

        job_key = _determine_job_key_from_files(csv_files)
        df_with_surrogate = add_surrogate_key_configurable(consolidated_df, job_key)

        processing_stats['surrogate_key_added'] = True
        processing_stats['shape_after_surrogate'] = df_with_surrogate.shape

        logging.info(f"✓ Step 3 completed: Added surrogate key, new shape {df_with_surrogate.shape}")

        # Step 4: Raw CSV Enhanced (GREEN BOX) - Works with Polars DataFrames
        logging.info("STEP 4: RAW CSV ENHANCED VALIDATION (POLARS)")
        logging.info("-" * 50)

        log_raw_csv_enhanced(df_with_surrogate)

        logging.info("✓ Step 4 completed: Raw CSV enhanced validation")

        # Step 5: Append Audit Columns (rightmost positions) using Polars
        logging.info("STEP 5: APPEND AUDIT COLUMNS (POLARS)")
        logging.info("-" * 50)

        final_df = append_audit_columns_polars(df_with_surrogate, parquet_files)

        processing_stats['audit_columns_added'] = True
        processing_stats['final_shape'] = final_df.shape

        logging.info(f"✓ Step 5 completed: Added audit columns, final shape {final_df.shape}")

        # Step 6: Upload processed file to destination as both Parquet and CSV
        logging.info("STEP 6: UPLOAD PROCESSED FILES (PARQUET + CSV)")
        logging.info("-" * 50)

        # Generate output filenames based on number of files
        if len(parquet_files) == 1:
            # Single file: use original filename (convert from parquet back to base name)
            parquet_basename = os.path.basename(parquet_files[0])
            base_name = parquet_basename.replace('.parquet', '')
            parquet_filename = f"{base_name}.parquet"
            csv_filename = f"{base_name}.csv"
            logging.info(f"📄 Single file detected: Using original filename '{base_name}'")
        else:
            # Multiple files: use combined naming
            today_date = datetime.now().strftime("%Y-%m-%d")
            parquet_filename = f"combined_sales_data_{today_date}.parquet"
            csv_filename = f"combined_sales_data_{today_date}.csv"
            logging.info(f"📄 Multiple files detected ({len(parquet_files)}): Using combined filename 'combined_sales_data_{today_date}'")

        # Upload Parquet file (primary format)
        parquet_success = upload_parquet_to_blob(final_df, parquet_filename, get_output_container())

        # Upload CSV file (for readability)
        csv_success = upload_csv_to_blob(final_df, csv_filename, get_output_container())

        processing_stats['parquet_filename'] = parquet_filename
        processing_stats['csv_filename'] = csv_filename
        processing_stats['parquet_upload_success'] = parquet_success
        processing_stats['csv_upload_success'] = csv_success
        processing_stats['output_format'] = 'Parquet + CSV'

        # Consider overall upload successful if at least one format succeeds
        upload_success = parquet_success or csv_success
        processing_stats['upload_success'] = upload_success

        if upload_success:
            if parquet_success and csv_success:
                logging.info(f"✓ Step 6 completed: Uploaded both {parquet_filename} and {csv_filename}")
            elif parquet_success:
                logging.info(f"✓ Step 6 partially completed: Uploaded {parquet_filename} (CSV failed)")
            else:
                logging.info(f"✓ Step 6 partially completed: Uploaded {csv_filename} (Parquet failed)")
        else:
            logging.error("❌ Step 6 failed: Both uploads unsuccessful")

        # Step 7: Transformation Completion (GREEN BOX)
        logging.info("STEP 7: TRANSFORMATION COMPLETION (POLARS)")
        logging.info("-" * 50)

        pipeline_end_time = datetime.now()
        processing_stats['pipeline_end_time'] = pipeline_end_time
        processing_stats['total_processing_time'] = str(pipeline_end_time - pipeline_start_time)

        log_transformation_completion_polars(final_df, processing_stats)

        logging.info("✓ Step 7 completed: Transformation completion logged")

        # Final pipeline summary
        logging.info("=" * 80)
        logging.info("🎉 PARQUET PROCESSING PIPELINE COMPLETED SUCCESSFULLY")
        logging.info("=" * 80)

        # Convert datetime objects to ISO format strings for JSON serialization
        serializable_stats = {}
        for key, value in processing_stats.items():
            if isinstance(value, datetime):
                serializable_stats[key] = value.isoformat()
            else:
                serializable_stats[key] = value

        # Clean up .placeholder files after successful processing
        logging.info("CLEANUP: Removing .placeholder files...")
        cleanup_count = cleanup_placeholder_files(SOURCE_CONTAINER)

        # Move zip files to archive if enabled - determine job key from processed files (dealer-aware)
        zip_movement_results = None
        job_key = _determine_job_key_from_files(csv_files)
        if job_key:
            logging.info(f"ZIP ARCHIVE MOVEMENT: Starting dealer-aware zip file movement for job '{job_key}'...")
            zip_movement_results = move_zip_files_to_archive_dealer_aware(SOURCE_CONTAINER, job_key)
            if zip_movement_results.get('status') == 'success':
                logging.info(f"ZIP ARCHIVE MOVEMENT: Completed - {zip_movement_results.get('successfully_moved', 0)} files moved")
            elif zip_movement_results.get('status') == 'disabled':
                logging.info(f"ZIP ARCHIVE MOVEMENT: Disabled in configuration for job '{job_key}'")
            elif zip_movement_results.get('status') == 'no_dealers':
                logging.info(f"ZIP ARCHIVE MOVEMENT: No dealer folders found for job '{job_key}'")
            else:
                logging.warning(f"ZIP ARCHIVE MOVEMENT: Unexpected status '{zip_movement_results.get('status')}' for job '{job_key}'")
        else:
            logging.info("ZIP ARCHIVE MOVEMENT: Could not determine job key from processed files")

        result = {
            "status": "success",
            "processing_stats": serializable_stats,
            "final_shape": final_df.shape,
            "parquet_file": parquet_filename,
            "csv_file": csv_filename,
            "output_format": "Parquet + CSV",
            "total_time": str(pipeline_end_time - pipeline_start_time),
            "processing_engine": "Polars",
            "file_format": "Parquet + CSV",
            "cleanup_placeholder_files": cleanup_count,
            "zip_movement": zip_movement_results
        }

        logging.info(f"Pipeline result: {result}")
        return result

    except Exception as e:
        logging.error(f"❌ Parquet Processing Pipeline failed: {str(e)}")

        # Convert datetime objects to ISO format strings for JSON serialization
        serializable_stats = {}
        if 'processing_stats' in locals():
            for key, value in processing_stats.items():
                if isinstance(value, datetime):
                    serializable_stats[key] = value.isoformat()
                else:
                    serializable_stats[key] = value

        error_result = {
            "status": "error",
            "error_message": str(e),
            "processing_stats": serializable_stats,
            "processing_engine": "Polars",
            "file_format": "Parquet"
        }

        return error_result


class CSVProcessingOrchestrator:
    """Enhanced wrapper class for dynamic job processing based on Azure Table Storage configuration"""

    def __init__(self, config):
        self.config = config

    def process_pipeline(self, container_name=None, input_folder='input', output_folder='output', file_format='auto'):
        """
        Process all enabled jobs dynamically from Azure Table Storage configuration

        This method now processes jobs based on their enabled status in the sftpConfig table,
        using job-specific paths, patterns, and surrogate key configurations.

        Args:
            container_name: Container name (ignored, uses job-specific config)
            input_folder: Input folder (ignored, uses job-specific config)
            output_folder: Output folder (ignored, uses job-specific config)
            file_format: File format (ignored, auto-detects per job)

        Returns:
            dict: Processing results for all enabled jobs
        """
        logging.info("🚀 Starting dynamic job processing based on Azure Table Storage configuration")

        # Use the new dynamic job processing
        return process_enabled_jobs()

    def process_parquet_pipeline(self, container_name=None, input_folder='input', output_folder='output'):
        """
        Explicitly process using the Parquet pipeline

        Args:
            container_name: Container name (currently not used, uses config defaults)
            input_folder: Input folder (currently not used)
            output_folder: Output folder (currently not used)

        Returns:
            dict: Processing results
        """
        logging.info("🚀 Explicitly using Parquet processing pipeline")
        return run_parquet_processing_pipeline()

    def process_csv_pipeline(self, container_name=None, input_folder='input', output_folder='output'):
        """
        Explicitly process using the CSV pipeline (legacy)

        Args:
            container_name: Container name (currently not used, uses config defaults)
            input_folder: Input folder (currently not used)
            output_folder: Output folder (currently not used)

        Returns:
            dict: Processing results
        """
        logging.info("🚀 Explicitly using CSV processing pipeline (legacy)")
        return run_processing_pipeline()
