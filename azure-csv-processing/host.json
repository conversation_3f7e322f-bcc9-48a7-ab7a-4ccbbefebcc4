{"version": "2.0", "functionTimeout": "00:10:00", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}, "logLevel": {"default": "Warning", "Function": "Information", "Host.Results": "Warning", "azure": "Warning", "azure.core.pipeline.policies.http_logging_policy": "Warning", "azure.identity": "Warning", "azure.storage": "Warning"}}, "extensions": {"http": {"routePrefix": "api", "cors": {"allowedOrigins": ["https://portal.azure.com", "https://ms.portal.azure.com"], "supportCredentials": false}}}}