{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"applicationInsightsName": {"type": "string", "defaultValue": "csv-processing-insights", "metadata": {"description": "Name of Application Insights instance"}}, "functionAppName": {"type": "string", "defaultValue": "csv-processing-func-3514", "metadata": {"description": "Function App name to monitor"}}, "logAnalyticsWorkspaceName": {"type": "string", "defaultValue": "swickard-log-analytics", "metadata": {"description": "Log Analytics workspace name"}}, "alertEmailAddress": {"type": "string", "defaultValue": "<EMAIL>", "metadata": {"description": "Email address for alerts"}}}, "variables": {"location": "[resourceGroup().location]"}, "resources": [{"type": "Microsoft.OperationalInsights/workspaces", "apiVersion": "2021-06-01", "name": "[parameters('logAnalyticsWorkspaceName')]", "location": "[variables('location')]", "properties": {"sku": {"name": "PerGB2018"}, "retentionInDays": 90, "features": {"enableLogAccessUsingOnlyResourcePermissions": true}}}, {"type": "Microsoft.Insights/components", "apiVersion": "2020-02-02", "name": "[parameters('applicationInsightsName')]", "location": "[variables('location')]", "dependsOn": ["[resourceId('Microsoft.OperationalInsights/workspaces', parameters('logAnalyticsWorkspaceName'))]"], "kind": "web", "properties": {"Application_Type": "web", "WorkspaceResourceId": "[resourceId('Microsoft.OperationalInsights/workspaces', parameters('logAnalyticsWorkspaceName'))]", "IngestionMode": "LogAnalytics", "publicNetworkAccessForIngestion": "Enabled", "publicNetworkAccessForQuery": "Enabled"}}, {"type": "Microsoft.Insights/actionGroups", "apiVersion": "2021-09-01", "name": "csv-processing-alerts", "location": "Global", "properties": {"groupShortName": "CSVAlerts", "enabled": true, "emailReceivers": [{"name": "AdminEmail", "emailAddress": "[parameters('alertEmailAddress')]", "useCommonAlertSchema": true}], "webhookReceivers": []}}, {"type": "Microsoft.Insights/metricAlerts", "apiVersion": "2018-03-01", "name": "csv-processing-failure-alert", "location": "Global", "dependsOn": ["[resourceId('Microsoft.Insights/components', parameters('applicationInsightsName'))]", "[resourceId('Microsoft.Insights/actionGroups', 'csv-processing-alerts')]"], "properties": {"description": "Alert when CSV processing function fails", "severity": 2, "enabled": true, "scopes": ["[resourceId('Microsoft.Web/sites', parameters('functionAppName'))]"], "evaluationFrequency": "PT5M", "windowSize": "PT15M", "criteria": {"odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria", "allOf": [{"name": "FunctionErrors", "metricName": "FunctionExecutionCount", "dimensions": [{"name": "Status", "operator": "Include", "values": ["Failed"]}], "operator": "GreaterThan", "threshold": 0, "timeAggregation": "Total"}]}, "actions": [{"actionGroupId": "[resourceId('Microsoft.Insights/actionGroups', 'csv-processing-alerts')]"}]}}, {"type": "Microsoft.Insights/metricAlerts", "apiVersion": "2018-03-01", "name": "csv-processing-duration-alert", "location": "Global", "dependsOn": ["[resourceId('Microsoft.Insights/components', parameters('applicationInsightsName'))]", "[resourceId('Microsoft.Insights/actionGroups', 'csv-processing-alerts')]"], "properties": {"description": "Alert when CSV processing takes too long", "severity": 3, "enabled": true, "scopes": ["[resourceId('Microsoft.Web/sites', parameters('functionAppName'))]"], "evaluationFrequency": "PT5M", "windowSize": "PT15M", "criteria": {"odata.type": "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria", "allOf": [{"name": "FunctionDuration", "metricName": "FunctionExecutionUnits", "operator": "GreaterThan", "threshold": 300000, "timeAggregation": "Average"}]}, "actions": [{"actionGroupId": "[resourceId('Microsoft.Insights/actionGroups', 'csv-processing-alerts')]"}]}}, {"type": "Microsoft.Insights/scheduledQueryRules", "apiVersion": "2021-08-01", "name": "csv-processing-error-pattern-alert", "location": "[variables('location')]", "dependsOn": ["[resourceId('Microsoft.Insights/components', parameters('applicationInsightsName'))]", "[resourceId('Microsoft.Insights/actionGroups', 'csv-processing-alerts')]"], "properties": {"displayName": "CSV Processing Error Pattern Alert", "description": "Alert on specific error patterns in CSV processing", "severity": 2, "enabled": true, "evaluationFrequency": "PT5M", "windowSize": "PT15M", "scopes": ["[resourceId('Microsoft.Insights/components', parameters('applicationInsightsName'))]"], "criteria": {"allOf": [{"query": "traces | where message contains \"❌\" or message contains \"CRITICAL\" or message contains \"FAILED\" | where timestamp > ago(15m) | summarize count() by bin(timestamp, 5m)", "timeAggregation": "Total", "operator": "GreaterThan", "threshold": 3, "failingPeriods": {"numberOfEvaluationPeriods": 1, "minFailingPeriodsToAlert": 1}}]}, "actions": {"actionGroups": ["[resourceId('Microsoft.Insights/actionGroups', 'csv-processing-alerts')]"]}}}], "outputs": {"applicationInsightsInstrumentationKey": {"type": "string", "value": "[reference(resourceId('Microsoft.Insights/components', parameters('applicationInsightsName'))).InstrumentationKey]"}, "applicationInsightsConnectionString": {"type": "string", "value": "[reference(resourceId('Microsoft.Insights/components', parameters('applicationInsightsName'))).ConnectionString]"}, "logAnalyticsWorkspaceId": {"type": "string", "value": "[reference(resourceId('Microsoft.OperationalInsights/workspaces', parameters('logAnalyticsWorkspaceName'))).customerId]"}}}