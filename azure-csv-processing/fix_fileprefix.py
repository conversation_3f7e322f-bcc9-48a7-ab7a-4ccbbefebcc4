#!/usr/bin/env python3
"""
Script to fix the FilePrefix in sftpConfig table for CUSTOMER-ACCTG_ job
"""
import os
import sys

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(current_dir, 'shared')
if shared_dir not in sys.path:
    sys.path.append(shared_dir)

try:
    from azure.data.tables import TableServiceClient
    from azure.identity import DefaultAzureCredential
    AZURE_MODULES_AVAILABLE = True
except ImportError:
    AZURE_MODULES_AVAILABLE = False
    print("❌ Azure modules not available")
    sys.exit(1)

def main():
    print("🔧 Fixing FilePrefix for CUSTOMER-ACCTG_ job...")
    
    if not AZURE_MODULES_AVAILABLE:
        print("❌ Azure modules not available")
        return
    
    try:
        # Connect to Azure Table Storage
        storage_account_name = "sagedw"
        table_storage_url = f"https://{storage_account_name}.table.core.windows.net"
        credential = DefaultAzureCredential()
        table_service_client = TableServiceClient(endpoint=table_storage_url, credential=credential)
        table_client = table_service_client.get_table_client(table_name="sftpConfig")
        
        # Get the current entity
        entity = table_client.get_entity(partition_key="Reynolds", row_key="CUSTOMER-ACCTG_")
        
        print(f"📋 Current FilePrefix: '{entity.get('FilePrefix', 'NOT_SET')}'")
        
        # Update the FilePrefix
        old_prefix = entity.get('FilePrefix', '')
        new_prefix = 'EDW_CUSTOMER_ACCTG'  # Changed from EDW-CUSTOMER-ACCTG_ to EDW_CUSTOMER_ACCTG
        
        print(f"🔄 Updating FilePrefix from '{old_prefix}' to '{new_prefix}'")
        
        # Update the entity
        entity['FilePrefix'] = new_prefix
        table_client.update_entity(mode="merge", entity=entity)
        
        print(f"✅ Successfully updated FilePrefix to '{new_prefix}'")
        
        # Verify the update
        updated_entity = table_client.get_entity(partition_key="Reynolds", row_key="CUSTOMER-ACCTG_")
        print(f"🔍 Verified FilePrefix is now: '{updated_entity.get('FilePrefix', 'NOT_SET')}'")
        
    except Exception as e:
        print(f"❌ Error updating FilePrefix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
