#!/usr/bin/env python3
"""
Script to check for file prefix mismatches between azure-ftp-etl and azure-csv-processing
"""
import os
import sys

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(current_dir, 'shared')
if shared_dir not in sys.path:
    sys.path.append(shared_dir)

try:
    from azure.data.tables import TableServiceClient
    from azure.identity import DefaultAzureCredential
    AZURE_MODULES_AVAILABLE = True
except ImportError:
    AZURE_MODULES_AVAILABLE = False
    print("❌ Azure modules not available")
    sys.exit(1)

def main():
    print("🔍 Checking for file prefix mismatches...")
    
    if not AZURE_MODULES_AVAILABLE:
        print("❌ Azure modules not available")
        return
    
    try:
        # Connect to Azure Table Storage
        storage_account_name = "sagedw"
        table_storage_url = f"https://{storage_account_name}.table.core.windows.net"
        credential = DefaultAzureCredential()
        table_service_client = TableServiceClient(endpoint=table_storage_url, credential=credential)
        table_client = table_service_client.get_table_client(table_name="sftpConfig")
        
        print("📋 Checking Reynolds entities for file prefix consistency...")
        
        # Get all Reynolds entities
        reynolds_entities = table_client.query_entities(query_filter="PartitionKey eq 'Reynolds'")
        
        for entity in reynolds_entities:
            row_key = entity['RowKey']
            file_prefix = entity.get('FilePrefix', 'NOT_SET')
            folder = entity.get('Folder', 'NOT_SET')
            enabled = entity.get('isEnabled', False)
            
            print(f"\n🔍 Entity: {row_key}")
            print(f"   FilePrefix: '{file_prefix}'")
            print(f"   Folder: '{folder}'")
            print(f"   Enabled: {enabled}")
            
            # Check for potential mismatches based on azure-ftp-etl logs
            if row_key == 'CUSTOMER-ACCTG_':
                expected_ftp_prefix = 'CUSTOMER-ACCTG_'  # From azure-ftp-etl logs
                expected_zip_pattern = 'CUSTOMER-ACCTG*.zip'
                
                print(f"   Expected azure-ftp-etl prefix: '{expected_ftp_prefix}'")
                print(f"   Expected zip pattern: '{expected_zip_pattern}'")
                
                if file_prefix != expected_ftp_prefix:
                    print(f"   ⚠️  MISMATCH: FilePrefix should be '{expected_ftp_prefix}' for zip archiving")
                    
                    # Check current zip pattern
                    zip_pattern = entity.get('ZipFilePattern', 'NOT_SET')
                    print(f"   Current ZipFilePattern: '{zip_pattern}'")
                    
                    if zip_pattern != expected_zip_pattern:
                        print(f"   ⚠️  MISMATCH: ZipFilePattern should be '{expected_zip_pattern}'")
                else:
                    print(f"   ✅ FilePrefix matches azure-ftp-etl")
            
            elif row_key == 'FIMASTSALES_':
                expected_ftp_prefix = 'FIMASTSALES_'  # From azure-ftp-etl logs
                expected_zip_pattern = 'FIMASTSALES*.zip'
                
                print(f"   Expected azure-ftp-etl prefix: '{expected_ftp_prefix}'")
                print(f"   Expected zip pattern: '{expected_zip_pattern}'")
                
                if file_prefix != expected_ftp_prefix:
                    print(f"   ⚠️  MISMATCH: FilePrefix should be '{expected_ftp_prefix}' for zip archiving")
                    
                    # Check current zip pattern
                    zip_pattern = entity.get('ZipFilePattern', 'NOT_SET')
                    print(f"   Current ZipFilePattern: '{zip_pattern}'")
                    
                    if zip_pattern != expected_zip_pattern:
                        print(f"   ⚠️  MISMATCH: ZipFilePattern should be '{expected_zip_pattern}'")
                else:
                    print(f"   ✅ FilePrefix matches azure-ftp-etl")
        
        print(f"\n{'='*60}")
        print("🎯 Analysis")
        print(f"{'='*60}")
        print("Key insight from azure-ftp-etl logs:")
        print("- CUSTOMER-ACCTG files use prefix: CUSTOMER-ACCTG_")
        print("- FIMASTSALES files use prefix: FIMASTSALES_")
        print("")
        print("But azure-csv-processing might be using:")
        print("- EDW-CUSTOMER-ACCTG_ (with EDW- prefix)")
        print("- EDW-FIMASTSALES_ (with EDW- prefix)")
        print("")
        print("For zip archiving to work, the ZipFilePattern must match")
        print("the actual zip file names created by azure-ftp-etl.")
        print("")
        print("Zip files are named like:")
        print("- CUSTOMER-ACCTG_04-01-25_09.50.08.csv.zip")
        print("- FIMASTSALES_08-18-25_11.40.30.csv.zip")
        print("")
        print("So the patterns should be:")
        print("- CUSTOMER-ACCTG*.zip")
        print("- FIMASTSALES*.zip")
        
    except Exception as e:
        print(f"❌ Error accessing sftpConfig table: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
