import azure.functions as func
import logging
import json
import os
import sys
from datetime import datetime

# Add the shared modules to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

try:
    from orchestrator import CSVProcessingOrchestrator
    from azure_config import Config
except ImportError as e:
    logging.error(f"Failed to import shared modules: {str(e)}")
    # Fallback for testing
    CSVProcessingOrchestrator = None
    Config = None

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('CSV Processing Pipeline triggered')

    # Suppress verbose Azure SDK logging
    try:
        from azure_config import setup_quiet_azure_logging
        setup_quiet_azure_logging()
    except Exception:
        pass

    try:
        # Check if we have the required modules
        if CSVProcessingOrchestrator is None or Config is None:
            return func.HttpResponse(
                json.dumps({
                    "status": "error",
                    "message": "Shared modules not available. Function is in basic test mode.",
                    "timestamp": datetime.utcnow().isoformat(),
                    "method": req.method,
                    "url": str(req.url)
                }),
                status_code=500,
                mimetype="application/json"
            )

        # Get request parameters
        try:
            req_body = req.get_json()
        except ValueError:
            req_body = {}

        # Get parameters from query string or request body
        container_name = req.params.get('container') or (req_body.get('container') if req_body else None)
        input_folder = req.params.get('input_folder') or (req_body.get('input_folder') if req_body else 'input')
        output_folder = req.params.get('output_folder') or (req_body.get('output_folder') if req_body else 'output')

        # Initialize configuration
        config = Config()

        # Validate configuration
        if not config.validate():
            return func.HttpResponse(
                json.dumps({
                    "status": "error",
                    "message": "Configuration validation failed. Please check environment variables.",
                    "timestamp": datetime.utcnow().isoformat()
                }),
                status_code=500,
                mimetype="application/json"
            )

        # Initialize orchestrator
        orchestrator = CSVProcessingOrchestrator(config)

        # Process the pipeline
        logging.info(f"Starting CSV processing pipeline for container: {container_name}")
        result = orchestrator.process_pipeline(
            container_name=container_name,
            input_folder=input_folder,
            output_folder=output_folder
        )

        # Return success response
        response_data = {
            "status": "success",
            "message": "CSV processing pipeline completed successfully",
            "timestamp": datetime.utcnow().isoformat(),
            "result": result,
            "parameters": {
                "container": container_name,
                "input_folder": input_folder,
                "output_folder": output_folder
            }
        }

        return func.HttpResponse(
            json.dumps(response_data, indent=2),
            status_code=200,
            mimetype="application/json"
        )

    except Exception as e:
        logging.error(f"CSV Processing Pipeline failed: {str(e)}", exc_info=True)

        error_response = {
            "status": "error",
            "message": f"Pipeline execution failed: {str(e)}",
            "timestamp": datetime.utcnow().isoformat(),
            "error_type": type(e).__name__
        }

        return func.HttpResponse(
            json.dumps(error_response, indent=2),
            status_code=500,
            mimetype="application/json"
        )
