# Memory-Efficient Per-File Transformation Implementation

## Overview

Successfully implemented a memory-efficient approach that transforms each CSV file individually before combining them, significantly reducing peak memory usage compared to the previous approach of combining first then transforming.

## Key Changes Made

### 1. Modified `convert_csv_files_to_parquet()` Function
**Location**: `azure-csv-processing/shared/orchestrator.py` (lines 188-287)

**Old Approach**:
1. Convert CSV → Parquet (no transformation)
2. Combine all Parquet files
3. Transform combined data (peak memory usage)

**New Approach**:
1. **Phase 1**: Schema discovery across all files (lightweight sampling)
2. **Phase 2**: Transform each file individually with consistent schema
   - Load single CSV file
   - Add surrogate key + composite key
   - Add audit columns
   - Ensure consistent schema alignment
   - Upload transformed Parquet
   - Free memory immediately

### 2. Added Schema Alignment Function
**Location**: `azure-csv-processing/shared/data_transformer.py` (lines 465-544)

**Function**: `ensure_consistent_schema(df, expected_schema, file_name)`
- Handles missing columns (adds with null values)
- <PERSON>les extra columns (adds to expected schema)
- Ensures consistent column order across all files
- Maintains surrogate/composite keys first, audit columns last

### 3. Two-Phase Processing
**Phase 1 - Schema Discovery**:
- Samples first 100 rows from each CSV file
- Applies transformations to discover complete schema
- Builds unified schema across all files
- Minimal memory footprint

**Phase 2 - Transform & Upload**:
- Processes each file with the unified schema
- Applies transformations (surrogate key, audit columns)
- Ensures schema consistency
- Uploads immediately and frees memory

## Memory Efficiency Benefits

### Peak Memory Reduction
- **Old**: Peak memory = sum(all_file_sizes) + transformation_overhead
- **New**: Peak memory = max(single_file_size) + transformation_overhead

### Scalability Improvements
- Can handle datasets where sum of files > available memory
- Each file processed independently
- Failures isolated to individual files
- Better garbage collection between files

### Example Memory Usage
For 5 files of 200MB each:
- **Old approach**: ~1GB+ peak memory (all files + transformations)
- **New approach**: ~200MB+ peak memory (largest single file + transformations)

## Column Structure Maintained

The implementation preserves the exact same column structure as before:

1. **Surrogate Key** (first column)
   - MD5 hash of configured columns (default: ST/BR|DEAL NO||)
   - Configurable via Azure Table Storage

2. **Composite Key** (second column)
   - Cleaned concatenated string of configured columns
   - Shows the actual values used for hashing

3. **Original Data Columns** (middle)
   - All original CSV columns preserved
   - Missing columns filled with null values
   - Extra columns included from any file

4. **Audit Columns** (last three columns)
   - Source Date/Time (extracted from filename or current timestamp)
   - Created Date/Time (current timestamp)
   - Modified Date/Time (current timestamp)

## Error Handling

### Batch Failure Strategy
- If any file fails during schema discovery → fail entire batch
- If any file fails during transformation → fail entire batch
- Detailed error logging with specific file names and reasons

### Schema Consistency
- All files guaranteed to have identical schemas
- Type coercion applied where possible
- Graceful handling of missing/extra columns

## Testing Results

Successfully tested with:
- Files with different schemas (missing columns, extra columns)
- Multiple file consolidation
- Schema alignment across varying structures
- Memory cleanup verification

## Deployment

✅ **Successfully deployed to Azure Function App**: `csv-processing-func-3514`
- Endpoint: `https://csv-processing-func-3514.azurewebsites.net/api/csvprocessingpipeline`
- All dependencies updated (Polars 1.33.1, PyArrow 21.0.0)
- Python 3.11 runtime environment

## Backward Compatibility

- Maintains all existing configuration options
- Same output file naming conventions
- Same Azure Table Storage integration
- Same dealer-aware processing logic
- Same zip archiving functionality

## Performance Characteristics

### Memory Usage
- **Reduced peak memory** by processing files individually
- **Better garbage collection** with explicit cleanup between files
- **Streaming-friendly** for large datasets

### Processing Time
- **Slight overhead** from schema discovery phase
- **Offset by better memory management** and reduced swapping
- **More predictable performance** regardless of total dataset size

## Next Steps

The implementation is production-ready and deployed. Key benefits:
1. **Memory efficiency**: Handles larger datasets within memory constraints
2. **Reliability**: Better error isolation and handling
3. **Scalability**: Performance scales with largest single file, not total size
4. **Maintainability**: Clear separation of concerns with two-phase processing

The Azure Function App is now ready to process CSV files with significantly improved memory efficiency while maintaining all existing functionality and output formats.
