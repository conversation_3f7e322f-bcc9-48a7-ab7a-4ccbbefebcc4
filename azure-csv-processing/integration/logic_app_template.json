{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"logicAppName": {"type": "string", "defaultValue": "csv-processing-trigger", "metadata": {"description": "Name of the Logic App"}}, "functionAppName": {"type": "string", "defaultValue": "csv-processing-func-3514", "metadata": {"description": "Name of the Function App to trigger"}}, "storageAccountName": {"type": "string", "defaultValue": "sagedw", "metadata": {"description": "Storage account to monitor"}}, "containerName": {"type": "string", "defaultValue": "sftp-landing", "metadata": {"description": "Container to monitor for new files"}}}, "variables": {"functionAppUrl": "[concat('https://', parameters('functionAppName'), '.azurewebsites.net')]"}, "resources": [{"type": "Microsoft.Logic/workflows", "apiVersion": "2019-05-01", "name": "[parameters('logicAppName')]", "location": "[resourceGroup().location]", "properties": {"state": "Enabled", "definition": {"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "*******", "parameters": {}, "triggers": {"When_a_blob_is_added_or_modified": {"recurrence": {"frequency": "Minute", "interval": 5}, "evaluatedRecurrence": {"frequency": "Minute", "interval": 5}, "splitOn": "@triggerBody()", "type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azureblob']['connectionId']"}}, "method": "get", "path": "/datasets/default/triggers/batch/onupdatedfile", "queries": {"folderId": "[concat('/', parameters('containerName'))]", "maxFileCount": 10}}}}, "actions": {"Check_file_extension": {"runAfter": {}, "type": "If", "expression": {"and": [{"endsWith": ["@triggerBody()?['Name']", ".csv"]}, {"or": [{"contains": ["@triggerBody()?['Name']", "FIMASTSALES"]}, {"contains": ["@triggerBody()?['Name']", "FIMASTSALES2TEST"]}]}]}, "actions": {"Log_file_detection": {"runAfter": {}, "type": "Compose", "inputs": {"message": "CSV file detected for processing", "fileName": "@triggerBody()?['Name']", "fileSize": "@triggerBody()?['Size']", "lastModified": "@triggerBody()?['LastModified']", "container": "[parameters('containerName')]", "timestamp": "@utcNow()"}}, "Wait_for_file_stability": {"runAfter": {"Log_file_detection": ["Succeeded"]}, "type": "Wait", "inputs": {"interval": {"count": 30, "unit": "Second"}}}, "Call_CSV_Processing_Function": {"runAfter": {"Wait_for_file_stability": ["Succeeded"]}, "type": "Http", "inputs": {"method": "POST", "uri": "[concat(variables('functionAppUrl'), '/api/csvprocessingpipeline')]", "headers": {"Content-Type": "application/json"}, "body": {"container": "[parameters('containerName')]", "triggered_by": "logic_app", "trigger_file": "@triggerBody()?['Name']", "trigger_timestamp": "@utcNow()"}, "authentication": {"type": "ManagedServiceIdentity"}}}, "Handle_function_response": {"runAfter": {"Call_CSV_Processing_Function": ["Succeeded", "Failed"]}, "type": "Switch", "expression": "@outputs('Call_CSV_Processing_Function')['statusCode']", "cases": {"Success": {"case": 200, "actions": {"Log_success": {"type": "Compose", "inputs": {"status": "success", "message": "CSV processing completed successfully", "fileName": "@triggerBody()?['Name']", "functionResponse": "@body('Call_CSV_Processing_Function')", "timestamp": "@utcNow()"}}}}, "Error": {"case": 500, "actions": {"Log_error": {"type": "Compose", "inputs": {"status": "error", "message": "CSV processing failed", "fileName": "@triggerBody()?['Name']", "errorDetails": "@body('Call_CSV_Processing_Function')", "timestamp": "@utcNow()"}}, "Send_error_notification": {"runAfter": {"Log_error": ["Succeeded"]}, "type": "Http", "inputs": {"method": "POST", "uri": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "headers": {"Content-Type": "application/json"}, "body": {"text": "🚨 CSV Processing Failed", "attachments": [{"color": "danger", "fields": [{"title": "File", "value": "@triggerBody()?['Name']", "short": true}, {"title": "Container", "value": "[parameters('containerName')]", "short": true}, {"title": "Error", "value": "@body('Call_CSV_Processing_Function')?['error']?['message']", "short": false}]}]}}}}}}, "default": {"actions": {"Log_unexpected_response": {"type": "Compose", "inputs": {"status": "unexpected", "message": "Unexpected response from function", "statusCode": "@outputs('Call_CSV_Processing_Function')['statusCode']", "response": "@body('Call_CSV_Processing_Function')", "timestamp": "@utcNow()"}}}}}}, "else": {"actions": {"Log_file_ignored": {"type": "Compose", "inputs": {"message": "File ignored - not a target CSV file", "fileName": "@triggerBody()?['Name']", "reason": "File does not match FIMASTSALES patterns or is not a CSV file", "timestamp": "@utcNow()"}}}}}}, "outputs": {}}, "parameters": {"$connections": {"value": {"azureblob": {"connectionId": "[resourceId('Microsoft.Web/connections', 'azureblob')]", "connectionName": "azureb<PERSON>b", "id": "[subscriptionResourceId('Microsoft.Web/locations/managedApis', resourceGroup().location, 'azureblob')]"}}}}}}, {"type": "Microsoft.Web/connections", "apiVersion": "2016-06-01", "name": "azureb<PERSON>b", "location": "[resourceGroup().location]", "properties": {"displayName": "Azure Blob Storage Connection", "customParameterValues": {}, "api": {"id": "[subscriptionResourceId('Microsoft.Web/locations/managedApis', resourceGroup().location, 'azureblob')]"}, "parameterValues": {"accountName": "[parameters('storageAccountName')]", "accessKey": "[listKeys(resourceId('Microsoft.Storage/storageAccounts', parameters('storageAccountName')), '2019-06-01').keys[0].value]"}}}], "outputs": {"logicAppUrl": {"type": "string", "value": "[listCallbackURL(concat(resourceId('Microsoft.Logic/workflows', parameters('logicAppName')), '/triggers/When_a_blob_is_added_or_modified'), '2019-05-01').value]"}}}