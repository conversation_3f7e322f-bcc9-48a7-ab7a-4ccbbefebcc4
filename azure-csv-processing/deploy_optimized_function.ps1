# Deploy Optimized Azure Function with Memory Improvements
# This script deploys the updated function with memory optimization fixes

param(
    [string]$FunctionAppName = "csv-processing-func-3514",
    [string]$ResourceGroup = "rg-swickard-edw",
    [switch]$RunTests = $true,
    [switch]$Force = $false
)

Write-Host "🚀 Deploying Optimized Azure Function App" -ForegroundColor Cyan
Write-Host "Function App: $FunctionAppName" -ForegroundColor Yellow
Write-Host "Resource Group: $ResourceGroup" -ForegroundColor Yellow

# Function to check Azure CLI login
function Test-AzureLogin {
    try {
        $account = az account show --query "name" --output tsv 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Azure CLI logged in as: $account" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "❌ Azure CLI not logged in" -ForegroundColor Red
        Write-Host "Please run: az login" -ForegroundColor Yellow
        return $false
    }
}

# Function to run tests
function Invoke-Tests {
    Write-Host "`n📋 Running pre-deployment tests..." -ForegroundColor Cyan
    
    try {
        # Test 1: Configuration switching test
        Write-Host "Running configuration switching test..." -ForegroundColor Gray
        $configTestResult = python test_configuration_switching.py
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Configuration switching test passed" -ForegroundColor Green
        } else {
            Write-Host "❌ Configuration switching test failed" -ForegroundColor Red
            if (-not $Force) {
                throw "Configuration test failed"
            }
        }
        
        # Test 2: Memory optimization test
        Write-Host "Running memory optimization test..." -ForegroundColor Gray
        $memoryTestResult = python test_memory_optimization.py
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Memory optimization test passed" -ForegroundColor Green
        } else {
            Write-Host "❌ Memory optimization test failed" -ForegroundColor Red
            if (-not $Force) {
                throw "Memory optimization test failed"
            }
        }
        
        return $true
    }
    catch {
        Write-Host "❌ Tests failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to deploy the function app
function Deploy-FunctionApp {
    Write-Host "`n📦 Deploying Function App..." -ForegroundColor Cyan
    
    try {
        # Check if func CLI is available
        $funcPath = "C:\Users\<USER>\AppData\Roaming\npm\func.cmd"
        if (-not (Test-Path $funcPath)) {
            Write-Host "❌ Azure Functions Core Tools not found at: $funcPath" -ForegroundColor Red
            Write-Host "Please install Azure Functions Core Tools" -ForegroundColor Yellow
            return $false
        }
        
        Write-Host "Using func CLI at: $funcPath" -ForegroundColor Gray
        
        # Deploy the function
        Write-Host "Deploying to: $FunctionAppName" -ForegroundColor Gray
        & $funcPath azure functionapp publish $FunctionAppName --python
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Function App deployed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Function App deployment failed" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error deploying Function App: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to verify deployment
function Test-Deployment {
    Write-Host "`n🔍 Verifying deployment..." -ForegroundColor Cyan
    
    try {
        # Test function endpoint
        $functionUrl = "https://$FunctionAppName.azurewebsites.net/api/csvprocessingpipeline"
        Write-Host "Testing function endpoint: $functionUrl" -ForegroundColor Gray
        
        # Simple connectivity test
        try {
            $response = Invoke-WebRequest -Uri $functionUrl -Method GET -TimeoutSec 10
            Write-Host "✅ Function endpoint is accessible" -ForegroundColor Green
        }
        catch {
            if ($_.Exception.Response.StatusCode -eq 401) {
                Write-Host "✅ Function endpoint is accessible (401 Unauthorized is expected)" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Function endpoint test inconclusive: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
        
        return $true
    }
    catch {
        Write-Host "❌ Error verifying deployment: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main deployment process
try {
    Write-Host "`n=== DEPLOYMENT SUMMARY ===" -ForegroundColor Cyan
    Write-Host "Optimizations included:" -ForegroundColor White
    Write-Host "  ✓ Memory-optimized CSV download with streaming" -ForegroundColor Green
    Write-Host "  ✓ Individual file processing with garbage collection" -ForegroundColor Green
    Write-Host "  ✓ Increased function timeout to 15 minutes" -ForegroundColor Green
    Write-Host "  ✓ Enhanced error handling and logging" -ForegroundColor Green
    
    # Step 1: Verify Azure login
    Write-Host "`nStep 1: Verifying Azure login..." -ForegroundColor Cyan
    if (-not (Test-AzureLogin)) {
        exit 1
    }
    
    # Step 2: Run tests (if requested)
    if ($RunTests) {
        Write-Host "`nStep 2: Running tests..." -ForegroundColor Cyan
        if (-not (Invoke-Tests)) {
            if (-not $Force) {
                Write-Host "❌ Tests failed. Use -Force to deploy anyway." -ForegroundColor Red
                exit 1
            } else {
                Write-Host "⚠️ Tests failed but continuing due to -Force flag" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "`nStep 2: Skipping tests (use -RunTests to enable)" -ForegroundColor Yellow
    }
    
    # Step 3: Deploy Function App
    Write-Host "`nStep 3: Deploying Function App..." -ForegroundColor Cyan
    if (-not (Deploy-FunctionApp)) {
        Write-Host "❌ Function App deployment failed" -ForegroundColor Red
        exit 1
    }
    
    # Step 4: Verify deployment
    Write-Host "`nStep 4: Verifying deployment..." -ForegroundColor Cyan
    Test-Deployment | Out-Null
    
    # Success summary
    Write-Host "`n🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "Function App: $FunctionAppName" -ForegroundColor White
    Write-Host "Endpoint: https://$FunctionAppName.azurewebsites.net/api/csvprocessingpipeline" -ForegroundColor White
    Write-Host "`nNext steps:" -ForegroundColor Cyan
    Write-Host "1. Test the function with your CSV files" -ForegroundColor White
    Write-Host "2. Monitor memory usage in Application Insights" -ForegroundColor White
    Write-Host "3. Verify configuration switching works as expected" -ForegroundColor White
    
}
catch {
    Write-Host "`n❌ DEPLOYMENT FAILED!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
