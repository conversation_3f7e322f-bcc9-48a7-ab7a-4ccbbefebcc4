#!/usr/bin/env python3
"""
Script to check what's actually in the blob storage folders
"""
import os
import sys

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(current_dir, 'shared')
if shared_dir not in sys.path:
    sys.path.append(shared_dir)

try:
    from azure.storage.blob import BlobServiceClient
    from azure.identity import DefaultAzureCredential
    AZURE_MODULES_AVAILABLE = True
except ImportError:
    AZURE_MODULES_AVAILABLE = False
    print("❌ Azure modules not available")
    sys.exit(1)

def list_blobs_in_folder(container_name, folder_prefix):
    """List all blobs in a specific folder"""
    try:
        storage_account_name = "sagedw"
        storage_account_url = f"https://{storage_account_name}.blob.core.windows.net"
        credential = DefaultAzureCredential()
        blob_service_client = BlobServiceClient(account_url=storage_account_url, credential=credential)
        
        container_client = blob_service_client.get_container_client(container_name)
        
        blobs = []
        for blob in container_client.list_blobs(name_starts_with=folder_prefix):
            blobs.append(blob.name)
        
        return blobs
    except Exception as e:
        print(f"❌ Error listing blobs in {folder_prefix}: {e}")
        return []

def main():
    print("🔍 Checking actual blob storage contents...")
    
    if not AZURE_MODULES_AVAILABLE:
        print("❌ Azure modules not available")
        return
    
    container_name = "sftp-landing"
    
    # Folders to check based on configuration
    folders_to_check = [
        # CUSTOMER-ACCTG expected folders
        "CUSTOMER-ACCTG/edw-customer-acctg-compressed",
        "CUSTOMER-ACCTG/edw-customer-acctg-decompressed", 
        "CUSTOMER-ACCTG/edw-customer-acctg-enhanced",
        "CUSTOMER-ACCTG/archive",
        "CUSTOMER-ACCTG",  # Check root folder too
        
        # FIMASTSALES expected folders  
        "FIMAST-SALES/edw-fimastsales-compressed",
        "FIMAST-SALES/edw-fimastsales-decompressed",
        "FIMAST-SALES/edw-fimastsales-enhanced", 
        "FIMAST-SALES/archive",
        "FIMAST-SALES",  # Check root folder too
        
        # Alternative folder structures that might exist
        "CUSTOMER-ACCTG/customer-acctg-compressed",
        "CUSTOMER-ACCTG/compressed",
        "FIMAST-SALES/fimastsales-compressed",
        "FIMAST-SALES/compressed",
    ]
    
    for folder in folders_to_check:
        print(f"\n{'='*60}")
        print(f"📁 Checking folder: {folder}")
        print(f"{'='*60}")
        
        blobs = list_blobs_in_folder(container_name, folder)
        
        if blobs:
            print(f"✅ Found {len(blobs)} items:")
            
            # Separate zip files from other files
            zip_files = [b for b in blobs if b.endswith('.zip')]
            csv_files = [b for b in blobs if b.endswith('.csv')]
            parquet_files = [b for b in blobs if b.endswith('.parquet')]
            other_files = [b for b in blobs if not any(b.endswith(ext) for ext in ['.zip', '.csv', '.parquet'])]
            
            if zip_files:
                print(f"   📦 ZIP files ({len(zip_files)}):")
                for zip_file in zip_files[:10]:  # Show first 10
                    print(f"     - {zip_file}")
                if len(zip_files) > 10:
                    print(f"     ... and {len(zip_files) - 10} more")
            
            if csv_files:
                print(f"   📄 CSV files ({len(csv_files)}):")
                for csv_file in csv_files[:5]:  # Show first 5
                    print(f"     - {csv_file}")
                if len(csv_files) > 5:
                    print(f"     ... and {len(csv_files) - 5} more")
            
            if parquet_files:
                print(f"   📊 Parquet files ({len(parquet_files)}):")
                for parquet_file in parquet_files[:5]:  # Show first 5
                    print(f"     - {parquet_file}")
                if len(parquet_files) > 5:
                    print(f"     ... and {len(parquet_files) - 5} more")
            
            if other_files:
                print(f"   📋 Other files ({len(other_files)}):")
                for other_file in other_files[:5]:  # Show first 5
                    print(f"     - {other_file}")
                if len(other_files) > 5:
                    print(f"     ... and {len(other_files) - 5} more")
        else:
            print("❌ No files found")
    
    print(f"\n{'='*60}")
    print("🔍 Analysis")
    print(f"{'='*60}")
    print("The zip archiving is looking for zip files in:")
    print("- CUSTOMER-ACCTG/edw-customer-acctg-compressed (pattern: CUSTOMER-ACCTG*.zip)")
    print("- FIMAST-SALES/edw-fimastsales-compressed (pattern: FIMASTSALES*.zip)")
    print("\nIf zip files are in different folders or have different naming patterns,")
    print("the archiving configuration needs to be updated.")

if __name__ == "__main__":
    main()
