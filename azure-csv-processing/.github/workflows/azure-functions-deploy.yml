name: Deploy CSV Processing Function to Azure

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'azure-csv-processing/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'azure-csv-processing/**'
  workflow_dispatch:

env:
  AZURE_FUNCTIONAPP_NAME: csv-processing-func-3514
  AZURE_FUNCTIONAPP_PACKAGE_PATH: './azure-csv-processing'
  PYTHON_VERSION: '3.11'

jobs:
  test:
    runs-on: ubuntu-latest
    name: Run Tests
    steps:
    - name: 'Checkout GitHub Action'
      uses: actions/checkout@v4

    - name: Setup Python ${{ env.PYTHON_VERSION }} Environment
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: 'Create and activate virtual environment'
      run: |
        python -m venv venv
        source venv/bin/activate
        echo "VIRTUAL_ENV=$VIRTUAL_ENV" >> $GITHUB_ENV
        echo "$VIRTUAL_ENV/bin" >> $GITHUB_PATH

    - name: 'Install dependencies'
      run: |
        pip install --upgrade pip
        pip install -r ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}/requirements.txt
        pip install pytest pytest-cov pytest-mock

    - name: 'Run unit tests'
      run: |
        cd ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        python -m pytest tests/ -v --cov=shared --cov-report=xml --cov-report=html
      continue-on-error: false

    - name: 'Upload coverage reports'
      uses: codecov/codecov-action@v3
      with:
        file: ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}/coverage.xml
        flags: unittests
        name: codecov-umbrella

  lint:
    runs-on: ubuntu-latest
    name: Code Quality Check
    steps:
    - name: 'Checkout GitHub Action'
      uses: actions/checkout@v4

    - name: Setup Python ${{ env.PYTHON_VERSION }} Environment
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: 'Install linting tools'
      run: |
        pip install --upgrade pip
        pip install flake8 black isort mypy

    - name: 'Run Black formatter check'
      run: |
        cd ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        black --check --diff .

    - name: 'Run isort import sorting check'
      run: |
        cd ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        isort --check-only --diff .

    - name: 'Run flake8 linting'
      run: |
        cd ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

  security-scan:
    runs-on: ubuntu-latest
    name: Security Scan
    steps:
    - name: 'Checkout GitHub Action'
      uses: actions/checkout@v4

    - name: Setup Python ${{ env.PYTHON_VERSION }} Environment
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: 'Install security scanning tools'
      run: |
        pip install --upgrade pip
        pip install bandit safety

    - name: 'Run Bandit security scan'
      run: |
        cd ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        bandit -r . -f json -o bandit-report.json || true
        bandit -r . --severity-level medium

    - name: 'Run Safety dependency scan'
      run: |
        cd ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        safety check --json --output safety-report.json || true
        safety check

  build-and-deploy:
    runs-on: ubuntu-latest
    needs: [test, lint, security-scan]
    name: Build and Deploy
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
    - name: 'Checkout GitHub Action'
      uses: actions/checkout@v4

    - name: Setup Python ${{ env.PYTHON_VERSION }} Environment
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: 'Create and activate virtual environment'
      run: |
        python -m venv venv
        source venv/bin/activate
        echo "VIRTUAL_ENV=$VIRTUAL_ENV" >> $GITHUB_ENV
        echo "$VIRTUAL_ENV/bin" >> $GITHUB_PATH

    - name: 'Install dependencies'
      run: |
        pip install --upgrade pip
        pip install -r ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}/requirements.txt

    - name: 'Run final tests before deployment'
      run: |
        cd ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        python -m pytest tests/ -v --tb=short

    - name: 'Azure Login'
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}

    - name: 'Deploy to Azure Functions'
      uses: Azure/functions-action@v1
      with:
        app-name: ${{ env.AZURE_FUNCTIONAPP_NAME }}
        package: ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        publish-profile: ${{ secrets.AZURE_FUNCTIONAPP_PUBLISH_PROFILE }}
        scm-do-build-during-deployment: true
        enable-oryx-build: true

    - name: 'Post-deployment health check'
      run: |
        sleep 30  # Wait for deployment to complete
        curl -f "${{ secrets.FUNCTION_APP_URL }}/api/csvprocessingpipeline" \
          -H "Content-Type: application/json" \
          -d '{"test": true}' \
          --max-time 30 || echo "Health check failed - function may still be warming up"

  integration-test:
    runs-on: ubuntu-latest
    needs: [build-and-deploy]
    name: Integration Tests
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
    - name: 'Checkout GitHub Action'
      uses: actions/checkout@v4

    - name: Setup Python ${{ env.PYTHON_VERSION }} Environment
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: 'Install test dependencies'
      run: |
        pip install --upgrade pip
        pip install requests pytest

    - name: 'Run integration tests'
      run: |
        cd ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
        python -m pytest tests/integration/ -v --tb=short
      env:
        FUNCTION_APP_URL: ${{ secrets.FUNCTION_APP_URL }}
        FUNCTION_APP_KEY: ${{ secrets.FUNCTION_APP_KEY }}

  notify:
    runs-on: ubuntu-latest
    needs: [build-and-deploy, integration-test]
    name: Notify Deployment Status
    if: always()
    steps:
    - name: 'Notify Success'
      if: needs.build-and-deploy.result == 'success' && needs.integration-test.result == 'success'
      run: |
        echo "✅ Deployment successful!"
        echo "Function App: ${{ env.AZURE_FUNCTIONAPP_NAME }}"
        echo "Commit: ${{ github.sha }}"

    - name: 'Notify Failure'
      if: needs.build-and-deploy.result == 'failure' || needs.integration-test.result == 'failure'
      run: |
        echo "❌ Deployment failed!"
        echo "Check the logs for details."
        exit 1
