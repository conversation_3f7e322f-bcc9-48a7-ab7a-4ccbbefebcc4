"""
Deployment Validation Script for Phase 4: Deployment & Validation
Comprehensive validation of the Parquet + Polars implementation for production readiness
"""

import os
import sys
import time
import logging
import polars as pl
from datetime import datetime
from unittest.mock import patch

# Add shared directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(os.path.dirname(current_dir), 'shared')
if shared_dir not in sys.path:
    sys.path.append(shared_dir)

from orchestrator import run_parquet_processing_pipeline, CSVProcessingOrchestrator


class DeploymentValidator:
    """Comprehensive deployment validation for production readiness"""
    
    def __init__(self):
        self.validation_results = {}
        self.start_time = datetime.now()
        
        # Configure logging for deployment validation
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(f'deployment_validation_{self.start_time.strftime("%Y%m%d_%H%M%S")}.log')
            ]
        )
        
    def log_validation_step(self, step_name, status, details=None):
        """Log validation step results"""
        self.validation_results[step_name] = {
            'status': status,
            'timestamp': datetime.now(),
            'details': details or {}
        }
        
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        logging.info(f"{status_emoji} {step_name}: {status}")
        
        if details:
            for key, value in details.items():
                logging.info(f"    {key}: {value}")

    def validate_dependencies(self):
        """Validate all required dependencies are installed"""
        logging.info("=" * 60)
        logging.info("STEP 1: DEPENDENCY VALIDATION")
        logging.info("=" * 60)
        
        required_packages = {
            'polars': '0.20.0',
            'pyarrow': '14.0.0',
            'azure-functions': '1.0.0',
            'azure-storage-blob': '12.0.0',
            'azure-identity': '1.0.0'
        }
        
        dependency_status = {}
        all_dependencies_ok = True
        
        for package, min_version in required_packages.items():
            try:
                if package == 'polars':
                    import polars as pl
                    version = pl.__version__
                elif package == 'pyarrow':
                    import pyarrow as pa
                    version = pa.__version__
                elif package == 'azure-functions':
                    import azure.functions
                    version = azure.functions.__version__
                elif package == 'azure-storage-blob':
                    import azure.storage.blob
                    version = "12.0.0+"  # Version detection varies
                elif package == 'azure-identity':
                    import azure.identity
                    version = "1.0.0+"  # Version detection varies
                
                dependency_status[package] = f"✅ {version}"
                logging.info(f"  ✅ {package}: {version}")
                
            except ImportError as e:
                dependency_status[package] = f"❌ Missing"
                logging.error(f"  ❌ {package}: Missing - {e}")
                all_dependencies_ok = False
        
        status = "PASS" if all_dependencies_ok else "FAIL"
        self.log_validation_step("Dependency Validation", status, dependency_status)
        
        return all_dependencies_ok

    def validate_core_functionality(self):
        """Validate core Polars functionality works correctly"""
        logging.info("=" * 60)
        logging.info("STEP 2: CORE FUNCTIONALITY VALIDATION")
        logging.info("=" * 60)
        
        try:
            # Test basic Polars operations
            test_data = pl.DataFrame({
                'customer_id': [1001, 1002, 1003],
                'product_id': [2001, 2002, 2003],
                'sale_amount': [150.50, 299.99, 75.25],
                'sale_date': ['2024-01-15', '2024-01-16', '2024-01-17'],
                'sales_rep': ['John Doe', 'Jane Smith', 'Bob Wilson']
            })
            
            # Test lazy evaluation
            lazy_df = test_data.lazy()
            result = lazy_df.filter(pl.col('sale_amount') > 100).collect()
            
            # Test transformations
            from data_transformer import add_surrogate_key_polars, append_audit_columns_polars
            
            with_key = add_surrogate_key_polars(test_data)
            with_audit = append_audit_columns_polars(with_key, ['test.parquet'])
            
            # Validate results
            validation_details = {
                'original_shape': test_data.shape,
                'lazy_filter_result': result.shape,
                'with_surrogate_key_shape': with_key.shape,
                'final_shape': with_audit.shape,
                'surrogate_key_position': with_audit.columns[0],
                'audit_columns_count': len([col for col in with_audit.columns if 'Date/Time' in col])
            }
            
            # Check validation criteria
            all_checks_pass = (
                with_audit.shape == (3, 9) and  # 5 original + 1 surrogate + 3 audit
                with_audit.columns[0] == 'Surrogate Key' and
                validation_details['audit_columns_count'] == 3
            )
            
            status = "PASS" if all_checks_pass else "FAIL"
            self.log_validation_step("Core Functionality Validation", status, validation_details)
            
            return all_checks_pass
            
        except Exception as e:
            self.log_validation_step("Core Functionality Validation", "FAIL", {'error': str(e)})
            return False

    def validate_performance_benchmarks(self):
        """Validate performance meets production requirements"""
        logging.info("=" * 60)
        logging.info("STEP 3: PERFORMANCE BENCHMARK VALIDATION")
        logging.info("=" * 60)
        
        try:
            # Create performance test datasets
            small_dataset = pl.DataFrame({
                'id': range(1000),
                'value': [i * 1.5 for i in range(1000)],
                'text': [f'Text_{i}' for i in range(1000)]
            })
            
            large_dataset = pl.DataFrame({
                'id': range(50000),
                'value': [i * 1.5 for i in range(50000)],
                'text': [f'Text_{i}' for i in range(50000)]
            })
            
            # Performance tests
            from data_transformer import add_surrogate_key_polars
            
            # Small dataset performance
            start_time = time.time()
            small_result = add_surrogate_key_polars(small_dataset)
            small_time = time.time() - start_time
            
            # Large dataset performance
            start_time = time.time()
            large_result = add_surrogate_key_polars(large_dataset)
            large_time = time.time() - start_time
            
            # Performance criteria
            small_target = 0.1  # 100ms for 1k rows
            large_target = 1.0   # 1s for 50k rows
            
            performance_details = {
                'small_dataset_time': f"{small_time:.4f}s",
                'small_dataset_target': f"{small_target}s",
                'small_dataset_pass': small_time < small_target,
                'large_dataset_time': f"{large_time:.4f}s",
                'large_dataset_target': f"{large_target}s",
                'large_dataset_pass': large_time < large_target,
                'small_throughput': f"{1000/small_time:.0f} rows/sec",
                'large_throughput': f"{50000/large_time:.0f} rows/sec"
            }
            
            all_performance_ok = (
                performance_details['small_dataset_pass'] and
                performance_details['large_dataset_pass']
            )
            
            status = "PASS" if all_performance_ok else "FAIL"
            self.log_validation_step("Performance Benchmark Validation", status, performance_details)
            
            return all_performance_ok
            
        except Exception as e:
            self.log_validation_step("Performance Benchmark Validation", "FAIL", {'error': str(e)})
            return False

    def validate_memory_efficiency(self):
        """Validate memory usage stays within acceptable limits"""
        logging.info("=" * 60)
        logging.info("STEP 4: MEMORY EFFICIENCY VALIDATION")
        logging.info("=" * 60)
        
        try:
            # Create large dataset for memory testing
            memory_test_dataset = pl.DataFrame({
                'id': range(100000),
                'value1': [i * 1.1 for i in range(100000)],
                'value2': [i * 2.2 for i in range(100000)],
                'text_field': [f'Text_{i}' for i in range(100000)],
                'category': [f'Cat_{i % 100}' for i in range(100000)]
            })
            
            # Test memory-intensive operations
            from data_transformer import add_surrogate_key_polars, append_audit_columns_polars
            
            # Process large dataset
            start_time = time.time()
            with_key = add_surrogate_key_polars(memory_test_dataset)
            with_audit = append_audit_columns_polars(with_key, ['memory_test.parquet'])
            processing_time = time.time() - start_time
            
            # Memory efficiency criteria
            memory_details = {
                'dataset_size': '100,000 rows',
                'processing_time': f"{processing_time:.4f}s",
                'final_shape': with_audit.shape,
                'memory_efficient': processing_time < 2.0,  # Should complete in under 2 seconds
                'no_memory_errors': True  # If we get here, no memory errors occurred
            }
            
            status = "PASS" if memory_details['memory_efficient'] else "WARN"
            self.log_validation_step("Memory Efficiency Validation", status, memory_details)
            
            return True  # If we complete without errors, memory is acceptable
            
        except MemoryError:
            self.log_validation_step("Memory Efficiency Validation", "FAIL", {'error': 'Memory error encountered'})
            return False
        except Exception as e:
            self.log_validation_step("Memory Efficiency Validation", "FAIL", {'error': str(e)})
            return False

    def validate_error_handling(self):
        """Validate error handling works correctly"""
        logging.info("=" * 60)
        logging.info("STEP 5: ERROR HANDLING VALIDATION")
        logging.info("=" * 60)
        
        error_handling_results = {}
        
        try:
            from data_transformer import add_surrogate_key_polars, append_audit_columns_polars
            
            # Test 1: Empty dataset
            empty_df = pl.DataFrame({'col1': [], 'col2': []})
            try:
                result = add_surrogate_key_polars(empty_df)
                error_handling_results['empty_dataset'] = "✅ HANDLED"
            except Exception as e:
                error_handling_results['empty_dataset'] = f"❌ FAILED: {e}"
            
            # Test 2: Single row dataset
            single_row_df = pl.DataFrame({'col1': [1], 'col2': [2]})
            try:
                result = add_surrogate_key_polars(single_row_df)
                error_handling_results['single_row'] = "✅ HANDLED"
            except Exception as e:
                error_handling_results['single_row'] = f"❌ FAILED: {e}"
            
            # Test 3: Null values
            null_df = pl.DataFrame({'col1': [1, None, 3], 'col2': [None, 2, 3]})
            try:
                result = add_surrogate_key_polars(null_df)
                error_handling_results['null_values'] = "✅ HANDLED"
            except Exception as e:
                error_handling_results['null_values'] = f"❌ FAILED: {e}"
            
            # Test 4: Text-only dataset
            text_df = pl.DataFrame({'name': ['A', 'B', 'C'], 'desc': ['X', 'Y', 'Z']})
            try:
                result = add_surrogate_key_polars(text_df)
                error_handling_results['text_only'] = "✅ HANDLED"
            except Exception as e:
                error_handling_results['text_only'] = f"❌ FAILED: {e}"
            
            # Check overall error handling
            all_handled = all("✅ HANDLED" in result for result in error_handling_results.values())
            
            status = "PASS" if all_handled else "FAIL"
            self.log_validation_step("Error Handling Validation", status, error_handling_results)
            
            return all_handled
            
        except Exception as e:
            self.log_validation_step("Error Handling Validation", "FAIL", {'error': str(e)})
            return False

    def validate_azure_function_compatibility(self):
        """Validate Azure Function compatibility"""
        logging.info("=" * 60)
        logging.info("STEP 6: AZURE FUNCTION COMPATIBILITY VALIDATION")
        logging.info("=" * 60)
        
        try:
            # Check Azure Functions specific requirements
            compatibility_details = {}
            
            # Test 1: Import Azure Functions
            try:
                import azure.functions as func
                compatibility_details['azure_functions_import'] = "✅ SUCCESS"
            except ImportError:
                compatibility_details['azure_functions_import'] = "❌ FAILED"
            
            # Test 2: Check function.json exists
            function_json_path = os.path.join(os.path.dirname(current_dir), 'function.json')
            if os.path.exists(function_json_path):
                compatibility_details['function_json'] = "✅ EXISTS"
            else:
                compatibility_details['function_json'] = "⚠️ NOT FOUND"
            
            # Test 3: Check requirements.txt is valid
            requirements_path = os.path.join(os.path.dirname(current_dir), 'requirements.txt')
            if os.path.exists(requirements_path):
                compatibility_details['requirements_txt'] = "✅ EXISTS"
            else:
                compatibility_details['requirements_txt'] = "❌ MISSING"
            
            # Test 4: Timeout compliance (should complete well under 10 minutes)
            test_data = pl.DataFrame({'id': range(1000), 'value': range(1000)})
            start_time = time.time()
            
            from data_transformer import add_surrogate_key_polars
            result = add_surrogate_key_polars(test_data)
            
            processing_time = time.time() - start_time
            timeout_limit = 600  # 10 minutes
            
            compatibility_details['processing_time'] = f"{processing_time:.4f}s"
            compatibility_details['timeout_limit'] = f"{timeout_limit}s"
            compatibility_details['timeout_compliance'] = processing_time < timeout_limit
            
            # Overall compatibility check
            critical_checks = [
                compatibility_details.get('azure_functions_import') == "✅ SUCCESS",
                compatibility_details.get('requirements_txt') == "✅ EXISTS",
                compatibility_details.get('timeout_compliance', False)
            ]
            
            all_compatible = all(critical_checks)
            status = "PASS" if all_compatible else "WARN"
            
            self.log_validation_step("Azure Function Compatibility", status, compatibility_details)
            
            return all_compatible
            
        except Exception as e:
            self.log_validation_step("Azure Function Compatibility", "FAIL", {'error': str(e)})
            return False

    def generate_deployment_report(self):
        """Generate comprehensive deployment validation report"""
        logging.info("=" * 60)
        logging.info("DEPLOYMENT VALIDATION REPORT")
        logging.info("=" * 60)
        
        total_validations = len(self.validation_results)
        passed_validations = sum(1 for result in self.validation_results.values() if result['status'] == 'PASS')
        failed_validations = sum(1 for result in self.validation_results.values() if result['status'] == 'FAIL')
        warning_validations = sum(1 for result in self.validation_results.values() if result['status'] == 'WARN')
        
        end_time = datetime.now()
        total_time = end_time - self.start_time
        
        logging.info(f"Validation Summary:")
        logging.info(f"  Total Validations: {total_validations}")
        logging.info(f"  ✅ Passed: {passed_validations}")
        logging.info(f"  ❌ Failed: {failed_validations}")
        logging.info(f"  ⚠️ Warnings: {warning_validations}")
        logging.info(f"  Total Time: {total_time}")
        
        # Determine overall deployment readiness
        if failed_validations == 0:
            if warning_validations == 0:
                deployment_status = "🎉 READY FOR PRODUCTION DEPLOYMENT"
                logging.info(f"\n{deployment_status}")
            else:
                deployment_status = "⚠️ READY WITH WARNINGS"
                logging.info(f"\n{deployment_status}")
        else:
            deployment_status = "❌ NOT READY - ISSUES MUST BE RESOLVED"
            logging.info(f"\n{deployment_status}")
        
        # Detailed results
        logging.info("\nDetailed Results:")
        for step_name, result in self.validation_results.items():
            status_emoji = "✅" if result['status'] == "PASS" else "❌" if result['status'] == "FAIL" else "⚠️"
            logging.info(f"  {status_emoji} {step_name}: {result['status']}")
        
        return {
            'deployment_status': deployment_status,
            'total_validations': total_validations,
            'passed': passed_validations,
            'failed': failed_validations,
            'warnings': warning_validations,
            'total_time': str(total_time),
            'ready_for_deployment': failed_validations == 0
        }

    def run_full_validation(self):
        """Run complete deployment validation suite"""
        logging.info("🚀 Starting Deployment Validation for Parquet + Polars Implementation")
        logging.info(f"Validation started at: {self.start_time}")
        
        # Run all validation steps
        validation_steps = [
            self.validate_dependencies,
            self.validate_core_functionality,
            self.validate_performance_benchmarks,
            self.validate_memory_efficiency,
            self.validate_error_handling,
            self.validate_azure_function_compatibility
        ]
        
        for step in validation_steps:
            try:
                step()
            except Exception as e:
                logging.error(f"Validation step failed with exception: {e}")
        
        # Generate final report
        return self.generate_deployment_report()


if __name__ == '__main__':
    validator = DeploymentValidator()
    report = validator.run_full_validation()
    
    # Exit with appropriate code
    exit_code = 0 if report['ready_for_deployment'] else 1
    exit(exit_code)
