"""
Test suite for source file cleanup functionality
Tests the new feature that deletes successfully processed CSV files from source container
"""

import pytest
import logging
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add the shared directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(os.path.dirname(current_dir), 'shared')
sys.path.insert(0, shared_dir)

from blob_manager import cleanup_source_csv_files, delete_blob_file
from file_consolidator import consolidate_files


class TestSourceFileCleanup:
    """Test cases for source file cleanup functionality"""

    def test_cleanup_source_csv_files_success(self):
        """Test successful cleanup of source CSV files"""
        
        # Mock the delete_blob_file function to simulate successful deletions
        with patch('blob_manager.delete_blob_file') as mock_delete:
            mock_delete.return_value = True
            
            test_files = ['file1.csv', 'file2.csv', 'file3.csv']
            container_name = 'test-container'
            
            result = cleanup_source_csv_files(container_name, test_files)
            
            # Verify the function was called for each file
            assert mock_delete.call_count == 3
            
            # Verify the results
            assert result['total_files'] == 3
            assert result['successfully_deleted'] == 3
            assert result['failed_deletions'] == 0
            assert result['deleted_files'] == test_files
            assert result['failed_files'] == []

    def test_cleanup_source_csv_files_partial_failure(self):
        """Test cleanup with some files failing to delete"""
        
        # Mock delete_blob_file to fail for the second file
        def mock_delete_side_effect(container, filename):
            return filename != 'file2.csv'
        
        with patch('blob_manager.delete_blob_file', side_effect=mock_delete_side_effect):
            test_files = ['file1.csv', 'file2.csv', 'file3.csv']
            container_name = 'test-container'
            
            result = cleanup_source_csv_files(container_name, test_files)
            
            # Verify the results
            assert result['total_files'] == 3
            assert result['successfully_deleted'] == 2
            assert result['failed_deletions'] == 1
            assert 'file1.csv' in result['deleted_files']
            assert 'file3.csv' in result['deleted_files']
            assert 'file2.csv' in result['failed_files']

    def test_cleanup_source_csv_files_empty_list(self):
        """Test cleanup with empty file list"""
        
        with patch('blob_manager.delete_blob_file') as mock_delete:
            result = cleanup_source_csv_files('test-container', [])
            
            # Verify no deletion attempts were made
            mock_delete.assert_not_called()
            
            # Verify the results
            assert result['total_files'] == 0
            assert result['successfully_deleted'] == 0
            assert result['failed_deletions'] == 0
            assert result['deleted_files'] == []
            assert result['failed_files'] == []

    def test_consolidate_files_returns_tuple(self):
        """Test that consolidate_files now returns both DataFrame and successful files list"""
        
        # Mock the required functions
        with patch('file_consolidator.download_csv_from_blob') as mock_download:
            # Create a mock DataFrame
            mock_df = Mock()
            mock_df.shape = (100, 5)
            mock_df.columns = ['col1', 'col2', 'col3', 'col4', 'col5']
            mock_df.with_columns = Mock(return_value=mock_df)
            
            mock_download.return_value = mock_df
            
            # Test with single file
            csv_files = ['test_file.csv']
            container_name = 'test-container'
            
            result = consolidate_files(csv_files, container_name)
            
            # Verify it returns a tuple
            assert isinstance(result, tuple)
            assert len(result) == 2
            
            df, successful_files = result
            
            # Verify the successful files list
            assert successful_files == ['test_file.csv']

    def test_consolidate_files_multiple_files_with_failures(self):
        """Test consolidate_files with multiple files where some fail"""
        
        # Mock download_csv_from_blob to fail for the second file
        def mock_download_side_effect(filename, container):
            if filename == 'file2.csv':
                raise Exception("Download failed")
            
            mock_df = Mock()
            mock_df.shape = (50, 3)
            mock_df.columns = ['col1', 'col2', 'col3']
            mock_df.with_columns = Mock(return_value=mock_df)
            return mock_df
        
        with patch('file_consolidator.download_csv_from_blob', side_effect=mock_download_side_effect):
            with patch('polars.concat') as mock_concat:
                mock_final_df = Mock()
                mock_final_df.shape = (100, 4)  # After adding metadata columns
                mock_final_df.columns = ['col1', 'col2', 'col3', '_source_file']  # Make columns iterable
                mock_final_df.with_columns = Mock(return_value=mock_final_df)
                mock_concat.return_value = mock_final_df
                
                csv_files = ['file1.csv', 'file2.csv', 'file3.csv']
                container_name = 'test-container'
                
                result = consolidate_files(csv_files, container_name)
                
                # Verify it returns a tuple
                assert isinstance(result, tuple)
                df, successful_files = result
                
                # Verify only successful files are in the list
                assert len(successful_files) == 2
                assert 'file1.csv' in successful_files
                assert 'file3.csv' in successful_files
                assert 'file2.csv' not in successful_files


if __name__ == "__main__":
    # Configure logging for test output
    logging.basicConfig(level=logging.INFO)
    
    # Run the tests
    pytest.main([__file__, "-v"])
