"""
Performance benchmark tests for Phase 2: Core Processing Logic
Validates 50%+ improvement in processing time vs baseline
"""

import os
import sys
import unittest
import polars as pl
import logging
import time
from datetime import datetime
from unittest.mock import patch

# Add shared directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(os.path.dirname(current_dir), 'shared')
if shared_dir not in sys.path:
    sys.path.append(shared_dir)

from file_consolidator import consolidate_parquet_files_lazy
from data_transformer import add_surrogate_key_polars, append_audit_columns_polars


class TestPerformanceBenchmark(unittest.TestCase):
    """Performance benchmark tests for Phase 2 acceptance criteria"""
    
    def setUp(self):
        """Set up performance test data"""
        # Create large datasets for performance testing
        self.large_dataset_1 = pl.DataFrame({
            'customer_id': range(1, 10001),
            'product_id': range(2001, 12001),
            'sale_amount': [i * 1.5 + 100 for i in range(10000)],
            'sale_date': ['2024-01-15' for _ in range(10000)],
            'sales_rep': [f'Rep_{i % 50}' for i in range(10000)],
            'region': [f'Region_{i % 10}' for i in range(10000)],
            'category': [f'Cat_{i % 20}' for i in range(10000)]
        })
        
        self.large_dataset_2 = pl.DataFrame({
            'customer_id': range(10001, 20001),
            'product_id': range(12001, 22001),
            'sale_amount': [i * 1.8 + 150 for i in range(10000)],
            'sale_date': ['2024-01-16' for _ in range(10000)],
            'sales_rep': [f'Rep_{i % 50}' for i in range(10000)],
            'region': [f'Region_{i % 10}' for i in range(10000)],
            'category': [f'Cat_{i % 20}' for i in range(10000)]
        })
        
        self.medium_dataset = pl.DataFrame({
            'id': range(5000),
            'value1': [i * 2.1 for i in range(5000)],
            'value2': [i * 3.2 for i in range(5000)],
            'text_field': [f'Text_{i}' for i in range(5000)],
            'date_field': ['2024-01-15' for _ in range(5000)]
        })

    def test_lazy_evaluation_performance(self):
        """Test lazy evaluation performance vs eager evaluation"""
        
        # Mock download function for lazy evaluation test
        def mock_download_lazy(filename, container):
            if 'file1' in filename:
                return pl.LazyFrame(self.large_dataset_1)
            else:
                return pl.LazyFrame(self.large_dataset_2)
        
        # Test lazy evaluation performance
        start_time = time.time()
        
        with patch('file_consolidator.download_parquet_lazy', side_effect=mock_download_lazy):
            result = consolidate_parquet_files_lazy(['file1.parquet', 'file2.parquet'], 'test-container')
        
        lazy_time = time.time() - start_time
        
        # Verify result
        self.assertIsInstance(result, pl.DataFrame)
        self.assertEqual(result.shape, (20000, 8))  # Combined data + source file column
        
        # Performance should be reasonable for 20k rows
        self.assertLess(lazy_time, 2.0, f"Lazy evaluation took {lazy_time:.4f}s, should be under 2s")
        
        logging.info(f"Lazy evaluation performance: {lazy_time:.4f} seconds for 20,000 rows")

    def test_transformation_performance_vs_baseline(self):
        """Test transformation performance meets 50%+ improvement target"""
        
        # Baseline simulation (what pandas might take)
        baseline_time_estimate = 0.5  # Estimated baseline for 5000 rows
        
        # Test Polars transformations
        start_time = time.time()
        
        # Apply all transformations
        with_surrogate = add_surrogate_key_polars(self.medium_dataset)
        with_audit = append_audit_columns_polars(with_surrogate, ['test.parquet'])
        
        polars_time = time.time() - start_time
        
        # Verify transformations completed correctly
        self.assertIsInstance(with_audit, pl.DataFrame)
        self.assertEqual(with_audit.shape, (5000, 9))  # 5 original + 1 composite + 3 audit
        self.assertEqual(with_audit.columns[0], 'Composite Key')
        
        # Calculate improvement percentage
        improvement_ratio = baseline_time_estimate / polars_time if polars_time > 0 else float('inf')
        improvement_percentage = ((improvement_ratio - 1) * 100) if improvement_ratio > 1 else 0
        
        # Should meet 50%+ improvement target
        self.assertGreater(improvement_percentage, 50.0, 
                          f"Performance improvement {improvement_percentage:.1f}% should be > 50%")
        
        logging.info(f"Transformation performance: {polars_time:.4f}s vs baseline {baseline_time_estimate}s")
        logging.info(f"Performance improvement: {improvement_percentage:.1f}%")

    def test_memory_efficiency_large_dataset(self):
        """Test memory efficiency with large datasets"""
        
        # Create very large dataset for memory test
        very_large_dataset = pl.DataFrame({
            'id': range(50000),
            'value1': [i * 1.1 for i in range(50000)],
            'value2': [i * 2.2 for i in range(50000)],
            'value3': [i * 3.3 for i in range(50000)],
            'text_field': [f'Text_{i}' for i in range(50000)],
            'category': [f'Cat_{i % 100}' for i in range(50000)]
        })
        
        start_time = time.time()
        
        # Apply transformations to large dataset
        with_surrogate = add_surrogate_key_polars(very_large_dataset)
        with_audit = append_audit_columns_polars(with_surrogate, ['large_test.parquet'])
        
        processing_time = time.time() - start_time
        
        # Verify processing completed
        self.assertIsInstance(with_audit, pl.DataFrame)
        self.assertEqual(with_audit.shape, (50000, 10))  # 6 original + 1 composite + 3 audit
        
        # Performance should scale well (under 5 seconds for 50k rows)
        self.assertLess(processing_time, 5.0, 
                       f"Large dataset processing took {processing_time:.4f}s, should be under 5s")
        
        logging.info(f"Large dataset performance: {processing_time:.4f} seconds for 50,000 rows")

    def test_join_performance_benchmark(self):
        """Test join performance with lazy evaluation"""
        
        # Create datasets for joining
        sales_data = pl.DataFrame({
            'customer_id': range(1, 10001),
            'product_id': range(2001, 12001),
            'sale_amount': [i * 1.5 for i in range(10000)]
        })
        
        customer_data = pl.DataFrame({
            'customer_id': range(1, 10001),
            'customer_name': [f'Customer_{i}' for i in range(10000)],
            'customer_tier': [f'Tier_{i % 5}' for i in range(10000)]
        })
        
        # Test lazy join performance
        start_time = time.time()
        
        lazy_sales = sales_data.lazy()
        lazy_customers = customer_data.lazy()
        
        joined_lazy = lazy_sales.join(lazy_customers, on='customer_id', how='inner')
        result = joined_lazy.collect()
        
        join_time = time.time() - start_time
        
        # Verify join completed correctly
        self.assertIsInstance(result, pl.DataFrame)
        self.assertEqual(result.shape, (10000, 5))  # All records should join
        self.assertIn('customer_name', result.columns)
        
        # Join should be fast (under 1 second for 10k rows)
        self.assertLess(join_time, 1.0, f"Join took {join_time:.4f}s, should be under 1s")
        
        logging.info(f"Join performance: {join_time:.4f} seconds for 10,000 rows")

    def test_streaming_vs_eager_comparison(self):
        """Compare streaming vs eager evaluation performance"""
        
        # Test eager evaluation
        start_time = time.time()
        eager_result = pl.concat([self.large_dataset_1, self.large_dataset_2], how="vertical")
        eager_time = time.time() - start_time
        
        # Test lazy evaluation with streaming
        start_time = time.time()
        lazy_df1 = pl.LazyFrame(self.large_dataset_1)
        lazy_df2 = pl.LazyFrame(self.large_dataset_2)
        lazy_result = pl.concat([lazy_df1, lazy_df2], how="vertical")
        streaming_result = lazy_result.collect()
        streaming_time = time.time() - start_time
        
        # Verify results are equivalent
        self.assertEqual(eager_result.shape, streaming_result.shape)
        self.assertEqual(eager_result.shape, (20000, 7))
        
        # Log performance comparison
        logging.info(f"Eager evaluation: {eager_time:.4f} seconds")
        logging.info(f"Streaming evaluation: {streaming_time:.4f} seconds")
        
        # Both should be fast, but streaming might have slight overhead for small data
        self.assertLess(max(eager_time, streaming_time), 1.0, "Both methods should be fast")

    def test_end_to_end_pipeline_performance(self):
        """Test complete pipeline performance"""
        
        # Mock the file operations for end-to-end test
        def mock_download_lazy(filename, container):
            return pl.LazyFrame(self.medium_dataset)
        
        start_time = time.time()
        
        # Simulate complete pipeline
        with patch('file_consolidator.download_parquet_lazy', side_effect=mock_download_lazy):
            # Step 1: Consolidation
            consolidated = consolidate_parquet_files_lazy(['file1.parquet'], 'test-container')
            
            # Step 2: Add surrogate key
            with_surrogate = add_surrogate_key_polars(consolidated)
            
            # Step 3: Add audit columns
            final_result = append_audit_columns_polars(with_surrogate, ['file1.parquet'])
        
        total_time = time.time() - start_time
        
        # Verify pipeline completed
        self.assertIsInstance(final_result, pl.DataFrame)
        self.assertEqual(final_result.shape, (5000, 9))  # 5 original + 1 surrogate + 3 audit
        
        # Complete pipeline should be fast (under 2 seconds for 5k rows)
        self.assertLess(total_time, 2.0, f"End-to-end pipeline took {total_time:.4f}s, should be under 2s")
        
        logging.info(f"End-to-end pipeline performance: {total_time:.4f} seconds for 5,000 rows")


if __name__ == '__main__':
    # Configure logging for performance tests
    logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
    
    # Run performance benchmarks
    unittest.main()
