"""
Error Handling and Edge Cases Tests for Phase 3: Integration & Testing
Tests robustness, error recovery, and edge case handling
"""

import os
import sys
import unittest
import polars as pl
import logging
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add shared directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(os.path.dirname(current_dir), 'shared')
if shared_dir not in sys.path:
    sys.path.append(shared_dir)

from orchestrator import run_parquet_processing_pipeline, CSVProcessingOrchestrator
from file_consolidator import consolidate_parquet_files_lazy
from data_transformer import add_surrogate_key_polars, append_audit_columns_polars


class TestErrorHandlingEdgeCases(unittest.TestCase):
    """Test error handling and edge cases for robust pipeline operation"""
    
    def setUp(self):
        """Set up test fixtures for edge cases"""
        
        # Empty dataset
        self.empty_dataset = pl.DataFrame({
            'customer_id': [],
            'product_id': [],
            'sale_amount': [],
            'sale_date': [],
            'sales_rep': []
        })
        
        # Single row dataset
        self.single_row_dataset = pl.DataFrame({
            'customer_id': [1001],
            'product_id': [2001],
            'sale_amount': [150.50],
            'sale_date': ['2024-01-15'],
            'sales_rep': ['John Doe']
        })
        
        # Dataset with null values
        self.null_dataset = pl.DataFrame({
            'customer_id': [1001, None, 1003, None, 1005],
            'product_id': [2001, 2002, None, 2004, None],
            'sale_amount': [150.50, None, 75.25, 450.00, None],
            'sale_date': ['2024-01-15', None, '2024-01-15', '2024-01-16', None],
            'sales_rep': ['John Doe', None, 'Bob Wilson', None, 'Alice Brown']
        })
        
        # Dataset with only text columns (no numeric)
        self.text_only_dataset = pl.DataFrame({
            'name': ['Alice', 'Bob', 'Charlie'],
            'category': ['A', 'B', 'C'],
            'description': ['Desc1', 'Desc2', 'Desc3'],
            'status': ['Active', 'Inactive', 'Active']
        })

    def test_empty_dataset_handling(self):
        """Test handling of empty datasets"""
        
        try:
            # Should handle empty dataset gracefully
            result_with_key = add_surrogate_key_polars(self.empty_dataset)
            
            # Verify structure is maintained
            self.assertIsInstance(result_with_key, pl.DataFrame)
            self.assertEqual(result_with_key.height, 0)  # Still empty
            self.assertEqual(result_with_key.columns[0], 'Composite Key')
            
            # Should be able to add audit columns to empty dataset
            result_with_audit = append_audit_columns_polars(result_with_key, ['empty.parquet'])
            self.assertIsInstance(result_with_audit, pl.DataFrame)
            self.assertEqual(result_with_audit.height, 0)
            
            logging.info("Empty dataset handled successfully")
            
        except Exception as e:
            self.fail(f"Empty dataset should be handled gracefully: {e}")

    def test_single_row_dataset_handling(self):
        """Test handling of single row datasets"""
        
        try:
            # Should handle single row dataset
            result_with_key = add_surrogate_key_polars(self.single_row_dataset)
            
            # Verify single row processing
            self.assertIsInstance(result_with_key, pl.DataFrame)
            self.assertEqual(result_with_key.height, 1)
            self.assertEqual(result_with_key.columns[0], 'Composite Key')
            
            # Verify composite key calculation (string concatenation)
            composite_value = result_with_key.select(pl.col('Composite Key')).item()
            self.assertEqual(composite_value, 1)  # Row index starting from 1
            
            # Add audit columns
            result_with_audit = append_audit_columns_polars(result_with_key, ['single.parquet'])
            self.assertEqual(result_with_audit.height, 1)
            self.assertEqual(result_with_audit.width, 9)  # 5 original + 1 composite + 3 audit
            
            logging.info("Single row dataset handled successfully")
            
        except Exception as e:
            self.fail(f"Single row dataset should be handled gracefully: {e}")

    def test_null_values_handling(self):
        """Test handling of datasets with null values"""
        
        try:
            # Should handle null values gracefully
            result_with_key = add_surrogate_key_polars(self.null_dataset)
            
            # Verify null handling
            self.assertIsInstance(result_with_key, pl.DataFrame)
            self.assertEqual(result_with_key.height, 5)
            self.assertEqual(result_with_key.columns[0], 'Composite Key')
            
            # Check that null values are handled in composite key calculation
            composite_values = result_with_key.select(pl.col('Composite Key')).to_series().to_list()

            # Should have valid composite keys even with nulls
            self.assertEqual(len(composite_values), 5)
            self.assertIsNotNone(composite_values[0])  # First row should have valid key
            
            # Add audit columns
            result_with_audit = append_audit_columns_polars(result_with_key, ['null_test.parquet'])
            self.assertEqual(result_with_audit.height, 5)
            
            logging.info("Null values handled successfully")
            
        except Exception as e:
            self.fail(f"Null values should be handled gracefully: {e}")

    def test_text_only_dataset_handling(self):
        """Test handling of datasets with no numeric columns"""
        
        try:
            # Should handle text-only dataset by using row numbers
            result_with_key = add_surrogate_key_polars(self.text_only_dataset)
            
            # Verify text-only handling
            self.assertIsInstance(result_with_key, pl.DataFrame)
            self.assertEqual(result_with_key.height, 3)
            self.assertEqual(result_with_key.columns[0], 'Composite Key')

            # Should use row numbers as composite keys
            composite_values = result_with_key.select(pl.col('Composite Key')).to_series().to_list()
            expected_values = [1, 2, 3]  # Row numbers starting from 1
            self.assertEqual(composite_values, expected_values)
            
            # Add audit columns
            result_with_audit = append_audit_columns_polars(result_with_key, ['text_only.parquet'])
            self.assertEqual(result_with_audit.height, 3)
            
            logging.info("Text-only dataset handled successfully")
            
        except Exception as e:
            self.fail(f"Text-only dataset should be handled gracefully: {e}")

    @patch('orchestrator.list_parquet_files_in_container')
    @patch('orchestrator.upload_parquet_to_blob')
    def test_blob_upload_failure_handling(self, mock_upload, mock_list_files):
        """Test handling of blob upload failures"""
        
        mock_list_files.return_value = ['test.parquet']
        mock_upload.return_value = False  # Simulate upload failure
        
        with patch('orchestrator.consolidate_parquet_files_lazy', return_value=self.single_row_dataset), \
             patch('orchestrator.get_output_container', return_value='test-output'):
            
            result = run_parquet_processing_pipeline()
            
            # Should complete pipeline but report upload failure
            self.assertEqual(result['status'], 'success')
            self.assertEqual(result['processing_stats']['upload_success'], False)
            
            logging.info("Upload failure handled gracefully")

    @patch('orchestrator.list_parquet_files_in_container')
    def test_consolidation_failure_handling(self, mock_list_files):
        """Test handling of file consolidation failures"""
        
        mock_list_files.return_value = ['test.parquet']
        
        # Mock consolidation failure
        with patch('orchestrator.consolidate_parquet_files_lazy', side_effect=Exception("Consolidation failed")):
            
            result = run_parquet_processing_pipeline()
            
            # Should return error result
            self.assertEqual(result['status'], 'error')
            self.assertIn('Consolidation failed', result['error_message'])
            
            logging.info("Consolidation failure handled gracefully")

    def test_invalid_file_patterns_handling(self):
        """Test handling of invalid file patterns"""
        
        orchestrator = CSVProcessingOrchestrator({})
        
        # Mock no files found with any pattern
        with patch('orchestrator.list_parquet_files_in_container', return_value=[]), \
             patch('orchestrator.list_csv_files_in_container', return_value=[]):
            
            result = orchestrator.process_pipeline(file_format='auto')
            
            # Should return appropriate error
            self.assertEqual(result['status'], 'error')
            self.assertIn('No CSV or Parquet files found', result['error_message'])
            
            logging.info("Invalid file patterns handled gracefully")

    def test_memory_pressure_simulation(self):
        """Test behavior under simulated memory pressure"""
        
        # Create a very large dataset to simulate memory pressure
        large_dataset = pl.DataFrame({
            'id': range(500000),  # 500k rows
            'value1': [i * 1.1 for i in range(500000)],
            'value2': [i * 2.2 for i in range(500000)],
            'text_field': [f'Text_{i}' for i in range(500000)],
            'category': [f'Cat_{i % 1000}' for i in range(500000)]
        })
        
        try:
            # Should handle large dataset without memory errors
            result_with_key = add_surrogate_key_polars(large_dataset)
            
            # Verify processing completed
            self.assertIsInstance(result_with_key, pl.DataFrame)
            self.assertEqual(result_with_key.height, 500000)
            
            logging.info("Large dataset (500k rows) processed successfully")
            
        except MemoryError:
            self.fail("Should handle large datasets without memory errors using Polars streaming")
        except Exception as e:
            # Other exceptions might be acceptable depending on system limits
            logging.warning(f"Large dataset processing encountered: {e}")

    def test_concurrent_processing_simulation(self):
        """Test behavior under simulated concurrent processing"""
        
        # Simulate multiple concurrent processing requests
        datasets = [self.single_row_dataset, self.null_dataset, self.text_only_dataset]
        
        results = []
        for i, dataset in enumerate(datasets):
            try:
                result = add_surrogate_key_polars(dataset)
                results.append(result)
                logging.info(f"Concurrent processing {i+1}/3 completed")
            except Exception as e:
                self.fail(f"Concurrent processing {i+1} failed: {e}")
        
        # All should complete successfully
        self.assertEqual(len(results), 3)
        for result in results:
            self.assertIsInstance(result, pl.DataFrame)
            self.assertEqual(result.columns[0], 'Composite Key')

    def test_malformed_data_handling(self):
        """Test handling of malformed or inconsistent data"""

        # Create dataset with mixed data types and inconsistencies using proper Polars construction
        malformed_dataset = pl.DataFrame({
            'text_column': ['text1', 'text2', 'text3', None, 'text5'],
            'inconsistent_dates': ['2024-01-15', 'invalid-date', '2024-02-30', None, '2024-01-01'],
            'numeric_column': [100, 200, 300, None, 500]
        })
        
        try:
            # Should handle malformed data gracefully
            result_with_key = add_surrogate_key_polars(malformed_dataset)
            
            # Verify processing completed
            self.assertIsInstance(result_with_key, pl.DataFrame)
            self.assertEqual(result_with_key.height, 5)
            self.assertEqual(result_with_key.columns[0], 'Composite Key')
            
            # Add audit columns
            result_with_audit = append_audit_columns_polars(result_with_key, ['malformed.parquet'])
            self.assertEqual(result_with_audit.height, 5)
            
            logging.info("Malformed data handled successfully")
            
        except Exception as e:
            self.fail(f"Malformed data should be handled gracefully: {e}")

    def test_timeout_edge_case(self):
        """Test processing completes within reasonable time limits"""
        
        import time
        
        # Test with medium dataset to ensure reasonable processing time
        start_time = time.time()
        
        try:
            result_with_key = add_surrogate_key_polars(self.null_dataset)
            result_with_audit = append_audit_columns_polars(result_with_key, ['timeout_test.parquet'])
            
            processing_time = time.time() - start_time
            
            # Should complete very quickly for small datasets
            self.assertLess(processing_time, 1.0, f"Processing took {processing_time:.4f}s, should be under 1s")
            
            # Verify results
            self.assertIsInstance(result_with_audit, pl.DataFrame)
            self.assertEqual(result_with_audit.height, 5)
            
            logging.info(f"Timeout edge case: {processing_time:.4f}s for 5 rows")
            
        except Exception as e:
            self.fail(f"Timeout edge case failed: {e}")


if __name__ == '__main__':
    # Configure logging for error handling tests
    logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
    
    # Run error handling and edge case tests
    unittest.main()
