"""
End-to-End Integration Tests for Phase 3: Integration & Testing
Tests complete pipeline with various file sizes and real-world scenarios
"""

import os
import sys
import unittest
import polars as pl
import logging
import time
import tempfile
from io import BytesIO
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add shared directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(os.path.dirname(current_dir), 'shared')
if shared_dir not in sys.path:
    sys.path.append(shared_dir)

from orchestrator import (
    run_parquet_processing_pipeline, 
    run_processing_pipeline,
    CSVProcessingOrchestrator
)


class TestEndToEndIntegration(unittest.TestCase):
    """End-to-end integration tests for complete pipeline"""
    
    def setUp(self):
        """Set up test fixtures with various file sizes"""
        
        # Small file (< 1GB equivalent in rows)
        self.small_dataset = pl.DataFrame({
            'customer_id': range(1, 1001),
            'product_id': range(2001, 3001),
            'sale_amount': [i * 1.5 + 50 for i in range(1000)],
            'sale_date': ['2024-01-15' for _ in range(1000)],
            'sales_rep': [f'Rep_{i % 10}' for i in range(1000)]
        })
        
        # Medium file (1-5GB equivalent)
        self.medium_dataset = pl.DataFrame({
            'customer_id': range(1, 25001),
            'product_id': range(2001, 27001),
            'sale_amount': [i * 2.1 + 75 for i in range(25000)],
            'sale_date': ['2024-01-16' for _ in range(25000)],
            'sales_rep': [f'Rep_{i % 25}' for i in range(25000)]
        })
        
        # Large file (5GB+ equivalent)
        self.large_dataset = pl.DataFrame({
            'customer_id': range(1, 100001),
            'product_id': range(2001, 102001),
            'sale_amount': [i * 1.8 + 100 for i in range(100000)],
            'sale_date': ['2024-01-17' for _ in range(100000)],
            'sales_rep': [f'Rep_{i % 50}' for i in range(100000)]
        })

    @patch('orchestrator.list_parquet_files_in_container')
    @patch('orchestrator.upload_parquet_to_blob')
    @patch('orchestrator.SOURCE_CONTAINER', 'test-source')
    @patch('orchestrator.get_output_container')
    def test_small_file_processing_under_1gb(self, mock_get_output, mock_upload, mock_list_files):
        """Test processing files under 1GB - should be very fast"""
        
        mock_get_output.return_value = 'test-output'
        mock_list_files.return_value = ['FIMASTSALES_01-15-24_small.parquet']
        mock_upload.return_value = True
        
        # Mock the consolidation to return our small dataset
        with patch('orchestrator.consolidate_parquet_files_lazy', return_value=self.small_dataset):
            
            start_time = time.time()
            result = run_parquet_processing_pipeline()
            processing_time = time.time() - start_time
            
            # Assertions
            self.assertEqual(result['status'], 'success')
            self.assertEqual(result['processing_engine'], 'Polars')
            self.assertIn('final_shape', result)
            
            # Performance: Small files should process very quickly (under 1 second)
            self.assertLess(processing_time, 1.0, 
                           f"Small file processing took {processing_time:.4f}s, should be under 1s")
            
            logging.info(f"Small file processing: {processing_time:.4f}s for 1,000 rows")

    @patch('orchestrator.list_parquet_files_in_container')
    @patch('orchestrator.upload_parquet_to_blob')
    @patch('orchestrator.SOURCE_CONTAINER', 'test-source')
    @patch('orchestrator.get_output_container')
    def test_medium_file_processing_1_5gb(self, mock_get_output, mock_upload, mock_list_files):
        """Test processing files 1-5GB - should maintain good performance"""
        
        mock_get_output.return_value = 'test-output'
        mock_list_files.return_value = ['FIMASTSALES_01-16-24_medium.parquet']
        mock_upload.return_value = True
        
        # Mock the consolidation to return our medium dataset
        with patch('orchestrator.consolidate_parquet_files_lazy', return_value=self.medium_dataset):
            
            start_time = time.time()
            result = run_parquet_processing_pipeline()
            processing_time = time.time() - start_time
            
            # Assertions
            self.assertEqual(result['status'], 'success')
            self.assertEqual(result['processing_engine'], 'Polars')
            
            # Performance: Medium files should still be fast (under 5 seconds)
            self.assertLess(processing_time, 5.0, 
                           f"Medium file processing took {processing_time:.4f}s, should be under 5s")
            
            logging.info(f"Medium file processing: {processing_time:.4f}s for 25,000 rows")

    @patch('orchestrator.list_parquet_files_in_container')
    @patch('orchestrator.upload_parquet_to_blob')
    @patch('orchestrator.SOURCE_CONTAINER', 'test-source')
    @patch('orchestrator.get_output_container')
    def test_large_file_processing_5gb_plus(self, mock_get_output, mock_upload, mock_list_files):
        """Test processing files 5GB+ - should stay under memory limits"""
        
        mock_get_output.return_value = 'test-output'
        mock_list_files.return_value = ['FIMASTSALES_01-17-24_large.parquet']
        mock_upload.return_value = True
        
        # Mock the consolidation to return our large dataset
        with patch('orchestrator.consolidate_parquet_files_lazy', return_value=self.large_dataset):
            
            start_time = time.time()
            result = run_parquet_processing_pipeline()
            processing_time = time.time() - start_time
            
            # Assertions
            self.assertEqual(result['status'], 'success')
            self.assertEqual(result['processing_engine'], 'Polars')
            
            # Performance: Large files should complete within reasonable time (under 10 seconds)
            self.assertLess(processing_time, 10.0, 
                           f"Large file processing took {processing_time:.4f}s, should be under 10s")
            
            logging.info(f"Large file processing: {processing_time:.4f}s for 100,000 rows")

    @patch('orchestrator.list_parquet_files_in_container')
    @patch('orchestrator.upload_parquet_to_blob')
    @patch('orchestrator.SOURCE_CONTAINER', 'test-source')
    @patch('orchestrator.get_output_container')
    def test_multiple_file_consolidation(self, mock_get_output, mock_upload, mock_list_files):
        """Test consolidation of multiple files with lazy evaluation"""
        
        mock_get_output.return_value = 'test-output'
        mock_list_files.return_value = [
            'FIMASTSALES_01-15-24_part1.parquet',
            'FIMASTSALES_01-15-24_part2.parquet',
            'FIMASTSALES_01-15-24_part3.parquet'
        ]
        mock_upload.return_value = True
        
        # Create combined dataset for consolidation result
        combined_dataset = pl.concat([
            self.small_dataset,
            self.small_dataset.with_columns([
                (pl.col('customer_id') + 1000).alias('customer_id')
            ]),
            self.small_dataset.with_columns([
                (pl.col('customer_id') + 2000).alias('customer_id')
            ])
        ], how="vertical")
        
        with patch('orchestrator.consolidate_parquet_files_lazy', return_value=combined_dataset):
            
            start_time = time.time()
            result = run_parquet_processing_pipeline()
            processing_time = time.time() - start_time
            
            # Assertions
            self.assertEqual(result['status'], 'success')
            self.assertEqual(result['processing_engine'], 'Polars')
            
            # Multiple file consolidation should be efficient
            self.assertLess(processing_time, 3.0, 
                           f"Multiple file processing took {processing_time:.4f}s, should be under 3s")
            
            logging.info(f"Multiple file consolidation: {processing_time:.4f}s for 3,000 rows")

    def test_orchestrator_auto_detection_parquet_priority(self):
        """Test orchestrator auto-detection prioritizes Parquet over CSV"""
        
        orchestrator = CSVProcessingOrchestrator({})
        
        # Mock both Parquet and CSV files available
        with patch('orchestrator.list_parquet_files_in_container') as mock_parquet_list, \
             patch('orchestrator.list_csv_files_in_container') as mock_csv_list, \
             patch('orchestrator.run_parquet_processing_pipeline') as mock_parquet_pipeline:
            
            mock_parquet_list.return_value = ['file1.parquet']
            mock_csv_list.return_value = ['file1.csv']
            mock_parquet_pipeline.return_value = {'status': 'success', 'format': 'parquet'}
            
            result = orchestrator.process_pipeline(file_format='auto')
            
            # Should choose Parquet over CSV
            self.assertEqual(result['status'], 'success')
            mock_parquet_pipeline.assert_called_once()
            mock_csv_list.assert_not_called()  # Should not even check CSV if Parquet found

    def test_orchestrator_csv_fallback(self):
        """Test orchestrator falls back to CSV when no Parquet files found"""
        
        orchestrator = CSVProcessingOrchestrator({})
        
        # Mock no Parquet files, but CSV files available
        with patch('orchestrator.list_parquet_files_in_container') as mock_parquet_list, \
             patch('orchestrator.list_csv_files_in_container') as mock_csv_list, \
             patch('orchestrator.run_processing_pipeline') as mock_csv_pipeline:
            
            mock_parquet_list.return_value = []  # No Parquet files
            mock_csv_list.return_value = ['file1.csv']
            mock_csv_pipeline.return_value = {'status': 'success', 'format': 'csv'}
            
            result = orchestrator.process_pipeline(file_format='auto')
            
            # Should fall back to CSV
            self.assertEqual(result['status'], 'success')
            mock_csv_pipeline.assert_called_once()

    def test_error_handling_no_files_found(self):
        """Test error handling when no files are found"""
        
        orchestrator = CSVProcessingOrchestrator({})
        
        # Mock no files found
        with patch('orchestrator.list_parquet_files_in_container', return_value=[]), \
             patch('orchestrator.list_csv_files_in_container', return_value=[]):
            
            result = orchestrator.process_pipeline(file_format='auto')
            
            # Should return error
            self.assertEqual(result['status'], 'error')
            self.assertIn('No CSV or Parquet files found', result['error_message'])

    def test_memory_usage_stays_under_1gb(self):
        """Test that memory usage remains under 1GB throughout processing"""
        
        # This is a conceptual test - in practice, you'd use memory profiling tools
        # For now, we'll test with large datasets and ensure no memory errors
        
        very_large_dataset = pl.DataFrame({
            'id': range(200000),  # 200k rows
            'value1': [i * 1.1 for i in range(200000)],
            'value2': [i * 2.2 for i in range(200000)],
            'value3': [i * 3.3 for i in range(200000)],
            'text_field': [f'Text_{i}' for i in range(200000)],
            'category': [f'Cat_{i % 100}' for i in range(200000)]
        })
        
        # Mock processing with very large dataset
        with patch('orchestrator.list_parquet_files_in_container', return_value=['large.parquet']), \
             patch('orchestrator.consolidate_parquet_files_lazy', return_value=very_large_dataset), \
             patch('orchestrator.upload_parquet_to_blob', return_value=True), \
             patch('orchestrator.get_output_container', return_value='test-output'):
            
            try:
                result = run_parquet_processing_pipeline()
                
                # Should complete successfully without memory errors
                self.assertEqual(result['status'], 'success')
                logging.info("Large dataset processing completed without memory issues")
                
            except MemoryError:
                self.fail("Processing should not run out of memory with Polars streaming")

    def test_timeout_compliance_under_10_minutes(self):
        """Test that processing completes within Azure Function timeout limits"""
        
        # Azure Functions have a 10-minute timeout limit
        timeout_limit = 600  # 10 minutes in seconds
        
        # Test with medium dataset to simulate realistic processing time
        with patch('orchestrator.list_parquet_files_in_container', return_value=['medium.parquet']), \
             patch('orchestrator.consolidate_parquet_files_lazy', return_value=self.medium_dataset), \
             patch('orchestrator.upload_parquet_to_blob', return_value=True), \
             patch('orchestrator.get_output_container', return_value='test-output'):
            
            start_time = time.time()
            result = run_parquet_processing_pipeline()
            processing_time = time.time() - start_time
            
            # Should complete well within timeout limits
            self.assertLess(processing_time, timeout_limit, 
                           f"Processing took {processing_time:.2f}s, should be under {timeout_limit}s")
            
            # For realistic datasets, should be much faster (under 30 seconds)
            self.assertLess(processing_time, 30.0, 
                           f"Realistic processing should be under 30s, took {processing_time:.4f}s")
            
            logging.info(f"Timeout compliance test: {processing_time:.4f}s (limit: {timeout_limit}s)")


if __name__ == '__main__':
    # Configure logging for integration tests
    logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
    
    # Run end-to-end integration tests
    unittest.main()
