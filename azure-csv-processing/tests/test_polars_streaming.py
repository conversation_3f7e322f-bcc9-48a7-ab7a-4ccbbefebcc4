"""
Unit tests for Polars streaming functionality
Phase 1: Foundation Setup - Core streaming functions validation
"""

import os
import sys
import unittest
import polars as pl
import logging
from io import BytesIO
from unittest.mock import Mock, patch, MagicMock

# Add shared directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(os.path.dirname(current_dir), 'shared')
if shared_dir not in sys.path:
    sys.path.append(shared_dir)

from blob_manager import (
    download_parquet_from_blob_streaming,
    download_parquet_lazy,
    upload_parquet_to_blob,
    list_parquet_files_in_container
)
from file_consolidator import consolidate_parquet_files_lazy
from data_transformer import (
    add_surrogate_key_polars,
    append_audit_columns_polars
)

class TestPolarsBlobManager(unittest.TestCase):
    """Test Polars blob manager streaming functions"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create sample Parquet data
        self.sample_data = pl.DataFrame({
            'id': [1, 2, 3, 4, 5],
            'name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
            'value': [10.5, 20.3, 30.1, 40.8, 50.2],
            'category': ['A', 'B', 'A', 'C', 'B']
        })
        
        # Create Parquet bytes for testing
        self.parquet_buffer = BytesIO()
        self.sample_data.write_parquet(self.parquet_buffer, compression='snappy')
        self.parquet_bytes = self.parquet_buffer.getvalue()
        
    @patch('blob_manager.blob_service_client')
    def test_list_parquet_files_in_container(self, mock_blob_service):
        """Test listing Parquet files in container"""
        # Mock blob list
        mock_blob1 = Mock()
        mock_blob1.name = 'file1.parquet'
        mock_blob2 = Mock()
        mock_blob2.name = 'file2.parquet'
        mock_blob3 = Mock()
        mock_blob3.name = 'file3.csv'  # Should be ignored
        
        mock_container = Mock()
        mock_container.list_blobs.return_value = [mock_blob1, mock_blob2, mock_blob3]
        mock_blob_service.get_container_client.return_value = mock_container
        
        # Test function
        result = list_parquet_files_in_container('test-container')
        
        # Assertions
        self.assertEqual(len(result), 2)
        self.assertIn('file1.parquet', result)
        self.assertIn('file2.parquet', result)
        self.assertNotIn('file3.csv', result)
        
    @patch('blob_manager.blob_service_client')
    def test_download_parquet_streaming_success(self, mock_blob_service):
        """Test successful Parquet streaming download"""
        # Mock blob client
        mock_blob_client = Mock()
        mock_download_blob = Mock()
        mock_download_blob.readinto = Mock(side_effect=lambda buf: buf.write(self.parquet_bytes))
        mock_blob_client.download_blob.return_value = mock_download_blob
        mock_blob_service.get_blob_client.return_value = mock_blob_client
        
        # Test function
        result_df = download_parquet_from_blob_streaming('test.parquet', 'test-container')
        
        # Assertions
        self.assertIsInstance(result_df, pl.DataFrame)
        self.assertEqual(result_df.shape, (5, 4))
        self.assertEqual(result_df.columns, ['id', 'name', 'value', 'category'])
        
    @patch('blob_manager.blob_service_client')
    def test_upload_parquet_to_blob_success(self, mock_blob_service):
        """Test successful Parquet upload to blob"""
        # Mock blob client
        mock_blob_client = Mock()
        mock_blob_client.upload_blob.return_value = Mock()
        mock_blob_client.get_blob_properties.return_value = Mock(size=len(self.parquet_bytes))
        mock_blob_service.get_blob_client.return_value = mock_blob_client
        
        # Mock container client
        mock_container_client = Mock()
        mock_container_client.create_container.side_effect = Exception("ContainerAlreadyExists")
        mock_blob_service.get_container_client.return_value = mock_container_client
        
        # Test function
        result = upload_parquet_to_blob(self.sample_data, 'test.parquet', 'test-container')
        
        # Assertions
        self.assertTrue(result)
        mock_blob_client.upload_blob.assert_called_once()


class TestPolarsFileConsolidator(unittest.TestCase):
    """Test Polars file consolidation with lazy evaluation"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.sample_data1 = pl.DataFrame({
            'id': [1, 2, 3],
            'name': ['Alice', 'Bob', 'Charlie'],
            'value': [10.5, 20.3, 30.1]
        })
        
        self.sample_data2 = pl.DataFrame({
            'id': [4, 5, 6],
            'name': ['David', 'Eve', 'Frank'],
            'value': [40.8, 50.2, 60.7]
        })
        
    @patch('file_consolidator.download_parquet_from_blob_streaming')
    def test_consolidate_single_parquet_file(self, mock_download_streaming):
        """Test consolidation of single Parquet file"""
        # Mock streaming download to return a DataFrame directly
        mock_download_streaming.return_value = self.sample_data1

        # Test function
        result = consolidate_parquet_files_lazy(['file1.parquet'], 'test-container')

        # Assertions
        self.assertIsInstance(result, pl.DataFrame)
        self.assertEqual(result.shape, (3, 3))
        mock_download_streaming.assert_called_once_with('file1.parquet', 'test-container')

    @patch('file_consolidator.download_parquet_from_blob_streaming')
    def test_consolidate_multiple_parquet_files(self, mock_download_streaming):
        """Test consolidation of multiple Parquet files"""
        # Prepare DataFrames to be returned by streaming download
        def mock_download_side_effect(filename, container):
            if filename == 'file1.parquet':
                return self.sample_data1
            elif filename == 'file2.parquet':
                return self.sample_data2

        mock_download_streaming.side_effect = mock_download_side_effect

        # Test function (use real pl.concat on DataFrames)
        result = consolidate_parquet_files_lazy(['file1.parquet', 'file2.parquet'], 'test-container')

        # Assertions
        self.assertIsInstance(result, pl.DataFrame)
        self.assertEqual(result.shape, (6, 4))
        self.assertIn("_source_file", result.columns)
        self.assertEqual(mock_download_streaming.call_count, 2)


class TestPolarsDataTransformer(unittest.TestCase):
    """Test Polars data transformation functions"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.sample_data = pl.DataFrame({
            'id': [1, 2, 3, 4, 5],
            'name': ['Alice', 'Bob', 'Charlie', 'David', 'Eve'],
            'value': [10.5, 20.3, 30.1, 40.8, 50.2],
            'score': [85, 92, 78, 88, 95]
        })
        
    def test_add_surrogate_key_polars_with_numeric_columns(self):
        """Test adding surrogate key with numeric columns available"""
        result = add_surrogate_key_polars(self.sample_data)
        
        # Assertions
        self.assertIsInstance(result, pl.DataFrame)
        self.assertEqual(result.shape, (5, 5))  # Original 4 + 1 composite key
        self.assertEqual(result.columns[0], 'Composite Key')  # Should be first column

        # Check that composite key values are row numbers (since required columns are missing)
        expected_values = [1, 2, 3, 4, 5]  # Row numbers starting from 1
        actual_values = result.select(pl.col('Composite Key')).to_series().to_list()
        self.assertEqual(actual_values, expected_values)
            
    def test_add_surrogate_key_polars_no_numeric_columns(self):
        """Test adding surrogate key with no numeric columns"""
        text_data = pl.DataFrame({
            'name': ['Alice', 'Bob', 'Charlie'],
            'category': ['A', 'B', 'C'],
            'description': ['Desc1', 'Desc2', 'Desc3']
        })
        
        result = add_surrogate_key_polars(text_data)
        
        # Assertions
        self.assertIsInstance(result, pl.DataFrame)
        self.assertEqual(result.shape, (3, 4))  # Original 3 + 1 composite key
        self.assertEqual(result.columns[0], 'Composite Key')  # Should be first column

        # Check that composite key values are row numbers starting from 1
        expected_values = [1, 2, 3]
        actual_values = result.select(pl.col('Composite Key')).to_series().to_list()
        self.assertEqual(expected_values, actual_values)
        
    def test_append_audit_columns_polars(self):
        """Test appending audit columns using Polars"""
        source_files = ['test_file_20240101.parquet']
        
        result = append_audit_columns_polars(self.sample_data, source_files)
        
        # Assertions
        self.assertIsInstance(result, pl.DataFrame)
        self.assertEqual(result.shape, (5, 7))  # Original 4 + 3 audit columns
        
        # Check that audit columns are present
        expected_audit_cols = ['Source Date/Time', 'Created Date/Time', 'Modified Date/Time']
        for col in expected_audit_cols:
            self.assertIn(col, result.columns)

        # Check that audit columns are at the end
        last_three_cols = result.columns[-3:]
        self.assertEqual(list(last_three_cols), expected_audit_cols)


class TestMemoryUsage(unittest.TestCase):
    """Test memory usage stays under Phase 1 acceptance criteria (200MB)"""
    
    def test_memory_usage_small_files(self):
        """Test memory usage with small test files"""
        # Create larger test data (but still small for testing)
        large_data = pl.DataFrame({
            'id': range(10000),
            'name': [f'Name_{i}' for i in range(10000)],
            'value': [i * 1.5 for i in range(10000)],
            'category': [f'Cat_{i % 10}' for i in range(10000)]
        })
        
        # Test transformations
        with_surrogate = add_surrogate_key_polars(large_data)
        with_audit = append_audit_columns_polars(with_surrogate, ['test.parquet'])
        
        # Basic assertions (memory usage would need external monitoring)
        self.assertIsInstance(with_audit, pl.DataFrame)
        self.assertEqual(with_audit.shape, (10000, 8))  # 4 original + 1 surrogate + 3 audit
        self.assertEqual(with_audit.columns[0], 'Composite Key')


if __name__ == '__main__':
    # Configure logging for tests
    logging.basicConfig(level=logging.INFO)
    
    # Run tests
    unittest.main()
