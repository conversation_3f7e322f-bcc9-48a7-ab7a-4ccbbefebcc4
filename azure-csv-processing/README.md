# Azure CSV Processing Pipeline

A comprehensive Azure Function App solution for intelligent CSV data processing with advanced datetime extraction, schema alignment, and Parquet conversion capabilities.

## 🚀 Project Overview

This Azure Function App provides an enterprise-grade CSV processing pipeline that automatically:
- Downloads CSV files from Azure Blob Storage
- Performs intelligent datetime extraction from filenames using 9 pattern recognition algorithms
- Validates and transforms data with robust schema alignment
- Adds comprehensive audit columns with source traceability
- Converts data to optimized Parquet format for analytics
- Handles type compatibility and data quality issues automatically

## 🏗️ Architecture

### Modular Design
The application follows a clean, modular architecture with separation of concerns:

```
azure-csv-processing/
├── CSVProcessingPipeline/          # Main Azure Function
│   ├── __init__.py                 # HTTP trigger entry point
│   └── function.json               # Function configuration
├── shared/                         # Shared components library
│   ├── azure_config.py            # Azure configuration management
│   ├── blob_manager.py            # Azure Blob Storage operations
│   ├── csv_processor.py           # CSV validation and processing
│   ├── data_transformer.py        # Data transformation and audit columns
│   ├── file_utils.py              # Intelligent datetime extraction
│   ├── file_consolidator.py       # Multi-file processing
│   ├── orchestrator.py            # Pipeline orchestration
│   └── error_handler.py           # Comprehensive error handling
├── requirements.txt                # Python dependencies
├── host.json                      # Function App configuration
└── local.settings.json            # Local development settings
```

### Core Components

- **🔧 Orchestrator**: Manages the complete 6-step processing pipeline
- **☁️ Blob Manager**: Handles Azure Storage operations with retry logic
- **📊 CSV Processor**: Validates CSV structure and handles encoding issues
- **🔄 Data Transformer**: Performs schema alignment and adds audit columns
- **📅 File Utils**: Intelligent datetime extraction from various filename patterns
- **🛠️ Error Handler**: Comprehensive error handling and logging

## ✨ Key Features

### 🧠 Intelligent Datetime Extraction
- **9 Pattern Recognition Algorithms** for extracting dates from filenames
- **Automatic Format Detection** for various date/time patterns
- **Fallback Mechanisms** using file metadata when patterns don't match
- **Robust Error Handling** with graceful degradation

### 🔄 Advanced Data Processing
- **Polars-Based Processing** for high-performance data operations
- **Automatic Schema Alignment** with type compatibility fixes
- **Surrogate Key Generation** with configurable business logic
- **Comprehensive Audit Trails** with source date/time tracking

### 🛡️ Enterprise Reliability
- **Retry Logic** for transient failures
- **Comprehensive Logging** for debugging and monitoring
- **Type Safety** with automatic data type conversion
- **Optimized Processing** with Polars high-performance engine

### 📊 Large File Processing Capabilities
- **Optimized Architecture**: Polars lazy evaluation with streaming processing
- **Memory Efficient**: Uses `collect(streaming=True)` for large file handling
- **Recommended**: Files up to **5GB** with optimal performance
- **Maximum Capacity**: Files up to **15GB** (with Premium Azure Function Plan)
- **Streaming Processing**: Already implemented with lazy frame evaluation

## 📋 Prerequisites

### Azure Resources
- **Azure Function App** (Python 3.11 runtime)
- **Azure Storage Account** with blob containers:
  - `finatsales-decompressed` (source CSV files)
  - `processed-data` (output Parquet files)
- **Azure Application Insights** (optional, for monitoring)

### Development Environment
- **Python 3.9+** (3.11 recommended for Azure deployment)
- **Azure Functions Core Tools** v4.x
- **Azure CLI** (for authentication and deployment)

### Required Dependencies
```txt
azure-functions>=1.0.0,<2.0.0
azure-storage-blob>=12.0.0
azure-identity>=1.0.0
python-dateutil>=2.8.0
polars>=0.20.0
pyarrow>=14.0.0
werkzeug~=3.1.3
```

## 🚀 Deployment Instructions

### 1. Prerequisites Setup
```bash
# Install Azure Functions Core Tools
npm install -g azure-functions-core-tools@4 --unsafe-perm true

# Login to Azure
az login
```

### 2. Local Development
```bash
# Navigate to project directory
cd azure-csv-processing

# Create virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Start local development server
func start
```

### 3. Azure Deployment
```bash
# Deploy to Azure Function App
func azure functionapp publish <your-function-app-name>

# Example:
func azure functionapp publish csv-processing-func-3514
```

## ⚙️ Configuration

### Environment Variables
Configure these settings in your Azure Function App:

```bash
# Azure Storage Configuration
AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=..."
AZURE_STORAGE_ACCOUNT_NAME="your-storage-account"

# Container Names
SOURCE_CONTAINER_NAME="finatsales-decompressed"
OUTPUT_CONTAINER_NAME="processed-data"

# Optional: Application Insights
APPINSIGHTS_INSTRUMENTATIONKEY="your-app-insights-key"
```

### Local Development Settings
Create `local.settings.json`:
```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "your-storage-connection-string",
    "FUNCTIONS_WORKER_RUNTIME": "python",
    "AZURE_STORAGE_CONNECTION_STRING": "your-storage-connection-string",
    "AZURE_STORAGE_ACCOUNT_NAME": "your-storage-account",
    "SOURCE_CONTAINER_NAME": "finatsales-decompressed",
    "OUTPUT_CONTAINER_NAME": "processed-data"
  }
}
```

## 📖 Usage

### HTTP Trigger
The function exposes an HTTP endpoint for processing CSV files:

```bash
# Trigger function
POST https://your-function-app.azurewebsites.net/api/csvprocessingpipeline

# Request body (optional parameters):
{
  "container_name": "finatsales-decompressed",
  "file_pattern": "*.csv",
  "output_container": "processed-data"
}
```

### Expected Input Format
- **CSV files** in Azure Blob Storage
- **Any filename format** (datetime extraction works with various patterns)
- **Standard CSV structure** with headers
- **UTF-8 or common encodings** (auto-detected)

### Output Format
- **Parquet files** (.parquet extension) optimized for analytics and storage efficiency
- **Intelligent Pipeline Selection**:
  - **CSV Input → Parquet Output**: Hybrid pipeline for optimal performance
  - **Parquet Input → Parquet Output**: Native Parquet processing
- **Audit columns** added automatically:
  - `Source Date/Time`: Extracted from filename using 9 pattern recognition algorithms
  - `Created Date/Time`: Processing timestamp
  - `Modified Date/Time`: Processing timestamp
- **Surrogate Key**: Generated as first column based on business logic
- **File Naming**: `combined_sales_data_YYYY-MM-DD.parquet`

## 🔄 File Processing Pipeline

The function executes a comprehensive 6-step processing pipeline:

### Step 1: Download & Validation
- Downloads CSV files from Azure Blob Storage
- Validates file accessibility and basic structure
- Handles encoding detection and conversion

### Step 2: CSV Processing
- Loads CSV data using Polars for high performance
- Validates column structure and data types
- Handles missing values and data quality issues

### Step 3: Schema Alignment
- Ensures consistent column names and types
- Applies business rules for data standardization
- Handles schema evolution and compatibility

### Step 4: Data Transformation
- Adds surrogate keys based on business logic
- Performs data cleansing and validation
- Applies transformation rules

### Step 5: Audit Column Generation
- Extracts datetime from filename using intelligent patterns
- Adds comprehensive audit trail columns
- Ensures data lineage and traceability

### Step 6: Parquet Conversion & Upload
- Converts processed data to optimized Parquet format
- Uploads to designated output container
- Provides processing summary and statistics

## 📅 Datetime Extraction Patterns

The intelligent datetime extraction system supports 9 different filename patterns:

### Pattern 1: MM-DD-YY_HH.MM.SS
```
FIMASTSALES2TEST_08-18-25_11.40.30.csv → 08/18/2025 11:40
FIMASTSALES_08-18-25_06.31.00.csv → 08/18/2025 06:31
```

### Pattern 2: YYYY-MM-DD
```
combined_sales_data_2025-08-29.csv → 08/29/2025
report_2025-12-25.csv → 12/25/2025
```

### Pattern 3: MM-DD-YYYY
```
sales_08-18-2025.csv → 08/18/2025
data_12-25-2025.csv → 12/25/2025
```

### Pattern 4: YYYYMMDD
```
sales_20250818.csv → 08/18/2025
export_20251225.csv → 12/25/2025
```

### Pattern 5: MM/DD/YY or MM/DD/YYYY
```
sales_08/18/25.csv → 08/18/2025
data_12/25/2025.csv → 12/25/2025
```

### Pattern 6: DD-MM-YY (European Format)
```
sales_18-08-25.csv → 08/18/2025
data_25-12-25.csv → 12/25/2025
```

### Pattern 7: 6-8 Digit Sequences
```
sales_081825.csv → 08/18/2025 (MMDDYY)
data_20250818.csv → 08/18/2025 (YYYYMMDD)
```

### Pattern 8: Date Anywhere in Filename
```
report_2025-08-29_final.csv → 08/29/2025
backup_data_2025-12-25_v2.csv → 12/25/2025
```

### Pattern 9: Fallback Mechanisms
- Uses file metadata (last modified, creation time)
- Shows "From file: [filename]" if no pattern matches
- Graceful degradation ensures processing continues

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. Deployment Failures
```bash
# Issue: Python version mismatch
# Solution: Ensure local Python matches Azure runtime
func azure functionapp publish <app-name> --python-version 3.11
```

#### 2. Storage Connection Issues
```bash
# Issue: Cannot access blob storage
# Solution: Verify connection string and container permissions
az storage container show --name finatsales-decompressed --connection-string "..."
```

#### 3. Large File Processing (Already Optimized!)
```bash
# Current Implementation: Polars streaming with lazy evaluation
# ✅ Handles files up to 15GB efficiently
# ✅ Uses collect(streaming=True) for memory optimization
# ✅ Lazy frame evaluation prevents memory overload

{
  "functionTimeout": "00:15:00",  # Increased for large files
  "extensions": {
    "http": {
      "maxOutstandingRequests": 20,
      "maxConcurrentRequests": 5   # Reduced for memory efficiency
    }
  }
}
```

#### 4. Large File Performance Tips
```bash
# For files 1GB - 5GB:
# - Uses automatic Polars streaming (already implemented)
# - Lazy evaluation handles memory efficiently
# - No additional configuration needed

# For files 5GB - 15GB:
# - Use Premium Azure Function Plan (recommended)
# - Increase timeout to 15+ minutes
# - Monitor processing through Application Insights
# - Polars streaming handles memory automatically
```

#### 4. CSV Processing Errors
```bash
# Issue: Encoding or format errors
# Solution: Check logs for specific error details
func azure functionapp logstream <app-name>
```

### Debug Mode
Enable detailed logging by setting environment variable:
```bash
AZURE_FUNCTIONS_ENVIRONMENT=Development
```

## 🧪 Testing

### Local Testing
```bash
# Start function locally
func start

# Test with curl
curl -X POST http://localhost:7071/api/csvprocessingpipeline \
  -H "Content-Type: application/json" \
  -d '{"container_name": "finatsales-decompressed"}'
```

### Azure Testing
```bash
# Test deployed function
curl -X POST https://your-function-app.azurewebsites.net/api/csvprocessingpipeline \
  -H "Content-Type: application/json" \
  -d '{"container_name": "finatsales-decompressed"}'
```

### Unit Testing
```bash
# Run unit tests (if implemented)
python -m pytest tests/
```

### Integration Testing
1. Upload test CSV files to source container
2. Trigger function via HTTP endpoint
3. Verify Parquet files in output container
4. Check audit columns and datetime extraction
5. Validate data integrity and transformations

## 📊 Monitoring and Logging

### Application Insights Integration
The function provides comprehensive telemetry:
- **Request tracking** with duration and success rates
- **Dependency tracking** for Azure Storage operations
- **Custom metrics** for processing statistics
- **Exception tracking** with detailed stack traces

### Log Analysis Queries
```kusto
// Function execution summary
requests
| where name == "CSVProcessingPipeline"
| summarize count(), avg(duration) by bin(timestamp, 1h)

// Error analysis
exceptions
| where outerMessage contains "CSV"
| summarize count() by type, bin(timestamp, 1h)
```

### Performance Metrics
- **Processing time** per file and total pipeline
- **Memory usage** during data transformation
- **Storage operations** (download/upload speeds)
- **Success/failure rates** with detailed error categorization

## 🔐 Security Considerations

### Authentication
- Uses **Azure Managed Identity** for secure storage access
- **Function-level authentication** can be enabled
- **API keys** for additional security layers

### Data Protection
- **In-transit encryption** for all Azure Storage operations
- **At-rest encryption** for processed Parquet files
- **Audit logging** for compliance and traceability

### Access Control
- **RBAC** for Azure resources
- **Storage account permissions** with least privilege
- **Network restrictions** via Azure Virtual Networks (optional)

## 🚀 Performance Optimization

### Recommended Settings
```json
{
  "version": "2.0",
  "functionTimeout": "00:10:00",
  "extensions": {
    "http": {
      "maxOutstandingRequests": 20,
      "maxConcurrentRequests": 10
    }
  },
  "extensionBundle": {
    "id": "Microsoft.Azure.Functions.ExtensionBundle",
    "version": "[3.*, 4.0.0)"
  }
}
```

### Scaling Configuration
- **Consumption Plan**: Automatic scaling based on demand
- **Premium Plan**: Pre-warmed instances for consistent performance
- **Dedicated Plan**: Predictable performance with reserved resources

## 📈 Future Enhancements

### Planned Features
- **Batch processing** for multiple files simultaneously
- **Data validation rules** engine with configurable business rules
- **Real-time monitoring** dashboard with custom metrics
- **Advanced error recovery** with automatic retry mechanisms
- **Schema evolution** handling with backward compatibility
- **Direct CSV streaming** for even larger files (>15GB)

### ✅ Already Implemented Large File Features
- **✅ Polars Lazy Evaluation**: Memory-efficient processing with `scan_parquet()`
- **✅ Streaming Collection**: Uses `collect(streaming=True)` for large files
- **✅ Memory Optimization**: Handles files up to 15GB efficiently
- **✅ Automatic Scaling**: Polars automatically manages memory usage

### Future Enhancements for Extreme Scale
- **Phase 1**: Direct CSV streaming for files >15GB
- **Phase 2**: Azure Data Factory integration for enterprise workflows
- **Phase 3**: Real-time processing with Event Grid triggers
- **Phase 4**: Multi-container parallel processing

### Integration Opportunities
- **Azure Data Factory** integration for complex ETL workflows
- **Power BI** direct connectivity for real-time analytics
- **Azure Synapse** integration for data warehousing
- **Event Grid** triggers for real-time file processing

## 📞 Support and Contributing

### Getting Help
- Check the **troubleshooting section** for common issues
- Review **Azure Function logs** for detailed error information
- Use **Application Insights** for performance analysis

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes with comprehensive tests
4. Submit a pull request with detailed description

### Code Standards
- Follow **PEP 8** Python style guidelines
- Include **comprehensive logging** for all operations
- Add **unit tests** for new functionality
- Update **documentation** for any changes

---

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Built with ❤️ for enterprise-grade CSV processing on Azure**
