#!/usr/bin/env python3
"""
Script to debug zip archiving configuration for Reynolds entities
"""
import os
import sys

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(current_dir, 'shared')
if shared_dir not in sys.path:
    sys.path.append(shared_dir)

try:
    from azure.data.tables import TableServiceClient
    from azure.identity import DefaultAzureCredential
    AZURE_MODULES_AVAILABLE = True
except ImportError:
    AZURE_MODULES_AVAILABLE = False
    print("❌ Azure modules not available")
    sys.exit(1)

def main():
    print("🔍 Debugging zip archiving configuration for Reynolds entities...")
    
    if not AZURE_MODULES_AVAILABLE:
        print("❌ Azure modules not available")
        return
    
    try:
        # Connect to Azure Table Storage
        storage_account_name = "sagedw"
        table_storage_url = f"https://{storage_account_name}.table.core.windows.net"
        credential = DefaultAzureCredential()
        table_service_client = TableServiceClient(endpoint=table_storage_url, credential=credential)
        table_client = table_service_client.get_table_client(table_name="sftpConfig")
        
        # Check Reynolds entities
        entities_to_check = ['CUSTOMER-ACCTG_', 'FIMASTSALES_']
        
        for entity_key in entities_to_check:
            print(f"\n{'='*60}")
            print(f"🔍 Checking entity: {entity_key}")
            print(f"{'='*60}")
            
            try:
                entity = table_client.get_entity(partition_key="Reynolds", row_key=entity_key)
                
                # Check zip-related properties
                zip_properties = [
                    'ZipMovementEnabled',
                    'ZipSourceFolder', 
                    'ZipArchiveFolder',
                    'ZipFilePattern',
                    'DealerPrefix'
                ]
                
                print(f"📋 Basic Configuration:")
                print(f"   Folder: '{entity.get('Folder', 'NOT_SET')}'")
                print(f"   FilePrefix: '{entity.get('FilePrefix', 'NOT_SET')}'")
                print(f"   isEnabled: '{entity.get('isEnabled', 'NOT_SET')}'")
                
                print(f"\n📦 Zip Archiving Configuration:")
                for prop in zip_properties:
                    value = entity.get(prop, 'NOT_SET')
                    print(f"   {prop}: '{value}'")
                
                # Check if zip archiving should be enabled
                zip_enabled = entity.get('ZipMovementEnabled', False)
                dealer_prefix = entity.get('DealerPrefix', False)
                
                print(f"\n🔧 Computed Configuration:")
                print(f"   ZipMovementEnabled (raw): {zip_enabled}")
                print(f"   DealerPrefix (raw): {dealer_prefix}")
                
                # Handle boolean conversion like the actual code does
                if isinstance(zip_enabled, str):
                    if '@Edm.Boolean' in zip_enabled:
                        zip_enabled_bool = zip_enabled.split('@')[0].lower() == 'true'
                    else:
                        zip_enabled_bool = zip_enabled.lower() in ('true', '1', 'yes')
                else:
                    zip_enabled_bool = bool(zip_enabled)
                
                if isinstance(dealer_prefix, str):
                    if '@Edm.Boolean' in dealer_prefix:
                        dealer_prefix_bool = dealer_prefix.split('@')[0].lower() == 'true'
                    else:
                        dealer_prefix_bool = dealer_prefix.lower() in ('true', '1', 'yes')
                else:
                    dealer_prefix_bool = bool(dealer_prefix)
                
                print(f"   ZipMovementEnabled (parsed): {zip_enabled_bool}")
                print(f"   DealerPrefix (parsed): {dealer_prefix_bool}")
                print(f"   Dealer-aware archiving: {dealer_prefix_bool and zip_enabled_bool}")
                print(f"   Standard archiving: {zip_enabled_bool and not dealer_prefix_bool}")
                
                # Show expected paths
                folder = entity.get('Folder', 'UNKNOWN')
                file_prefix = entity.get('FilePrefix', 'UNKNOWN')
                
                print(f"\n📁 Expected Paths:")
                if zip_enabled_bool:
                    if dealer_prefix_bool:
                        print(f"   Type: Dealer-aware archiving")
                        print(f"   Would look for dealers in: {folder}/*/")
                    else:
                        print(f"   Type: Standard archiving")
                        zip_source = entity.get('ZipSourceFolder')
                        if not zip_source:
                            zip_source = f"{folder}/{file_prefix.lower().rstrip('_')}-compressed"
                        
                        zip_archive = entity.get('ZipArchiveFolder')
                        if not zip_archive:
                            zip_archive = f"{folder}/archive"
                        
                        zip_pattern = entity.get('ZipFilePattern')
                        if not zip_pattern:
                            zip_pattern = f"{file_prefix.rstrip('_')}*.zip"
                        
                        print(f"   Source folder: {zip_source}")
                        print(f"   Archive folder: {zip_archive}")
                        print(f"   File pattern: {zip_pattern}")
                else:
                    print(f"   Type: Archiving disabled")
                
            except Exception as e:
                print(f"❌ Error getting entity {entity_key}: {e}")
        
        print(f"\n{'='*60}")
        print("🔍 Summary")
        print(f"{'='*60}")
        print("Based on the logs, both jobs are using STANDARD ZIP ARCHIVING")
        print("but no zip files are found in the expected compressed folders.")
        print("\nPossible issues:")
        print("1. Zip files are in different folders than expected")
        print("2. Zip file patterns don't match actual filenames")
        print("3. ZipMovementEnabled is false or not set")
        print("4. Zip files were already moved or don't exist")
        
    except Exception as e:
        print(f"❌ Error accessing sftpConfig table: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
