{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"functionAppName": {"type": "string", "defaultValue": "csv-processing-func-3514", "metadata": {"description": "Name of the Function App"}}, "vnetName": {"type": "string", "defaultValue": "swickard-vnet", "metadata": {"description": "Virtual Network name"}}, "subnetName": {"type": "string", "defaultValue": "function-subnet", "metadata": {"description": "Subnet for Function App integration"}}, "storageAccountName": {"type": "string", "defaultValue": "sagedw", "metadata": {"description": "Storage account name"}}, "keyVaultName": {"type": "string", "defaultValue": "swickard-keyvault", "metadata": {"description": "Key Vault name for secrets"}}}, "variables": {"location": "[resourceGroup().location]", "functionAppResourceId": "[resourceId('Microsoft.Web/sites', parameters('functionAppName'))]", "storageAccountResourceId": "[resourceId('Microsoft.Storage/storageAccounts', parameters('storageAccountName'))]", "keyVaultResourceId": "[resourceId('Microsoft.KeyVault/vaults', parameters('keyVaultName'))]"}, "resources": [{"type": "Microsoft.Network/virtualNetworks", "apiVersion": "2021-02-01", "name": "[parameters('vnetName')]", "location": "[variables('location')]", "properties": {"addressSpace": {"addressPrefixes": ["10.0.0.0/16"]}, "subnets": [{"name": "[parameters('subnetName')]", "properties": {"addressPrefix": "********/24", "delegations": [{"name": "Microsoft.Web.serverFarms", "properties": {"serviceName": "Microsoft.Web/serverFarms"}}], "serviceEndpoints": [{"service": "Microsoft.Storage"}, {"service": "Microsoft.KeyVault"}]}}, {"name": "private-endpoint-subnet", "properties": {"addressPrefix": "********/24", "privateEndpointNetworkPolicies": "Disabled"}}]}}, {"type": "Microsoft.Network/networkSecurityGroups", "apiVersion": "2021-02-01", "name": "function-app-nsg", "location": "[variables('location')]", "properties": {"securityRules": [{"name": "AllowHTTPS", "properties": {"protocol": "Tcp", "sourcePortRange": "*", "destinationPortRange": "443", "sourceAddressPrefix": "*", "destinationAddressPrefix": "*", "access": "Allow", "priority": 1000, "direction": "Inbound"}}, {"name": "AllowAzureServices", "properties": {"protocol": "*", "sourcePortRange": "*", "destinationPortRange": "*", "sourceAddressPrefix": "AzureCloud", "destinationAddressPrefix": "*", "access": "Allow", "priority": 1100, "direction": "Inbound"}}, {"name": "DenyAllInbound", "properties": {"protocol": "*", "sourcePortRange": "*", "destinationPortRange": "*", "sourceAddressPrefix": "*", "destinationAddressPrefix": "*", "access": "<PERSON><PERSON>", "priority": 4000, "direction": "Inbound"}}]}}, {"type": "Microsoft.Network/privateEndpoints", "apiVersion": "2021-02-01", "name": "storage-private-endpoint", "location": "[variables('location')]", "dependsOn": ["[resourceId('Microsoft.Network/virtualNetworks', parameters('vnetName'))]"], "properties": {"subnet": {"id": "[resourceId('Microsoft.Network/virtualNetworks/subnets', parameters('vnetName'), 'private-endpoint-subnet')]"}, "privateLinkServiceConnections": [{"name": "storage-connection", "properties": {"privateLinkServiceId": "[variables('storageAccountResourceId')]", "groupIds": ["blob"]}}]}}, {"type": "Microsoft.Network/privateEndpoints", "apiVersion": "2021-02-01", "name": "keyvault-private-endpoint", "location": "[variables('location')]", "dependsOn": ["[resourceId('Microsoft.Network/virtualNetworks', parameters('vnetName'))]"], "properties": {"subnet": {"id": "[resourceId('Microsoft.Network/virtualNetworks/subnets', parameters('vnetName'), 'private-endpoint-subnet')]"}, "privateLinkServiceConnections": [{"name": "keyvault-connection", "properties": {"privateLinkServiceId": "[variables('keyVaultResourceId')]", "groupIds": ["vault"]}}]}}], "outputs": {"vnetId": {"type": "string", "value": "[resourceId('Microsoft.Network/virtualNetworks', parameters('vnetName'))]"}, "subnetId": {"type": "string", "value": "[resourceId('Microsoft.Network/virtualNetworks/subnets', parameters('vnetName'), parameters('subnetName'))]"}, "storagePrivateEndpointId": {"type": "string", "value": "[resourceId('Microsoft.Network/privateEndpoints', 'storage-private-endpoint')]"}}}