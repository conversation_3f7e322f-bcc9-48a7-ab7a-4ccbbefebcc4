#!/usr/bin/env python3
"""
Test script to validate the table-driven configuration system
"""

import sys
import os

# Add the shared directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'azure-csv-processing', 'shared'))

def test_table_configuration():
    """Test the table-driven configuration"""
    print("=== Testing Table-Driven Configuration ===")
    
    try:
        # Import the updated configuration
        from azure_config import (
            Config, get_system_config, get_job_configs, get_job_config,
            get_enabled_jobs, get_source_blob_path, get_output_blob_path,
            refresh_configuration
        )
        
        print("\n1. Testing System Configuration:")
        print(f"   Storage Account: {get_system_config('StorageAccountName')}")
        print(f"   Source Container: {get_system_config('SourceContainer')}")
        print(f"   Output Container: {get_system_config('OutputContainer')}")
        print(f"   Surrogate Key Column: {get_system_config('SurrogateKeyColumn')}")
        print(f"   Audit Columns: {get_system_config('AuditColumns')}")
        print(f"   Default Consolidation: {get_system_config('DefaultConsolidationMethod')}")
        print(f"   Default Timeout: {get_system_config('DefaultTimeout')}")
        print(f"   Default Retries: {get_system_config('DefaultRetries')}")
        
        print("\n2. Testing Job Configurations:")
        enabled_jobs = get_enabled_jobs()
        print(f"   Enabled Jobs: {enabled_jobs}")
        
        for job_key in enabled_jobs:
            job_config = get_job_config(job_key)
            if job_config:
                print(f"\n   Job: {job_key}")
                print(f"     Source: {job_config.get('Source')}")
                print(f"     Folder: {job_config.get('Folder')}")
                print(f"     File Prefix: {job_config.get('FilePrefix')}")
                print(f"     Source Subfolder: {job_config.get('SourceSubfolder')}")
                print(f"     Output Subfolder: {job_config.get('OutputSubfolder')}")
                print(f"     Consolidation Method: {job_config.get('ConsolidationMethod')}")
                print(f"     Processing Order: {job_config.get('ProcessingOrder')}")
                print(f"     Max Retries: {job_config.get('MaxRetries')}")
                print(f"     Timeout Minutes: {job_config.get('TimeoutMinutes')}")
        
        print("\n3. Testing Config Class:")
        # Test system-wide config
        system_config = Config()
        print(f"   System Config - Source Container: {system_config.source_container}")
        print(f"   System Config - Output Container: {system_config.output_container}")
        print(f"   System Config - Audit Columns: {system_config.audit_columns}")
        
        # Test job-specific config
        if enabled_jobs:
            job_key = enabled_jobs[0]
            job_config = Config(job_key)
            print(f"\n   Job Config ({job_key}):")
            print(f"     Source Folder Path: {job_config.source_folder_path}")
            print(f"     Output Folder Path: {job_config.output_folder_path}")
            print(f"     File Patterns: {job_config.file_patterns}")
            print(f"     Consolidation Method: {job_config.consolidation_method}")
            print(f"     Max Retries: {job_config.max_retries}")
            print(f"     Timeout Minutes: {job_config.timeout_minutes}")
            print(f"     Validation Rules: {job_config.validation_rules}")
            print(f"     Transformation Rules: {job_config.transformation_rules}")
        
        print("\n4. Testing Path Generation:")
        test_filename = "FIMASTSALES_test.csv"
        if enabled_jobs:
            job_key = enabled_jobs[0]
            source_path = get_source_blob_path(test_filename, job_key)
            output_path = get_output_blob_path(test_filename, job_key)
            print(f"   Source Path ({job_key}): {source_path}")
            print(f"   Output Path ({job_key}): {output_path}")
        
        print("\n✅ Table-driven configuration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_table_configuration()
    sys.exit(0 if success else 1)
