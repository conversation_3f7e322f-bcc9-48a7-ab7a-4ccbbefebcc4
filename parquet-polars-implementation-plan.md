# Parquet + Polars Implementation Plan for Azure CSV Processing Function App

## 1. Project Overview

### 1.1 Transformation Summary
**Objective**: Transform the current Azure CSV Processing Function App from pandas-based CSV processing to Polars-based Parquet streaming processing to handle large files (up to 15GB) within Azure Consumption Plan constraints.

**Current State**:
- In-memory pandas DataFrame processing
- CSV file format with 3x memory overhead
- Maximum file size: ~400MB
- Memory usage: 180GB for 60GB files

**Target State**:
- Polars lazy evaluation and streaming processing
- Parquet file format with compression
- Maximum file size: ~15GB reliably
- Memory usage: 300-800MB for large files

### 1.2 Key Benefits
- **99.7% Memory Reduction**: From 180GB to 500MB for large files
- **4x Faster Processing**: Columnar format and lazy evaluation
- **Larger File Capacity**: From 400MB to 15GB+ files
- **Better Performance**: Optimized joins and transformations

---

## 2. Prerequisites

### 2.1 Upstream Data Pipeline Changes
**CRITICAL**: Coordinate with data ingestion team to:
- Convert CSV output to Parquet format in the FTP ETL pipeline
- Maintain same file naming conventions (FIMASTSALES patterns)
- Ensure Parquet files are stored in same blob containers
- Test data integrity during format conversion

### 2.2 Development Environment Setup
```bash
# Install required packages locally for development
pip install polars>=0.20.0
pip install pyarrow>=14.0.0
pip install azure-storage-blob>=12.0.0
```

### 2.3 Azure Function App Configuration
- Ensure Python 3.9+ runtime
- Increase function timeout to maximum (10 minutes)
- Monitor memory usage during testing

---

## 3. Implementation Phases

### Phase 1: Foundation Setup (Week 1)
**Deliverables**:
- Updated requirements.txt with Polars dependencies
- New Polars-based blob manager with streaming capabilities
- Basic Parquet file reading functionality
- Unit tests for core streaming functions

**Acceptance Criteria**:
- Successfully download and read small Parquet files (<1GB)
- Memory usage stays under 200MB for test files
- All existing unit tests pass

### Phase 2: Core Processing Logic (Week 2)
**Deliverables**:
- Polars-based file consolidation logic
- Lazy evaluation join operations
- Updated data transformation functions
- Integration tests with sample data

**Acceptance Criteria**:
- Successfully join multiple Parquet files using lazy evaluation
- Transformations (surrogate key, audit columns) work correctly
- Processing time improved by 50%+ vs current implementation

### Phase 3: Integration & Testing (Week 3)
**Deliverables**:
- Complete end-to-end pipeline integration
- Comprehensive testing with various file sizes
- Performance benchmarking and optimization
- Error handling and logging updates

**Acceptance Criteria**:
- Process files up to 5GB successfully
- Memory usage remains under 1GB throughout processing
- All error scenarios handled gracefully

### Phase 4: Deployment & Validation (Week 4)
**Deliverables**:
- Production deployment
- Monitoring and alerting setup
- Documentation updates
- Rollback procedures tested

**Acceptance Criteria**:
- Production deployment successful
- Real-world file processing validated
- Performance metrics meet targets

---

## 4. File-by-File Modification Plan

### 4.1 requirements.txt
**Changes**: Add Polars and update dependencies
```txt
# Existing dependencies
azure-functions>=1.0.0,<2.0.0
azure-storage-blob>=12.0.0
azure-identity>=1.0.0
python-dateutil>=2.8.0

# NEW: Polars and PyArrow dependencies
polars>=0.20.0
pyarrow>=14.0.0

# REMOVE: pandas (will be replaced by Polars)
# pandas>=1.3.0
# numpy>=1.20.0
```

### 4.2 shared/blob_manager.py
**Changes**: Replace pandas CSV reading with Polars Parquet streaming

**Key Modifications**:
```python
import polars as pl
from io import BytesIO

def download_parquet_from_blob_streaming(blob_name, container_name):
    """Download Parquet file with streaming and lazy evaluation"""
    try:
        logging.info(f"Streaming Parquet from blob: {blob_name}")
        
        blob_client = blob_service_client.get_blob_client(
            container=container_name, blob=blob_name
        )
        
        # Stream download to memory buffer
        blob_data = blob_client.download_blob()
        parquet_buffer = BytesIO()
        blob_data.readinto(parquet_buffer)
        parquet_buffer.seek(0)
        
        # Use Polars lazy loading for memory efficiency
        df = pl.read_parquet(parquet_buffer, streaming=True)
        
        logging.info(f"✓ Streamed Parquet: {blob_name} - Shape: {df.shape}")
        return df
        
    except Exception as e:
        logging.error(f"Error streaming Parquet {blob_name}: {e}")
        raise

def upload_parquet_to_blob(df, blob_name, container_name):
    """Upload Polars DataFrame as Parquet to blob storage"""
    try:
        logging.info(f"🔄 Starting Parquet upload - Blob: {blob_name}")
        
        # Convert to Parquet bytes using Polars
        parquet_buffer = BytesIO()
        df.write_parquet(parquet_buffer, compression='snappy')
        parquet_bytes = parquet_buffer.getvalue()
        
        logging.info(f"📝 Parquet size: {len(parquet_bytes)} bytes")
        
        # Upload the Parquet file
        blob_client = blob_service_client.get_blob_client(
            container=container_name, blob=blob_name
        )
        upload_result = blob_client.upload_blob(parquet_bytes, overwrite=True)
        
        logging.info(f"🎉 SUCCESS: Uploaded Parquet: {blob_name}")
        return True
        
    except Exception as e:
        logging.error(f"❌ ERROR uploading Parquet {blob_name}: {e}")
        return False
```

### 4.3 shared/file_consolidator.py
**Changes**: Implement Polars lazy evaluation for file joining

**Key Modifications**:
```python
import polars as pl

def consolidate_parquet_files_lazy(parquet_files, container_name):
    """
    Polars-based file consolidation with lazy evaluation
    Implements boss's recommended approach for memory efficiency
    """
    logging.info("=== POLARS FILE CONSOLIDATION START ===")
    
    file_count = len(parquet_files)
    logging.info(f"Parquet files found: {file_count}")
    
    if file_count == 1:
        # Single file - use lazy loading
        single_file = parquet_files[0]
        logging.info(f"✓ Single Parquet file: {single_file}")
        
        df_lazy = download_parquet_lazy(single_file, container_name)
        df = df_lazy.collect(streaming=True)
        
        logging.info(f"✓ Single file processed - Shape: {df.shape}")
        return df
        
    else:
        # Multiple files - lazy join approach
        logging.info(f"✓ Multiple Parquet files: {file_count}")
        logging.info("Using Polars lazy evaluation for memory efficiency")
        
        lazy_dataframes = []
        
        for i, filename in enumerate(parquet_files, 1):
            try:
                logging.info(f"Processing file {i}/{file_count}: {filename}")
                
                # Create lazy frame
                df_lazy = download_parquet_lazy(filename, container_name)
                
                # Add source tracking
                df_lazy = df_lazy.with_columns([
                    pl.lit(filename).alias("_source_file")
                ])
                
                lazy_dataframes.append(df_lazy)
                logging.info(f"  ✓ File {i} queued for lazy processing")
                
            except Exception as e:
                logging.error(f"  ❌ File {i} failed: {filename} - Error: {str(e)}")
        
        if not lazy_dataframes:
            raise ValueError("All Parquet files failed to load")
        
        # Concatenate with lazy evaluation
        logging.info("Concatenating DataFrames with lazy evaluation...")
        consolidated_lazy = pl.concat(lazy_dataframes, how="vertical")
        
        # Collect with streaming for memory efficiency
        df = consolidated_lazy.collect(streaming=True)
        
        logging.info(f"✓ Consolidation completed - Final shape: {df.shape}")
        return df

def download_parquet_lazy(blob_name, container_name):
    """Download Parquet and return lazy frame"""
    blob_client = blob_service_client.get_blob_client(
        container=container_name, blob=blob_name
    )
    
    blob_data = blob_client.download_blob()
    parquet_buffer = BytesIO()
    blob_data.readinto(parquet_buffer)
    parquet_buffer.seek(0)
    
    # Return lazy frame for memory efficiency
    return pl.scan_parquet(parquet_buffer)
```

### 4.4 shared/data_transformer.py
**Changes**: Convert pandas operations to Polars expressions

**Key Modifications**:
```python
import polars as pl
from datetime import datetime

def add_surrogate_key_polars(df):
    """Add surrogate key using Polars expressions"""
    try:
        logging.info("=== ADDING SURROGATE KEY (POLARS) ===")
        
        original_shape = df.shape
        logging.info(f"Original DataFrame shape: {original_shape}")
        
        # Detect numeric columns
        numeric_columns = [col for col in df.columns 
                          if df[col].dtype in [pl.Int64, pl.Float64, pl.Int32, pl.Float32]]
        
        logging.info(f"Numeric columns detected: {numeric_columns}")
        
        if len(numeric_columns) >= 2:
            # Placeholder logic: sum of first two numeric columns
            df_with_key = df.with_columns([
                (pl.col(numeric_columns[0]) + pl.col(numeric_columns[1]))
                .alias(SURROGATE_KEY_COLUMN)
            ])
        else:
            # Fallback: row number
            df_with_key = df.with_row_count(SURROGATE_KEY_COLUMN, offset=1)
        
        # Move surrogate key to first position
        columns = df_with_key.columns
        surrogate_col = [SURROGATE_KEY_COLUMN]
        other_cols = [col for col in columns if col != SURROGATE_KEY_COLUMN]
        df_reordered = df_with_key.select(surrogate_col + other_cols)
        
        logging.info(f"✓ Surrogate key added - New shape: {df_reordered.shape}")
        return df_reordered
        
    except Exception as e:
        logging.error(f"❌ Error adding surrogate key: {str(e)}")
        raise

def append_audit_columns_polars(df, source_files=None):
    """Append audit columns using Polars expressions"""
    try:
        logging.info("=== APPENDING AUDIT COLUMNS (POLARS) ===")
        
        current_timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
        
        # Extract source datetime from filename if available
        source_datetime_str = current_timestamp
        if source_files and len(source_files) > 0:
            # Use existing filename parsing logic
            file_info = extract_file_info(source_files[0])
            if file_info.get('source_date'):
                source_datetime_str = file_info['source_date']
        
        # Add audit columns using Polars expressions
        df_with_audit = df.with_columns([
            pl.lit(source_datetime_str).alias(AUDIT_COLUMNS[0]),  # Source Date/Time
            pl.lit(current_timestamp).alias(AUDIT_COLUMNS[1]),    # Created Date/Time
            pl.lit(current_timestamp).alias(AUDIT_COLUMNS[2])     # Modified Date/Time
        ])
        
        logging.info(f"✓ Audit columns appended - Final shape: {df_with_audit.shape}")
        return df_with_audit
        
    except Exception as e:
        logging.error(f"❌ Error appending audit columns: {str(e)}")
        raise
```

---

## 5. Code Architecture Changes

### 5.1 Polars Lazy Evaluation Strategy
```python
# Boss's recommended approach implementation
def process_large_parquet_with_polars(file_paths, join_key=None, columns_to_remove=None):
    """
    Implement boss's Polars streaming approach for memory efficiency
    """
    if len(file_paths) == 1:
        # Single file processing
        return (pl.scan_parquet(file_paths[0])
                .collect(streaming=True))
    
    else:
        # Multiple file joining with lazy evaluation
        lazy_frames = []
        
        for i, file_path in enumerate(file_paths):
            df_lazy = (pl.scan_parquet(file_path)
                      .drop(columns_to_remove.get(f'file{i+1}', [])))
            lazy_frames.append(df_lazy)
        
        # Join with lazy evaluation
        if join_key:
            result = lazy_frames[0]
            for df in lazy_frames[1:]:
                result = result.join(df, on=join_key, how="inner")
        else:
            result = pl.concat(lazy_frames, how="vertical")
        
        # Collect with streaming
        return result.collect(streaming=True)
```

### 5.2 Memory Management Strategy
- Use `streaming=True` for all collect operations
- Implement lazy evaluation throughout the pipeline
- Process files in logical chunks when needed
- Monitor memory usage with logging

---

## 6. Testing Strategy

### 6.1 Unit Testing
- Test individual Polars functions with small datasets
- Validate data transformation accuracy
- Test error handling scenarios
- Memory usage validation

### 6.2 Integration Testing
- End-to-end pipeline testing with various file sizes
- Performance benchmarking vs current implementation
- Memory usage monitoring during processing
- Timeout compliance testing

### 6.3 Load Testing
- Test with files up to 15GB
- Concurrent processing scenarios
- Memory pressure testing
- Error recovery testing

---

## 7. Deployment Plan

### 7.1 Pre-Deployment
1. Backup current function app code
2. Test in development environment
3. Validate upstream Parquet file availability
4. Prepare rollback procedures

### 7.2 Deployment Steps
1. Deploy updated requirements.txt
2. Deploy updated Python modules
3. Update function app configuration
4. Validate deployment health
5. Run smoke tests

### 7.3 Post-Deployment
1. Monitor function execution logs
2. Validate processing performance
3. Check memory usage metrics
4. Confirm file processing accuracy

---

## 8. Rollback Plan

### 8.1 Immediate Rollback (< 1 hour)
- Revert to previous function app deployment
- Restore original requirements.txt
- Switch back to CSV processing temporarily

### 8.2 Data Recovery
- Ensure processed files are backed up
- Validate data integrity after rollback
- Re-process any failed files if needed

---

## 9. Timeline Estimates

| Phase | Duration | Key Activities |
|-------|----------|----------------|
| **Phase 1** | 5 days | Foundation setup, basic Parquet reading |
| **Phase 2** | 5 days | Core processing logic, transformations |
| **Phase 3** | 5 days | Integration testing, optimization |
| **Phase 4** | 5 days | Deployment, validation, documentation |
| **Total** | **20 days** | **4 weeks end-to-end** |

---

## 10. Risk Assessment

### 10.1 High Risk Items
- **Upstream Data Format**: Dependency on FTP ETL pipeline changes
- **Memory Usage**: Potential memory spikes during processing
- **Performance**: Processing time may still exceed limits for largest files

### 10.2 Mitigation Strategies
- **Parallel Development**: Work with data ingestion team early
- **Incremental Testing**: Test with progressively larger files
- **Monitoring**: Implement comprehensive logging and alerting
- **Fallback Options**: Maintain CSV processing capability during transition

### 10.3 Success Criteria
- ✅ Process files up to 15GB successfully
- ✅ Memory usage under 1GB throughout processing
- ✅ 50%+ improvement in processing time
- ✅ Zero data integrity issues
- ✅ Successful production deployment

---

## Next Steps

1. **Immediate**: Coordinate with upstream team for Parquet file generation
2. **Week 1**: Begin Phase 1 implementation
3. **Ongoing**: Daily progress reviews and risk assessment
4. **Week 4**: Production deployment and validation

This plan provides a structured approach to implementing your boss's Parquet + Polars recommendation while maintaining system reliability and data integrity.
