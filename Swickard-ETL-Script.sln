Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "extensions", "azure-ftp-etl\extensions.csproj", "{802BBF4D-72DB-C2E1-0050-96CC36ED9E21}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{802BBF4D-72DB-C2E1-0050-96CC36ED9E21}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{802BBF4D-72DB-C2E1-0050-96CC36ED9E21}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{802BBF4D-72DB-C2E1-0050-96CC36ED9E21}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{802BBF4D-72DB-C2E1-0050-96CC36ED9E21}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D7D177C4-1355-4601-9D85-6E0EE8761AB5}
	EndGlobalSection
EndGlobal
