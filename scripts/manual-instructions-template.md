# Manual Steps Required for Network Security Implementation

This file contains the manual steps that <PERSON> needs to complete to finish the network security implementation.

## Overview

The automated implementation has completed successfully for the parts that don't require "User Access Administrator" permissions. However, some role assignments to the managed identity need to be completed manually by <PERSON>.

## What Was Completed Automatically

✅ **Managed Identity Enabled** - System-assigned managed identity is now enabled on the function app
✅ **Storage Network Rules** - Storage account is configured with:
   - Default action: Deny (blocks unauthorized access)
   - Bypass: AzureServices (allows trusted Microsoft services)
✅ **Function App Configuration** - Function app is configured to use managed identity authentication
✅ **Backup Created** - All original configurations have been backed up

## What Requires Manual Completion

⚠️ **Role Assignments** - The managed identity needs storage access roles assigned by someone with "User Access Administrator" permissions.

## Required Role Assignments

Jonathan needs to assign these roles to the function app's managed identity:

### 1. Storage Blob Data Contributor
**Purpose:** Allows the function app to read, write, and delete blob data
**Required for:** Uploading compressed and decompressed files to storage containers

### 2. Storage Table Data Contributor  
**Purpose:** Allows the function app to read, write, and delete table data
**Required for:** Accessing configuration tables and logging

## How to Complete the Role Assignments

### Option 1: Azure CLI Commands (Recommended)

Jonathan should run these commands in Azure CLI or Cloud Shell:

```bash
# The specific commands will be generated in the actual manual instructions file
# when the implementation runs and detects the managed identity Principal ID
```

### Option 2: Azure Portal Method

1. **Navigate to Storage Account:**
   - Go to Azure Portal → Storage Accounts → `sagedw`

2. **Access Control:**
   - Click "Access control (IAM)" in the left menu
   - Click "Add" → "Add role assignment"

3. **Assign Storage Blob Data Contributor:**
   - Role: Select "Storage Blob Data Contributor"
   - Assign access to: "Managed Identity"
   - Managed identity: Select "Function App"
   - Select: Choose "func-swickard-ftp-etl"
   - Click "Save"

4. **Assign Storage Table Data Contributor:**
   - Repeat step 3 but select "Storage Table Data Contributor" as the role

### Option 3: PowerShell Commands

```powershell
# Alternative PowerShell commands (Principal ID will be provided in actual file)
# New-AzRoleAssignment commands will be generated
```

## Verification Steps

After Jonathan completes the role assignments:

1. **Wait 5-10 minutes** for role propagation
2. **Run the test script:**
   ```powershell
   .\test-implementation.ps1
   ```
3. **Check for successful results** - all tests should pass

## Testing the Function

Once role assignments are complete, test the function:

1. **Direct function test:**
   ```
   https://func-swickard-ftp-etl.azurewebsites.net/api/ftpetlpipeline?code=duSM205SkJe2R8cjWyw69fo5l1u6I4Gt55n9Rdlt2mJ_AzFuwyEMIQ==
   ```

2. **Expected results:**
   - Function should execute without authentication errors
   - Files should be uploaded to storage containers
   - Response should show `compressed_uploaded > 0` and `decompressed_uploaded > 0`

## Troubleshooting

### If Role Assignment Fails:
- Verify Jonathan has "Owner" or "User Access Administrator" role on the storage account
- Check that the managed identity Principal ID is correct
- Wait a few minutes and retry

### If Function Still Can't Access Storage:
- Verify the storage account network rules are correct:
  - Default action: Deny
  - Bypass: AzureServices
- Check that the function app is using managed identity settings (not connection string)
- Restart the function app if needed

### If Emergency Rollback is Needed:
```powershell
.\emergency-rollback.ps1 -StorageOnly
```

## Success Criteria

The implementation is complete when:
- ✅ Function app has managed identity enabled
- ✅ Managed identity has both storage roles assigned
- ✅ Storage account blocks unauthorized access but allows Azure services
- ✅ Function app uses managed identity authentication
- ✅ Function executes successfully and uploads files
- ✅ No authentication errors in function logs

## Contact Information

- **For role assignment issues:** Contact the person with User Access Administrator permissions
- **For technical issues:** Reference the test report and troubleshooting guide
- **For emergency rollback:** Use the emergency rollback script

## Files Generated During Implementation

- `backup-YYYYMMDD-HHMMSS/` - Configuration backups
- `test-report-YYYYMMDD-HHMMSS.json` - Test results
- `implementation-report-YYYYMMDD-HHMMSS.json` - Implementation log
- `manual-role-assignments-for-jonathan.md` - Specific role assignment commands

Keep these files until the implementation is verified stable and working correctly.
