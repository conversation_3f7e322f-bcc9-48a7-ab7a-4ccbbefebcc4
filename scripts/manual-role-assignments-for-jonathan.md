# Manual Role Assignments Required - For <PERSON>

**Generated on:** August 26, 2025 5:05 PM
**Managed Identity Principal ID:** bd700248-b5f6-46f0-8546-c96b2e68beee
**Function App:** func-swickard-ftp-etl
**Storage Account:** sagedw

## Implementation Status

✅ **COMPLETED AUTOMATICALLY:**
- Managed identity enabled on function app
- Storage network rules configured (Default: Deny, Bypass: AzureServices)
- Function app configured for managed identity authentication
- Configuration backup created (backup-********-170443)

⚠️ **REQUIRES MANUAL COMPLETION:**
- Storage role assignments to the managed identity

## Missing Role Assignments

The following role assignments could not be completed automatically due to insufficient permissions.
<PERSON> needs to execute these commands:

### Assign Storage Blob Data Contributor

```bash
az role assignment create \
  --assignee "bd700248-b5f6-46f0-8546-c96b2e68beee" \
  --role "Storage Blob Data Contributor" \
  --scope "/subscriptions/70285d5c-842e-47a9-8c4e-e714fa1fd6c1/resourceGroups/Data_API/providers/Microsoft.Storage/storageAccounts/sagedw"
```

### Assign Storage Table Data Contributor

```bash
az role assignment create \
  --assignee "bd700248-b5f6-46f0-8546-c96b2e68beee" \
  --role "Storage Table Data Contributor" \
  --scope "/subscriptions/70285d5c-842e-47a9-8c4e-e714fa1fd6c1/resourceGroups/Data_API/providers/Microsoft.Storage/storageAccounts/sagedw"
```

## Verification Commands

After assigning the roles, Jonathan can verify with:

```bash
# Check all role assignments for the managed identity
az role assignment list \
  --assignee "bd700248-b5f6-46f0-8546-c96b2e68beee" \
  --scope "/subscriptions/70285d5c-842e-47a9-8c4e-e714fa1fd6c1/resourceGroups/Data_API/providers/Microsoft.Storage/storageAccounts/sagedw" \
  --query "[].{Role:roleDefinitionName, Principal:principalName}" \
  --output table
```

## Alternative: Azure Portal Method

1. Navigate to Azure Portal → Storage Accounts → sagedw
2. Click "Access control (IAM)" in the left menu
3. Click "Add" → "Add role assignment"
4. For each missing role:
   - Select the role name ("Storage Blob Data Contributor" or "Storage Table Data Contributor")
   - Assign access to: "Managed Identity"
   - Select: "func-swickard-ftp-etl" (System-assigned)
   - Click "Save"

## After Completion

Once Jonathan has assigned these roles:
1. Wait 5-10 minutes for role propagation
2. Test the function endpoint:
   ```
   https://func-swickard-ftp-etl.azurewebsites.net/api/ftpetlpipeline?code=duSM205SkJe2R8cjWyw69fo5l1u6I4Gt55n9Rdlt2mJ_AzFuwyEMIQ==
   ```
3. Expected results:
   - Function should execute without authentication errors
   - Files should be uploaded to storage containers
   - Response should show `compressed_uploaded > 0` and `decompressed_uploaded > 0`

## Current Configuration Summary

**Function App Settings:**
- AzureWebJobsStorage__accountName: sagedw
- AzureWebJobsStorage__blobServiceUri: https://sagedw.blob.core.windows.net
- AzureWebJobsStorage__tableServiceUri: https://sagedw.table.core.windows.net

**Storage Account Network Rules:**
- Default Action: Deny (blocks unauthorized access)
- Bypass: AzureServices (allows trusted Microsoft services)

**Managed Identity:**
- Type: System-assigned
- Principal ID: bd700248-b5f6-46f0-8546-c96b2e68beee
- Status: Enabled

## Troubleshooting

If the function still can't access storage after role assignment:
1. Wait 10-15 minutes for full role propagation
2. Restart the function app: `az functionapp restart --name func-swickard-ftp-etl --resource-group Data_API`
3. Check function logs for authentication errors
4. Verify storage network rules allow Azure services

## Emergency Rollback

If immediate access restoration is needed:
```powershell
.\emergency-rollback.ps1 -StorageOnly
```

This will temporarily set the storage account to allow all connections until issues are resolved.
