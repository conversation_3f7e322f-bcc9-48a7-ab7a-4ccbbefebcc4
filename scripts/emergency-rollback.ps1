# Azure Network Security Implementation - Emergency Rollback Script
# This script provides immediate rollback capabilities for network security changes

# Import configuration
. "$PSScriptRoot\azure-config.ps1"

Write-Host "=== EMERGENCY ROLLBACK SCRIPT ===" -ForegroundColor Red
Write-Host "⚠️  WARNING: This will undo network security implementation!" -ForegroundColor Yellow
Write-Host ""

# Initialize environment
if (-not $global:AzureConfig.Initialize.Invoke()) {
    Write-Host "❌ Failed to initialize Azure environment. Exiting." -ForegroundColor Red
    exit 1
}

# Function for immediate storage access restoration
function Enable-EmergencyStorageAccess {
    Write-Host "🚨 EMERGENCY: Restoring full storage access..." -ForegroundColor Red
    
    try {
        Write-Host "Setting storage default action to Allow..." -ForegroundColor Yellow
        az storage account update --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --default-action Allow --output none
        
        # Verify the change
        $defaultAction = az storage account show --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --query "networkRuleSet.defaultAction" --output tsv
        
        if ($defaultAction -eq "Allow") {
            Write-Host "✅ EMERGENCY ACCESS RESTORED" -ForegroundColor Green
            Write-Host "   Storage account now allows all connections" -ForegroundColor White
            Write-Host "   ⚠️  SECURITY WARNING: Storage is now publicly accessible!" -ForegroundColor Yellow
            Write-Host "   Remember to re-secure after troubleshooting!" -ForegroundColor Yellow
            return $true
        }
        else {
            Write-Host "❌ Failed to restore emergency access" -ForegroundColor Red
            Write-Host "   Current default action: $defaultAction" -ForegroundColor White
            return $false
        }
    }
    catch {
        Write-Host "❌ CRITICAL: Failed to restore emergency access" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "   Manual intervention required via Azure Portal!" -ForegroundColor Yellow
        return $false
    }
}

# Function to restore connection string authentication
function Restore-ConnectionStringAuth {
    Write-Host "Restoring connection string authentication..." -ForegroundColor Yellow
    
    # Look for backup connection string
    $backupConnectionString = $null
    
    # Try to find backup from file
    if (Test-Path "backup-connection-string.txt") {
        $backupConnectionString = Get-Content "backup-connection-string.txt" -Raw
        Write-Host "Found backup connection string in file" -ForegroundColor White
    }
    elseif ($env:BACKUP_CONNECTION_STRING) {
        $backupConnectionString = $env:BACKUP_CONNECTION_STRING
        Write-Host "Found backup connection string in environment" -ForegroundColor White
    }
    else {
        Write-Host "⚠️  No backup connection string found" -ForegroundColor Yellow
        Write-Host "   You may need to manually recreate the connection string" -ForegroundColor White
        
        # Generate a basic connection string template
        $connectionStringTemplate = "DefaultEndpointsProtocol=https;AccountName=$($global:AzureConfig.STORAGE_ACCOUNT);AccountKey=<ACCOUNT_KEY>;EndpointSuffix=core.windows.net"
        Write-Host "   Template: $connectionStringTemplate" -ForegroundColor White
        Write-Host "   Replace <ACCOUNT_KEY> with the actual storage account key" -ForegroundColor White
        
        return $false
    }
    
    try {
        # Remove managed identity settings
        Write-Host "Removing managed identity settings..." -ForegroundColor White
        az functionapp config appsettings delete --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --setting-names "AzureWebJobsStorage__accountName" "AzureWebJobsStorage__blobServiceUri" "AzureWebJobsStorage__tableServiceUri" --output none 2>$null
        
        # Restore connection string
        Write-Host "Restoring connection string..." -ForegroundColor White
        az functionapp config appsettings set --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --settings "AzureWebJobsStorage=$backupConnectionString" --output none
        
        # Verify restoration
        $settings = az functionapp config appsettings list --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json | ConvertFrom-Json
        $restoredSetting = $settings | Where-Object { $_.name -eq "AzureWebJobsStorage" }
        
        if ($restoredSetting) {
            Write-Host "✅ Connection string authentication restored" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "❌ Failed to restore connection string" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Failed to restore connection string authentication" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to restart function app
function Restart-FunctionAppEmergency {
    Write-Host "Restarting function app..." -ForegroundColor Yellow
    
    try {
        az functionapp restart --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output none
        Write-Host "✅ Function app restarted" -ForegroundColor Green
        
        # Wait for restart
        Write-Host "Waiting for restart to complete..." -ForegroundColor White
        Start-Sleep -Seconds 15
        
        return $true
    }
    catch {
        Write-Host "❌ Failed to restart function app" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to test emergency restoration
function Test-EmergencyRestoration {
    Write-Host "Testing emergency restoration..." -ForegroundColor Yellow
    
    try {
        # Test function app status
        $appState = az functionapp show --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --query "state" --output tsv
        
        if ($appState -eq "Running") {
            Write-Host "✅ Function app is running" -ForegroundColor Green
        }
        else {
            Write-Host "⚠️  Function app state: $appState" -ForegroundColor Yellow
        }
        
        # Test function endpoint
        $functionUrl = "https://$($global:AzureConfig.FUNCTION_APP).azurewebsites.net/api/ftpetlpipeline?code=duSM205SkJe2R8cjWyw69fo5l1u6I4Gt55n9Rdlt2mJ_AzFuwyEMIQ=="
        
        Write-Host "Testing function endpoint..." -ForegroundColor White
        try {
            $response = Invoke-WebRequest -Uri $functionUrl -Method GET -TimeoutSec 30 -ErrorAction Stop
            Write-Host "✅ Function endpoint responded: $($response.StatusCode)" -ForegroundColor Green
            return $true
        }
        catch {
            Write-Host "⚠️  Function endpoint test failed" -ForegroundColor Yellow
            Write-Host "   This may be normal immediately after restoration" -ForegroundColor White
            return $false
        }
    }
    catch {
        Write-Host "❌ Failed to test emergency restoration" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to remove managed identity (if needed)
function Remove-ManagedIdentityRoles {
    Write-Host "Removing managed identity role assignments..." -ForegroundColor Yellow
    
    try {
        # Get managed identity principal ID
        $identity = az functionapp identity show --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json 2>$null | ConvertFrom-Json
        
        if ($identity -and $identity.principalId) {
            $storageScope = "/subscriptions/$($global:AzureConfig.SUBSCRIPTION_ID)/resourceGroups/$($global:AzureConfig.RESOURCE_GROUP)/providers/Microsoft.Storage/storageAccounts/$($global:AzureConfig.STORAGE_ACCOUNT)"
            
            # Remove role assignments
            $rolesToRemove = @("Storage Blob Data Contributor", "Storage Table Data Contributor")
            
            foreach ($role in $rolesToRemove) {
                try {
                    az role assignment delete --assignee $identity.principalId --role $role --scope $storageScope --output none 2>$null
                    Write-Host "✅ Removed role: $role" -ForegroundColor Green
                }
                catch {
                    Write-Host "⚠️  Could not remove role: $role" -ForegroundColor Yellow
                }
            }
            
            Write-Host "✅ Managed identity role cleanup completed" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "ℹ️  No managed identity found to clean up" -ForegroundColor White
            return $true
        }
    }
    catch {
        Write-Host "⚠️  Failed to remove managed identity roles" -ForegroundColor Yellow
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor White
        return $false
    }
}

# Function to restore from backup files
function Restore-FromBackup {
    param([string]$BackupDirectory)
    
    if (-not $BackupDirectory) {
        # Find most recent backup directory
        $backupDirs = Get-ChildItem -Directory -Name "backup-*" | Sort-Object -Descending
        if ($backupDirs.Count -gt 0) {
            $BackupDirectory = $backupDirs[0]
            Write-Host "Using most recent backup: $BackupDirectory" -ForegroundColor White
        }
        else {
            Write-Host "❌ No backup directories found" -ForegroundColor Red
            return $false
        }
    }
    
    if (-not (Test-Path $BackupDirectory)) {
        Write-Host "❌ Backup directory not found: $BackupDirectory" -ForegroundColor Red
        return $false
    }
    
    Write-Host "Restoring from backup: $BackupDirectory" -ForegroundColor Yellow
    
    try {
        # Check if restore script exists
        $restoreScript = Join-Path $BackupDirectory "restore-configuration.ps1"
        if (Test-Path $restoreScript) {
            Write-Host "Executing backup restore script..." -ForegroundColor White
            & $restoreScript
            return $true
        }
        else {
            Write-Host "⚠️  No restore script found in backup" -ForegroundColor Yellow
            Write-Host "   Manual restoration may be required" -ForegroundColor White
            return $false
        }
    }
    catch {
        Write-Host "❌ Failed to restore from backup" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Main emergency rollback function
function Start-EmergencyRollback {
    param(
        [switch]$StorageOnly,
        [switch]$FunctionAppOnly,
        [switch]$FullRollback,
        [string]$BackupDirectory
    )
    
    Write-Host "🚨 STARTING EMERGENCY ROLLBACK 🚨" -ForegroundColor Red
    Write-Host ""
    
    if (-not ($StorageOnly -or $FunctionAppOnly -or $FullRollback)) {
        Write-Host "Please specify rollback type:" -ForegroundColor Yellow
        Write-Host "  -StorageOnly      : Only restore storage access" -ForegroundColor White
        Write-Host "  -FunctionAppOnly  : Only restore function app settings" -ForegroundColor White
        Write-Host "  -FullRollback     : Complete rollback of all changes" -ForegroundColor White
        Write-Host "  -BackupDirectory  : Restore from specific backup" -ForegroundColor White
        return $false
    }
    
    $rollbackSuccess = $true
    
    # Storage rollback
    if ($StorageOnly -or $FullRollback) {
        Write-Host "=== STORAGE ROLLBACK ===" -ForegroundColor Red
        $storageOk = Enable-EmergencyStorageAccess
        $rollbackSuccess = $rollbackSuccess -and $storageOk
        Write-Host ""
    }
    
    # Function app rollback
    if ($FunctionAppOnly -or $FullRollback) {
        Write-Host "=== FUNCTION APP ROLLBACK ===" -ForegroundColor Red
        $functionOk = Restore-ConnectionStringAuth
        $rollbackSuccess = $rollbackSuccess -and $functionOk
        
        $restartOk = Restart-FunctionAppEmergency
        $rollbackSuccess = $rollbackSuccess -and $restartOk
        Write-Host ""
    }
    
    # Full rollback includes cleanup
    if ($FullRollback) {
        Write-Host "=== CLEANUP ===" -ForegroundColor Red
        Remove-ManagedIdentityRoles
        Write-Host ""
    }
    
    # Backup restoration
    if ($BackupDirectory) {
        Write-Host "=== BACKUP RESTORATION ===" -ForegroundColor Red
        $backupOk = Restore-FromBackup -BackupDirectory $BackupDirectory
        $rollbackSuccess = $rollbackSuccess -and $backupOk
        Write-Host ""
    }
    
    # Test restoration
    Write-Host "=== TESTING RESTORATION ===" -ForegroundColor Red
    Test-EmergencyRestoration
    Write-Host ""
    
    if ($rollbackSuccess) {
        Write-Host "✅ EMERGENCY ROLLBACK COMPLETED" -ForegroundColor Green
        Write-Host "   System should be restored to previous state" -ForegroundColor White
        Write-Host "   ⚠️  If storage access was restored, remember to re-secure!" -ForegroundColor Yellow
    }
    else {
        Write-Host "❌ EMERGENCY ROLLBACK COMPLETED WITH ERRORS" -ForegroundColor Red
        Write-Host "   Some operations failed - manual intervention may be required" -ForegroundColor Yellow
        Write-Host "   Check Azure Portal for current configuration" -ForegroundColor White
    }
    
    return $rollbackSuccess
}

# Interactive emergency menu
function Show-EmergencyMenu {
    Write-Host "=== EMERGENCY ROLLBACK MENU ===" -ForegroundColor Red
    Write-Host ""
    Write-Host "1. Emergency Storage Access (immediate)" -ForegroundColor White
    Write-Host "2. Restore Function App Settings" -ForegroundColor White
    Write-Host "3. Full Rollback (all changes)" -ForegroundColor White
    Write-Host "4. Restore from Backup" -ForegroundColor White
    Write-Host "5. Test Current Status" -ForegroundColor White
    Write-Host "6. Exit" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "Select option (1-6)"
    
    switch ($choice) {
        "1" { Start-EmergencyRollback -StorageOnly }
        "2" { Start-EmergencyRollback -FunctionAppOnly }
        "3" { 
            $confirm = Read-Host "This will undo ALL network security changes. Continue? (yes/no)"
            if ($confirm -eq "yes") {
                Start-EmergencyRollback -FullRollback
            }
        }
        "4" { 
            $backupDir = Read-Host "Enter backup directory name (or press Enter for most recent)"
            Start-EmergencyRollback -BackupDirectory $backupDir
        }
        "5" { Test-EmergencyRestoration }
        "6" { Write-Host "Exiting..." -ForegroundColor White }
        default { Write-Host "Invalid option" -ForegroundColor Red }
    }
}

# Execute based on how script is called
if ($MyInvocation.InvocationName -ne '.') {
    if ($args.Count -eq 0) {
        # Interactive mode
        Show-EmergencyMenu
    }
    else {
        # Command line mode
        $params = @{}
        foreach ($arg in $args) {
            switch ($arg) {
                "-StorageOnly" { $params.StorageOnly = $true }
                "-FunctionAppOnly" { $params.FunctionAppOnly = $true }
                "-FullRollback" { $params.FullRollback = $true }
                default { 
                    if ($arg.StartsWith("-BackupDirectory=")) {
                        $params.BackupDirectory = $arg.Split("=")[1]
                    }
                }
            }
        }
        
        $success = Start-EmergencyRollback @params
        if ($success) { exit 0 } else { exit 1 }
    }
}

# Export functions for use in other scripts
$global:EmergencyRollback = @{
    EmergencyStorageAccess = ${function:Enable-EmergencyStorageAccess}
    RestoreConnectionString = ${function:Restore-ConnectionStringAuth}
    RestartFunctionApp = ${function:Restart-FunctionAppEmergency}
    TestRestoration = ${function:Test-EmergencyRestoration}
    RemoveManagedIdentityRoles = ${function:Remove-ManagedIdentityRoles}
    RestoreFromBackup = ${function:Restore-FromBackup}
    StartRollback = ${function:Start-EmergencyRollback}
    ShowMenu = ${function:Show-EmergencyMenu}
}
