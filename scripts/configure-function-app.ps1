# Azure Network Security Implementation - Function App Configuration Script
# This script updates function app settings for managed identity authentication

# Import configuration
. "$PSScriptRoot\azure-config.ps1"

Write-Host "=== Function App Configuration ===" -ForegroundColor Cyan

# Initialize environment
if (-not $global:AzureConfig.Initialize.Invoke()) {
    Write-Host "❌ Failed to initialize Azure environment. Exiting." -ForegroundColor Red
    exit 1
}

# Function to get current app settings
function Get-CurrentAppSettings {
    Write-Host "Getting current function app settings..." -ForegroundColor Yellow
    
    try {
        $settings = az functionapp config appsettings list --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json | ConvertFrom-Json
        
        # Filter storage-related settings
        $storageSettings = $settings | Where-Object { $_.name -like "*Storage*" -or $_.name -like "*AzureWebJobs*" }
        
        Write-Host "Current storage-related settings:" -ForegroundColor White
        foreach ($setting in $storageSettings) {
            $value = if ($setting.value.Length -gt 50) { "$($setting.value.Substring(0, 50))..." } else { $setting.value }
            Write-Host "  $($setting.name): $value" -ForegroundColor White
        }
        
        return $settings
    }
    catch {
        Write-Host "❌ Failed to get current app settings" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $null
    }
}

# Function to backup current connection string
function Backup-ConnectionString {
    Write-Host "Backing up current connection string..." -ForegroundColor Yellow
    
    try {
        $currentSettings = az functionapp config appsettings list --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json | ConvertFrom-Json
        
        $azureWebJobsStorage = $currentSettings | Where-Object { $_.name -eq "AzureWebJobsStorage" }
        
        if ($azureWebJobsStorage) {
            Write-Host "✅ Current connection string found and backed up" -ForegroundColor Green
            
            # Save to environment variable for potential restoration
            $env:BACKUP_CONNECTION_STRING = $azureWebJobsStorage.value
            
            # Also save to file for persistence
            $backupFile = "backup-connection-string.txt"
            $azureWebJobsStorage.value | Out-File -FilePath $backupFile -Encoding UTF8
            Write-Host "   Connection string backed up to: $backupFile" -ForegroundColor White
            
            return $azureWebJobsStorage.value
        }
        else {
            Write-Host "ℹ️  No existing AzureWebJobsStorage connection string found" -ForegroundColor White
            return $null
        }
    }
    catch {
        Write-Host "❌ Failed to backup connection string" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $null
    }
}

# Function to remove old connection string
function Remove-OldConnectionString {
    Write-Host "Removing old connection string configuration..." -ForegroundColor Yellow
    
    try {
        # Try to delete the old connection string setting
        az functionapp config appsettings delete --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --setting-names "AzureWebJobsStorage" --output none 2>$null
        
        # Verify it's removed
        $settings = az functionapp config appsettings list --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json | ConvertFrom-Json
        $oldSetting = $settings | Where-Object { $_.name -eq "AzureWebJobsStorage" }
        
        if (-not $oldSetting) {
            Write-Host "✅ Old connection string removed" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "ℹ️  Old connection string still exists (will be overwritten)" -ForegroundColor White
            return $true
        }
    }
    catch {
        Write-Host "ℹ️  No existing connection string to remove" -ForegroundColor White
        return $true
    }
}

# Function to set managed identity configuration
function Set-ManagedIdentityConfig {
    Write-Host "Setting managed identity configuration..." -ForegroundColor Yellow
    
    try {
        # Define the new settings for managed identity
        $newSettings = @(
            "AzureWebJobsStorage__accountName=$($global:AzureConfig.STORAGE_ACCOUNT)",
            "AzureWebJobsStorage__blobServiceUri=https://$($global:AzureConfig.STORAGE_ACCOUNT).blob.core.windows.net",
            "AzureWebJobsStorage__tableServiceUri=https://$($global:AzureConfig.STORAGE_ACCOUNT).table.core.windows.net"
        )
        
        Write-Host "Setting new managed identity configuration:" -ForegroundColor White
        foreach ($setting in $newSettings) {
            Write-Host "  $setting" -ForegroundColor White
        }
        
        # Apply the settings
        az functionapp config appsettings set --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --settings $newSettings --output none
        
        Write-Host "✅ Managed identity configuration applied" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Failed to set managed identity configuration" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to verify new configuration
function Test-NewConfiguration {
    Write-Host "Verifying new function app configuration..." -ForegroundColor Yellow
    
    try {
        $settings = az functionapp config appsettings list --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json | ConvertFrom-Json
        
        # Check for required managed identity settings
        $requiredSettings = @(
            "AzureWebJobsStorage__accountName",
            "AzureWebJobsStorage__blobServiceUri", 
            "AzureWebJobsStorage__tableServiceUri"
        )
        
        $allSettingsPresent = $true
        
        Write-Host "Checking required settings:" -ForegroundColor White
        foreach ($requiredSetting in $requiredSettings) {
            $setting = $settings | Where-Object { $_.name -eq $requiredSetting }
            
            if ($setting) {
                Write-Host "  ✅ $requiredSetting: $($setting.value)" -ForegroundColor Green
            }
            else {
                Write-Host "  ❌ Missing: $requiredSetting" -ForegroundColor Red
                $allSettingsPresent = $false
            }
        }
        
        # Check that old connection string is not present
        $oldConnectionString = $settings | Where-Object { $_.name -eq "AzureWebJobsStorage" -and -not $_.name.Contains("__") }
        
        if ($oldConnectionString) {
            Write-Host "  ⚠️  Old connection string still present: AzureWebJobsStorage" -ForegroundColor Yellow
            Write-Host "     This may override managed identity settings" -ForegroundColor Yellow
        }
        else {
            Write-Host "  ✅ Old connection string properly removed/replaced" -ForegroundColor Green
        }
        
        return $allSettingsPresent
    }
    catch {
        Write-Host "❌ Failed to verify new configuration" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to restart function app
function Restart-FunctionApp {
    Write-Host "Restarting function app to apply changes..." -ForegroundColor Yellow
    
    try {
        az functionapp restart --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output none
        
        Write-Host "✅ Function app restarted" -ForegroundColor Green
        
        # Wait a moment for restart to complete
        Write-Host "Waiting for restart to complete..." -ForegroundColor White
        Start-Sleep -Seconds 10
        
        return $true
    }
    catch {
        Write-Host "❌ Failed to restart function app" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to test function app functionality
function Test-FunctionAppHealth {
    Write-Host "Testing function app health..." -ForegroundColor Yellow
    
    try {
        # Get function app status
        $appStatus = az functionapp show --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --query "state" --output tsv
        
        if ($appStatus -eq "Running") {
            Write-Host "✅ Function app is running" -ForegroundColor Green
        }
        else {
            Write-Host "⚠️  Function app state: $appStatus" -ForegroundColor Yellow
        }
        
        # Test the function endpoint (if available)
        $functionUrl = "https://$($global:AzureConfig.FUNCTION_APP).azurewebsites.net/api/ftpetlpipeline?code=duSM205SkJe2R8cjWyw69fo5l1u6I4Gt55n9Rdlt2mJ_AzFuwyEMIQ=="
        
        Write-Host "Testing function endpoint..." -ForegroundColor White
        Write-Host "  URL: $functionUrl" -ForegroundColor White
        
        try {
            $response = Invoke-WebRequest -Uri $functionUrl -Method GET -TimeoutSec 30 -ErrorAction Stop
            Write-Host "✅ Function endpoint responded with status: $($response.StatusCode)" -ForegroundColor Green
            
            # Show first few lines of response
            $responseText = $response.Content
            if ($responseText.Length -gt 200) {
                $responseText = $responseText.Substring(0, 200) + "..."
            }
            Write-Host "   Response preview: $responseText" -ForegroundColor White
            
            return $true
        }
        catch {
            Write-Host "⚠️  Function endpoint test failed" -ForegroundColor Yellow
            Write-Host "   This may be normal during configuration changes" -ForegroundColor White
            Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "❌ Failed to test function app health" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to restore connection string (for rollback)
function Restore-ConnectionString {
    param([string]$ConnectionString)
    
    Write-Host "Restoring original connection string..." -ForegroundColor Yellow
    
    if (-not $ConnectionString) {
        if (Test-Path "backup-connection-string.txt") {
            $ConnectionString = Get-Content "backup-connection-string.txt" -Raw
        }
        elseif ($env:BACKUP_CONNECTION_STRING) {
            $ConnectionString = $env:BACKUP_CONNECTION_STRING
        }
        else {
            Write-Host "❌ No backup connection string available" -ForegroundColor Red
            return $false
        }
    }
    
    try {
        # Remove managed identity settings
        az functionapp config appsettings delete --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --setting-names "AzureWebJobsStorage__accountName" "AzureWebJobsStorage__blobServiceUri" "AzureWebJobsStorage__tableServiceUri" --output none 2>$null
        
        # Restore original connection string
        az functionapp config appsettings set --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --settings "AzureWebJobsStorage=$ConnectionString" --output none
        
        Write-Host "✅ Original connection string restored" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Failed to restore connection string" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Main configuration function
function Configure-FunctionApp {
    Write-Host "Starting function app configuration..." -ForegroundColor White
    Write-Host ""
    
    # Step 1: Get current settings
    $currentSettings = Get-CurrentAppSettings
    if (-not $currentSettings) {
        Write-Host "❌ Cannot proceed without current settings" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    
    # Step 2: Backup current connection string
    $backupConnectionString = Backup-ConnectionString
    Write-Host ""
    
    # Step 3: Remove old connection string
    $removeOk = Remove-OldConnectionString
    if (-not $removeOk) {
        Write-Host "❌ Failed to remove old connection string" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    
    # Step 4: Set managed identity configuration
    $configOk = Set-ManagedIdentityConfig
    if (-not $configOk) {
        Write-Host "❌ Failed to set managed identity configuration" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    
    # Step 5: Verify new configuration
    $verifyOk = Test-NewConfiguration
    if (-not $verifyOk) {
        Write-Host "❌ Configuration verification failed" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    
    # Step 6: Restart function app
    $restartOk = Restart-FunctionApp
    if (-not $restartOk) {
        Write-Host "⚠️  Function app restart failed, but configuration may still work" -ForegroundColor Yellow
    }
    
    Write-Host ""
    
    # Step 7: Test function app health
    Test-FunctionAppHealth
    
    Write-Host ""
    Write-Host "✅ FUNCTION APP CONFIGURATION COMPLETED" -ForegroundColor Green
    Write-Host "   Managed identity authentication configured" -ForegroundColor White
    Write-Host "   Old connection string removed/replaced" -ForegroundColor White
    Write-Host "   Function app restarted" -ForegroundColor White
    
    return $true
}

# Execute if script is run directly
if ($MyInvocation.InvocationName -ne '.') {
    $success = Configure-FunctionApp
    if ($success) {
        exit 0
    }
    else {
        Write-Host ""
        Write-Host "❌ CONFIGURATION FAILED" -ForegroundColor Red
        Write-Host "   Use Restore-ConnectionString to rollback if needed" -ForegroundColor Yellow
        exit 1
    }
}

# Export functions for use in other scripts
$global:FunctionAppConfig = @{
    Configure = ${function:Configure-FunctionApp}
    GetSettings = ${function:Get-CurrentAppSettings}
    BackupConnectionString = ${function:Backup-ConnectionString}
    SetManagedIdentity = ${function:Set-ManagedIdentityConfig}
    TestConfig = ${function:Test-NewConfiguration}
    TestHealth = ${function:Test-FunctionAppHealth}
    Restart = ${function:Restart-FunctionApp}
    Restore = ${function:Restore-ConnectionString}
}
