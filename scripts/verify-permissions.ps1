# Azure Network Security Implementation - Permission Verification Script
# This script verifies all required Azure permissions before implementation

# Import configuration
. "$PSScriptRoot\azure-config.ps1"

Write-Host "=== Azure Permission Verification ===" -ForegroundColor Cyan

# Initialize environment
if (-not $global:AzureConfig.Initialize.Invoke()) {
    Write-Host "❌ Failed to initialize Azure environment. Exiting." -ForegroundColor Red
    exit 1
}

# Get current user information
$currentUser = az account show --query "user.name" --output tsv
Write-Host "Checking permissions for user: $currentUser" -ForegroundColor White
Write-Host ""

# Function to check role assignment
function Test-RoleAssignment {
    param(
        [string]$Role,
        [string]$Scope,
        [string]$ScopeName
    )
    
    Write-Host "Checking $Role on $ScopeName..." -ForegroundColor Yellow
    
    try {
        $assignments = az role assignment list --scope $Scope --assignee $currentUser --query "[?roleDefinitionName=='$Role'].roleDefinitionName" --output tsv
        
        if ($assignments -contains $Role) {
            Write-Host "✅ CONFIRMED: $Role on $ScopeName" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "❌ MISSING: $Role on $ScopeName" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ ERROR checking $Role on $ScopeName" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to check all required permissions
function Test-AllPermissions {
    Write-Host "=== Required Permission Check ===" -ForegroundColor Cyan

    $criticalPermissionsOk = $true
    $hasWarnings = $false

    # Resource Group scope
    $rgScope = "/subscriptions/$($global:AzureConfig.SUBSCRIPTION_ID)/resourceGroups/$($global:AzureConfig.RESOURCE_GROUP)"

    # Check Contributor role on Resource Group (CRITICAL)
    $contributorOk = Test-RoleAssignment -Role "Contributor" -Scope $rgScope -ScopeName "Resource Group ($($global:AzureConfig.RESOURCE_GROUP))"
    $criticalPermissionsOk = $criticalPermissionsOk -and $contributorOk

    # Check User Access Administrator role on Resource Group (WARNING ONLY)
    Write-Host "Checking User Access Administrator on Resource Group..." -ForegroundColor Yellow
    $uaaOk = Test-RoleAssignment -Role "User Access Administrator" -Scope $rgScope -ScopeName "Resource Group ($($global:AzureConfig.RESOURCE_GROUP))"
    if (-not $uaaOk) {
        Write-Host "⚠️  WARNING: User Access Administrator role missing" -ForegroundColor Yellow
        Write-Host "   Implementation can proceed, but role assignments will need to be done manually by Jonathan" -ForegroundColor White
        $hasWarnings = $true
    }

    # Storage Account scope
    $saScope = "/subscriptions/$($global:AzureConfig.SUBSCRIPTION_ID)/resourceGroups/$($global:AzureConfig.RESOURCE_GROUP)/providers/Microsoft.Storage/storageAccounts/$($global:AzureConfig.STORAGE_ACCOUNT)"

    # Check Storage Account Contributor role (CRITICAL)
    $sacOk = Test-RoleAssignment -Role "Storage Account Contributor" -Scope $saScope -ScopeName "Storage Account ($($global:AzureConfig.STORAGE_ACCOUNT))"
    $criticalPermissionsOk = $criticalPermissionsOk -and $sacOk

    Write-Host ""
    if ($criticalPermissionsOk -and -not $hasWarnings) {
        Write-Host "✅ ALL REQUIRED PERMISSIONS CONFIRMED" -ForegroundColor Green
        Write-Host "   You can proceed with the network security implementation." -ForegroundColor White
        return @{ CanProceed = $true; HasWarnings = $false; RequiresManualSteps = $false }
    }
    elseif ($criticalPermissionsOk -and $hasWarnings) {
        Write-Host "✅ CRITICAL PERMISSIONS CONFIRMED (WITH WARNINGS)" -ForegroundColor Yellow
        Write-Host "   You can proceed with the implementation." -ForegroundColor White
        Write-Host "   ⚠️  Some steps will require manual completion by Jonathan." -ForegroundColor Yellow
        return @{ CanProceed = $true; HasWarnings = $true; RequiresManualSteps = $true }
    }
    else {
        Write-Host "❌ MISSING CRITICAL PERMISSIONS" -ForegroundColor Red
        Write-Host "   Cannot proceed without Contributor and Storage Account Contributor roles." -ForegroundColor Yellow
        Write-Host "   Contact Jonathan to grant the missing permissions." -ForegroundColor Yellow
        return @{ CanProceed = $false; HasWarnings = $hasWarnings; RequiresManualSteps = $true }
    }
}

# Function to display detailed permission report
function Show-PermissionReport {
    Write-Host "=== Detailed Permission Report ===" -ForegroundColor Cyan
    
    # Show all role assignments for current user
    Write-Host "All role assignments for $currentUser" -ForegroundColor Yellow
    try {
        az role assignment list --assignee $currentUser --query "[].{Role:roleDefinitionName, Scope:scope}" --output table
    }
    catch {
        Write-Host "Error retrieving role assignments" -ForegroundColor Red
    }
    
    Write-Host ""
    
    # Show specific resource group permissions
    Write-Host "Resource Group permissions:" -ForegroundColor Yellow
    $rgScope = "/subscriptions/$($global:AzureConfig.SUBSCRIPTION_ID)/resourceGroups/$($global:AzureConfig.RESOURCE_GROUP)"
    try {
        az role assignment list --scope $rgScope --assignee $currentUser --query "[].{Role:roleDefinitionName, Scope:scope}" --output table
    }
    catch {
        Write-Host "Error retrieving resource group permissions" -ForegroundColor Red
    }
    
    Write-Host ""
    
    # Show storage account permissions
    Write-Host "Storage Account permissions:" -ForegroundColor Yellow
    $saScope = "/subscriptions/$($global:AzureConfig.SUBSCRIPTION_ID)/resourceGroups/$($global:AzureConfig.RESOURCE_GROUP)/providers/Microsoft.Storage/storageAccounts/$($global:AzureConfig.STORAGE_ACCOUNT)"
    try {
        az role assignment list --scope $saScope --assignee $currentUser --query "[].{Role:roleDefinitionName, Scope:scope}" --output table
    }
    catch {
        Write-Host "Error retrieving storage account permissions" -ForegroundColor Red
    }
}

# Function to generate permission commands for Jonathan
function Export-PermissionCommands {
    Write-Host "=== Generating Permission Commands for Jonathan ===" -ForegroundColor Cyan
    
    $commandsFile = "jonathan_permission_commands.sh"
    $commands = @"
#!/bin/bash
# Commands for Jonathan to grant Azure permissions
# Generated on $(Get-Date)

# Set variables
SUBSCRIPTION_ID="$($global:AzureConfig.SUBSCRIPTION_ID)"
RESOURCE_GROUP="$($global:AzureConfig.RESOURCE_GROUP)"
STORAGE_ACCOUNT="$($global:AzureConfig.STORAGE_ACCOUNT)"
USER_EMAIL="$currentUser"

echo "Granting permissions to `$USER_EMAIL..."

# Grant Contributor role on Resource Group
echo "Granting Contributor role on Resource Group..."
az role assignment create \
  --assignee "`$USER_EMAIL" \
  --role "Contributor" \
  --scope "/subscriptions/`$SUBSCRIPTION_ID/resourceGroups/`$RESOURCE_GROUP"

# Grant User Access Administrator role on Resource Group
echo "Granting User Access Administrator role on Resource Group..."
az role assignment create \
  --assignee "`$USER_EMAIL" \
  --role "User Access Administrator" \
  --scope "/subscriptions/`$SUBSCRIPTION_ID/resourceGroups/`$RESOURCE_GROUP"

# Grant Storage Account Contributor role
echo "Granting Storage Account Contributor role..."
az role assignment create \
  --assignee "`$USER_EMAIL" \
  --role "Storage Account Contributor" \
  --scope "/subscriptions/`$SUBSCRIPTION_ID/resourceGroups/`$RESOURCE_GROUP/providers/Microsoft.Storage/storageAccounts/`$STORAGE_ACCOUNT"

echo "Permissions granted successfully!"

# Verification
echo "Verifying permissions..."
az role assignment list \
  --assignee "`$USER_EMAIL" \
  --query "[].{Role:roleDefinitionName, Scope:scope}" \
  --output table
"@
    
    $commands | Out-File -FilePath $commandsFile -Encoding UTF8
    Write-Host "✅ Permission commands saved to: $commandsFile" -ForegroundColor Green
    Write-Host "   Send this file to Jonathan for execution." -ForegroundColor White
}

# Main execution
Write-Host "Starting permission verification..." -ForegroundColor White
Write-Host ""

$permissionResult = Test-AllPermissions

Write-Host ""
Show-PermissionReport

Write-Host ""
Export-PermissionCommands

# Return result based on new permission structure
if ($permissionResult.CanProceed -and -not $permissionResult.HasWarnings) {
    Write-Host ""
    Write-Host "🎉 READY TO PROCEED WITH FULL IMPLEMENTATION" -ForegroundColor Green
    exit 0
}
elseif ($permissionResult.CanProceed -and $permissionResult.HasWarnings) {
    Write-Host ""
    Write-Host "⚠️  READY TO PROCEED WITH PARTIAL IMPLEMENTATION" -ForegroundColor Yellow
    Write-Host "   Some steps will require manual completion by Jonathan" -ForegroundColor White
    Write-Host "   Check the generated manual instructions file" -ForegroundColor White
    exit 0
}
else {
    Write-Host ""
    Write-Host "❌ CANNOT PROCEED - MISSING CRITICAL PERMISSIONS" -ForegroundColor Red
    Write-Host "   Contact Jonathan to grant the missing critical permissions" -ForegroundColor Yellow
    exit 1
}
