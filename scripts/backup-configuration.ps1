# Azure Network Security Implementation - Configuration Backup Script
# This script backs up current Azure configuration before implementation

# Import configuration
. "$PSScriptRoot\azure-config.ps1"

Write-Host "=== Azure Configuration Backup ===" -ForegroundColor Cyan

# Initialize environment
if (-not $global:AzureConfig.Initialize.Invoke()) {
    Write-Host "❌ Failed to initialize Azure environment. Exiting." -ForegroundColor Red
    exit 1
}

# Create backup directory with timestamp
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$backupDir = "backup-$timestamp"
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

Write-Host "Creating backup in directory: $backupDir" -ForegroundColor White
Write-Host ""

# Function to backup storage account configuration
function Backup-StorageConfiguration {
    Write-Host "Backing up Storage Account configuration..." -ForegroundColor Yellow
    
    $backupFile = "$backupDir\storage-account-config.json"
    
    try {
        # Get complete storage account configuration
        $storageConfig = az storage account show --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --output json | ConvertFrom-Json
        
        # Create backup object with essential configuration
        $backup = @{
            timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
            storageAccount = $global:AzureConfig.STORAGE_ACCOUNT
            resourceGroup = $global:AzureConfig.RESOURCE_GROUP
            subscriptionId = $global:AzureConfig.SUBSCRIPTION_ID
            configuration = @{
                networkRuleSet = $storageConfig.networkRuleSet
                kind = $storageConfig.kind
                sku = $storageConfig.sku
                accessTier = $storageConfig.accessTier
                allowBlobPublicAccess = $storageConfig.allowBlobPublicAccess
                minimumTlsVersion = $storageConfig.minimumTlsVersion
                supportsHttpsTrafficOnly = $storageConfig.supportsHttpsTrafficOnly
                encryption = $storageConfig.encryption
            }
        }
        
        $backup | ConvertTo-Json -Depth 10 | Out-File -FilePath $backupFile -Encoding UTF8
        Write-Host "✅ Storage configuration backed up to: $backupFile" -ForegroundColor Green
        
        # Display current network rules
        Write-Host "Current Network Rules:" -ForegroundColor White
        Write-Host "  Default Action: $($storageConfig.networkRuleSet.defaultAction)" -ForegroundColor White
        Write-Host "  Bypass: $($storageConfig.networkRuleSet.bypass)" -ForegroundColor White
        
        return $true
    }
    catch {
        Write-Host "❌ Failed to backup storage configuration" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to backup function app configuration
function Backup-FunctionAppConfiguration {
    Write-Host "Backing up Function App configuration..." -ForegroundColor Yellow
    
    $backupFile = "$backupDir\function-app-config.json"
    
    try {
        # Get function app configuration
        $functionConfig = az functionapp show --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json | ConvertFrom-Json
        
        # Get app settings
        $appSettings = az functionapp config appsettings list --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json | ConvertFrom-Json
        
        # Get managed identity status
        $identity = az functionapp identity show --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json 2>$null | ConvertFrom-Json
        
        # Create backup object
        $backup = @{
            timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
            functionApp = $global:AzureConfig.FUNCTION_APP
            resourceGroup = $global:AzureConfig.RESOURCE_GROUP
            subscriptionId = $global:AzureConfig.SUBSCRIPTION_ID
            configuration = @{
                identity = $identity
                appSettings = $appSettings
                kind = $functionConfig.kind
                state = $functionConfig.state
                hostNameSslStates = $functionConfig.hostNameSslStates
            }
        }
        
        $backup | ConvertTo-Json -Depth 10 | Out-File -FilePath $backupFile -Encoding UTF8
        Write-Host "✅ Function App configuration backed up to: $backupFile" -ForegroundColor Green
        
        # Display current identity status
        if ($identity -and $identity.principalId) {
            Write-Host "Current Managed Identity:" -ForegroundColor White
            Write-Host "  Principal ID: $($identity.principalId)" -ForegroundColor White
            Write-Host "  Type: $($identity.type)" -ForegroundColor White
        }
        else {
            Write-Host "Current Managed Identity: Not configured" -ForegroundColor White
        }
        
        # Display storage-related app settings
        $storageSettings = $appSettings | Where-Object { $_.name -like "*Storage*" -or $_.name -like "*AzureWebJobs*" }
        if ($storageSettings) {
            Write-Host "Current Storage Settings:" -ForegroundColor White
            foreach ($setting in $storageSettings) {
                $value = if ($setting.value.Length -gt 50) { "$($setting.value.Substring(0, 50))..." } else { $setting.value }
                Write-Host "  $($setting.name): $value" -ForegroundColor White
            }
        }
        
        return $true
    }
    catch {
        Write-Host "❌ Failed to backup function app configuration" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to backup role assignments
function Backup-RoleAssignments {
    Write-Host "Backing up current role assignments..." -ForegroundColor Yellow
    
    $backupFile = "$backupDir\role-assignments.json"
    
    try {
        # Get role assignments for storage account scope
        $saScope = "/subscriptions/$($global:AzureConfig.SUBSCRIPTION_ID)/resourceGroups/$($global:AzureConfig.RESOURCE_GROUP)/providers/Microsoft.Storage/storageAccounts/$($global:AzureConfig.STORAGE_ACCOUNT)"
        $roleAssignments = az role assignment list --scope $saScope --output json | ConvertFrom-Json
        
        # Create backup object
        $backup = @{
            timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
            scope = $saScope
            roleAssignments = $roleAssignments
        }
        
        $backup | ConvertTo-Json -Depth 10 | Out-File -FilePath $backupFile -Encoding UTF8
        Write-Host "✅ Role assignments backed up to: $backupFile" -ForegroundColor Green
        
        # Display current role assignments
        if ($roleAssignments.Count -gt 0) {
            Write-Host "Current Role Assignments on Storage Account:" -ForegroundColor White
            foreach ($assignment in $roleAssignments) {
                Write-Host "  $($assignment.roleDefinitionName): $($assignment.principalName)" -ForegroundColor White
            }
        }
        else {
            Write-Host "No existing role assignments on storage account" -ForegroundColor White
        }
        
        return $true
    }
    catch {
        Write-Host "❌ Failed to backup role assignments" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to create restoration script
function Create-RestorationScript {
    Write-Host "Creating restoration script..." -ForegroundColor Yellow
    
    $restoreScript = "$backupDir\restore-configuration.ps1"
    
    $script = @"
# Azure Configuration Restoration Script
# Generated on $(Get-Date)
# Backup Directory: $backupDir

Write-Host "=== Azure Configuration Restoration ===" -ForegroundColor Cyan

# Import configuration
. "`$PSScriptRoot\..\azure-config.ps1"

# Initialize environment
if (-not `$global:AzureConfig.Initialize.Invoke()) {
    Write-Host "❌ Failed to initialize Azure environment. Exiting." -ForegroundColor Red
    exit 1
}

Write-Host "⚠️  WARNING: This will restore the previous configuration!" -ForegroundColor Yellow
Write-Host "   This may undo the network security implementation." -ForegroundColor Yellow
`$confirm = Read-Host "Do you want to continue? (yes/no)"

if (`$confirm -ne "yes") {
    Write-Host "Restoration cancelled." -ForegroundColor Yellow
    exit 0
}

# Load backup configurations
`$storageBackup = Get-Content "$backupDir\storage-account-config.json" | ConvertFrom-Json
`$functionBackup = Get-Content "$backupDir\function-app-config.json" | ConvertFrom-Json

Write-Host "Restoring storage account network rules..." -ForegroundColor Yellow

# Restore storage account default action
az storage account update \
  --name `$global:AzureConfig.STORAGE_ACCOUNT \
  --resource-group `$global:AzureConfig.RESOURCE_GROUP \
  --default-action `$storageBackup.configuration.networkRuleSet.defaultAction

# Restore bypass settings
if (`$storageBackup.configuration.networkRuleSet.bypass) {
    az storage account update \
      --name `$global:AzureConfig.STORAGE_ACCOUNT \
      --resource-group `$global:AzureConfig.RESOURCE_GROUP \
      --bypass `$storageBackup.configuration.networkRuleSet.bypass
}

Write-Host "✅ Configuration restoration completed" -ForegroundColor Green
Write-Host "   Review the changes and test functionality" -ForegroundColor White
"@
    
    $script | Out-File -FilePath $restoreScript -Encoding UTF8
    Write-Host "✅ Restoration script created: $restoreScript" -ForegroundColor Green
}

# Function to create backup summary
function Create-BackupSummary {
    $summaryFile = "$backupDir\backup-summary.md"
    
    $summary = @"
# Azure Configuration Backup Summary

**Backup Created:** $(Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC")
**Backup Directory:** $backupDir

## Resources Backed Up

- **Storage Account:** $($global:AzureConfig.STORAGE_ACCOUNT)
- **Function App:** $($global:AzureConfig.FUNCTION_APP)
- **Resource Group:** $($global:AzureConfig.RESOURCE_GROUP)
- **Subscription:** $($global:AzureConfig.SUBSCRIPTION_ID)

## Backup Files

- `storage-account-config.json` - Complete storage account configuration
- `function-app-config.json` - Function app settings and identity configuration
- `role-assignments.json` - Current role assignments on storage account
- `restore-configuration.ps1` - Automated restoration script

## Usage

To restore the configuration:
1. Navigate to the backup directory
2. Run: `.\restore-configuration.ps1`
3. Confirm the restoration when prompted

## Notes

- This backup was created before implementing network security changes
- Keep this backup until the implementation is verified and stable
- The restoration script will undo network security changes
"@
    
    $summary | Out-File -FilePath $summaryFile -Encoding UTF8
    Write-Host "✅ Backup summary created: $summaryFile" -ForegroundColor Green
}

# Main execution
Write-Host "Starting configuration backup..." -ForegroundColor White
Write-Host ""

$storageOk = Backup-StorageConfiguration
Write-Host ""

$functionOk = Backup-FunctionAppConfiguration
Write-Host ""

$rolesOk = Backup-RoleAssignments
Write-Host ""

Create-RestorationScript
Write-Host ""

Create-BackupSummary
Write-Host ""

if ($storageOk -and $functionOk -and $rolesOk) {
    Write-Host "✅ BACKUP COMPLETED SUCCESSFULLY" -ForegroundColor Green
    Write-Host "   Backup directory: $backupDir" -ForegroundColor White
    Write-Host "   You can now proceed with the implementation." -ForegroundColor White
    exit 0
}
else {
    Write-Host "❌ BACKUP COMPLETED WITH ERRORS" -ForegroundColor Red
    Write-Host "   Review the errors above before proceeding." -ForegroundColor Yellow
    exit 1
}
