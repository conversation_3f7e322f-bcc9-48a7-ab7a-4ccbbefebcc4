# Azure Network Security Implementation - Managed Identity Configuration Script
# This script enables managed identity and assigns required storage roles

# Import configuration
. "$PSScriptRoot\azure-config.ps1"

Write-Host "=== Managed Identity Configuration ===" -ForegroundColor Cyan

# Initialize environment
if (-not $global:AzureConfig.Initialize.Invoke()) {
    Write-Host "❌ Failed to initialize Azure environment. Exiting." -ForegroundColor Red
    exit 1
}

# Function to check if managed identity exists
function Test-ManagedIdentity {
    Write-Host "Checking existing managed identity..." -ForegroundColor Yellow
    
    try {
        $identity = az functionapp identity show --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json 2>$null | ConvertFrom-Json
        
        if ($identity -and $identity.principalId -and $identity.principalId -ne "null") {
            Write-Host "✅ Managed identity already exists" -ForegroundColor Green
            Write-Host "   Principal ID: $($identity.principalId)" -ForegroundColor White
            Write-Host "   Type: $($identity.type)" -ForegroundColor White
            return $identity.principalId
        }
        else {
            Write-Host "ℹ️  No managed identity found" -ForegroundColor White
            return $null
        }
    }
    catch {
        Write-Host "ℹ️  No managed identity found" -ForegroundColor White
        return $null
    }
}

# Function to enable managed identity
function Enable-ManagedIdentity {
    Write-Host "Enabling system-assigned managed identity..." -ForegroundColor Yellow
    
    try {
        $result = az functionapp identity assign --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json | ConvertFrom-Json
        
        if ($result.principalId) {
            Write-Host "✅ Managed identity enabled successfully" -ForegroundColor Green
            Write-Host "   Principal ID: $($result.principalId)" -ForegroundColor White
            Write-Host "   Tenant ID: $($result.tenantId)" -ForegroundColor White
            return $result.principalId
        }
        else {
            Write-Host "❌ Failed to enable managed identity" -ForegroundColor Red
            return $null
        }
    }
    catch {
        Write-Host "❌ Failed to enable managed identity" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $null
    }
}

# Function to assign storage role
function Grant-StorageRole {
    param(
        [string]$PrincipalId,
        [string]$RoleName
    )

    Write-Host "Attempting to assign $RoleName role..." -ForegroundColor Yellow

    $storageScope = "/subscriptions/$($global:AzureConfig.SUBSCRIPTION_ID)/resourceGroups/$($global:AzureConfig.RESOURCE_GROUP)/providers/Microsoft.Storage/storageAccounts/$($global:AzureConfig.STORAGE_ACCOUNT)"

    try {
        # Check if role assignment already exists
        $existingAssignment = az role assignment list --assignee $PrincipalId --scope $storageScope --query "[?roleDefinitionName=='$RoleName'].roleDefinitionName" --output tsv

        if ($existingAssignment -contains $RoleName) {
            Write-Host "✅ $RoleName already assigned" -ForegroundColor Green
            return @{ Success = $true; RequiresManualStep = $false }
        }

        # Attempt to create role assignment
        $result = az role assignment create --assignee $PrincipalId --role $RoleName --scope $storageScope --output none 2>&1

        if ($LASTEXITCODE -eq 0) {
            # Verify assignment
            $verification = az role assignment list --assignee $PrincipalId --scope $storageScope --query "[?roleDefinitionName=='$RoleName'].roleDefinitionName" --output tsv

            if ($verification -contains $RoleName) {
                Write-Host "✅ $RoleName assigned successfully" -ForegroundColor Green
                return @{ Success = $true; RequiresManualStep = $false }
            }
            else {
                Write-Host "⚠️  Role assignment command succeeded but verification failed for $RoleName" -ForegroundColor Yellow
                return @{ Success = $false; RequiresManualStep = $true }
            }
        }
        else {
            # Check if it's a permission error
            if ($result -match "insufficient privileges" -or $result -match "Authorization failed" -or $result -match "does not have authorization") {
                Write-Host "⚠️  Insufficient permissions to assign $RoleName" -ForegroundColor Yellow
                Write-Host "   This role assignment needs to be completed manually by Jonathan" -ForegroundColor White
                return @{ Success = $false; RequiresManualStep = $true }
            }
            else {
                Write-Host "❌ Failed to assign $RoleName" -ForegroundColor Red
                Write-Host "   Error: $result" -ForegroundColor Yellow
                return @{ Success = $false; RequiresManualStep = $true }
            }
        }
    }
    catch {
        Write-Host "❌ Exception while assigning $RoleName" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow

        # Check if it's likely a permission issue
        if ($_.Exception.Message -match "insufficient privileges" -or $_.Exception.Message -match "Authorization failed") {
            Write-Host "   This appears to be a permission issue - manual assignment required" -ForegroundColor White
            return @{ Success = $false; RequiresManualStep = $true }
        }

        return @{ Success = $false; RequiresManualStep = $true }
    }
}

# Function to wait for role propagation
function Wait-RolePropagation {
    param([int]$Seconds = 30)
    
    Write-Host "Waiting $Seconds seconds for role propagation..." -ForegroundColor Yellow
    
    for ($i = $Seconds; $i -gt 0; $i--) {
        Write-Progress -Activity "Role Propagation" -Status "Waiting for Azure AD propagation..." -SecondsRemaining $i
        Start-Sleep -Seconds 1
    }
    
    Write-Progress -Activity "Role Propagation" -Completed
    Write-Host "✅ Role propagation wait completed" -ForegroundColor Green
}

# Function to verify role assignments
function Test-RoleAssignments {
    param([string]$PrincipalId)

    Write-Host "Verifying role assignments..." -ForegroundColor Yellow

    $storageScope = "/subscriptions/$($global:AzureConfig.SUBSCRIPTION_ID)/resourceGroups/$($global:AzureConfig.RESOURCE_GROUP)/providers/Microsoft.Storage/storageAccounts/$($global:AzureConfig.STORAGE_ACCOUNT)"

    try {
        $assignments = az role assignment list --assignee $PrincipalId --scope $storageScope --query "[].{Role:roleDefinitionName, Principal:principalName}" --output json | ConvertFrom-Json

        Write-Host "Current role assignments:" -ForegroundColor White
        if ($assignments.Count -eq 0) {
            Write-Host "  ℹ️  No role assignments found" -ForegroundColor White
        }
        else {
            foreach ($assignment in $assignments) {
                Write-Host "  ✅ $($assignment.Role)" -ForegroundColor Green
            }
        }

        # Check for required roles
        $requiredRoles = @("Storage Blob Data Contributor", "Storage Table Data Contributor")
        $assignedRoles = $assignments | ForEach-Object { $_.Role }
        $missingRoles = @()

        foreach ($role in $requiredRoles) {
            if ($assignedRoles -contains $role) {
                Write-Host "  ✅ Confirmed: $role" -ForegroundColor Green
            }
            else {
                Write-Host "  ⚠️  Missing: $role" -ForegroundColor Yellow
                $missingRoles += $role
            }
        }

        return @{
            AllRolesAssigned = ($missingRoles.Count -eq 0)
            MissingRoles = $missingRoles
            AssignedRoles = $assignedRoles
        }
    }
    catch {
        Write-Host "❌ Failed to verify role assignments" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return @{
            AllRolesAssigned = $false
            MissingRoles = @("Storage Blob Data Contributor", "Storage Table Data Contributor")
            AssignedRoles = @()
        }
    }
}

# Function to test managed identity functionality
function Test-ManagedIdentityAccess {
    param([string]$PrincipalId)
    
    Write-Host "Testing managed identity access to storage..." -ForegroundColor Yellow
    
    try {
        # Test blob access
        Write-Host "Testing blob access..." -ForegroundColor White
        $blobTest = az storage blob list --account-name $global:AzureConfig.STORAGE_ACCOUNT --container-name "fimastsales-compressed" --auth-mode login --query "[0].name" --output tsv 2>$null
        
        if ($blobTest) {
            Write-Host "✅ Blob access successful" -ForegroundColor Green
        }
        else {
            Write-Host "⚠️  Blob access test inconclusive (container may be empty)" -ForegroundColor Yellow
        }
        
        # Test table access
        Write-Host "Testing table access..." -ForegroundColor White
        $tableTest = az storage table list --account-name $global:AzureConfig.STORAGE_ACCOUNT --auth-mode login --query "[0].name" --output tsv 2>$null
        
        if ($tableTest) {
            Write-Host "✅ Table access successful" -ForegroundColor Green
        }
        else {
            Write-Host "⚠️  Table access test inconclusive (no tables may exist)" -ForegroundColor Yellow
        }
        
        return $true
    }
    catch {
        Write-Host "⚠️  Access test failed - this may be normal if containers/tables don't exist" -ForegroundColor Yellow
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to generate manual instructions for Jonathan
function Generate-ManualInstructions {
    param(
        [string]$PrincipalId,
        [array]$MissingRoles
    )

    $instructionsFile = "manual-role-assignments-for-jonathan.md"

    $instructions = @"
# Manual Role Assignments Required - For Jonathan

**Generated on:** $(Get-Date)
**Managed Identity Principal ID:** $PrincipalId
**Function App:** $($global:AzureConfig.FUNCTION_APP)
**Storage Account:** $($global:AzureConfig.STORAGE_ACCOUNT)

## Missing Role Assignments

The following role assignments could not be completed automatically due to insufficient permissions.
Jonathan needs to execute these commands:

"@

    foreach ($role in $MissingRoles) {
        $instructions += @"

### Assign $role

``````bash
az role assignment create \
  --assignee "$PrincipalId" \
  --role "$role" \
  --scope "/subscriptions/$($global:AzureConfig.SUBSCRIPTION_ID)/resourceGroups/$($global:AzureConfig.RESOURCE_GROUP)/providers/Microsoft.Storage/storageAccounts/$($global:AzureConfig.STORAGE_ACCOUNT)"
``````

"@
    }

    $instructions += @"

## Verification Commands

After assigning the roles, Jonathan can verify with:

``````bash
# Check all role assignments for the managed identity
az role assignment list \
  --assignee "$PrincipalId" \
  --scope "/subscriptions/$($global:AzureConfig.SUBSCRIPTION_ID)/resourceGroups/$($global:AzureConfig.RESOURCE_GROUP)/providers/Microsoft.Storage/storageAccounts/$($global:AzureConfig.STORAGE_ACCOUNT)" \
  --query "[].{Role:roleDefinitionName, Principal:principalName}" \
  --output table
``````

## Alternative: Azure Portal Method

1. Navigate to Azure Portal → Storage Accounts → $($global:AzureConfig.STORAGE_ACCOUNT)
2. Click "Access control (IAM)" in the left menu
3. Click "Add" → "Add role assignment"
4. For each missing role:
   - Select the role name
   - Assign access to: "Managed Identity"
   - Select: "$($global:AzureConfig.FUNCTION_APP)" (System-assigned)
   - Click "Save"

## After Completion

Once Jonathan has assigned these roles:
1. Wait 5-10 minutes for role propagation
2. Re-run the testing script: ``.\test-implementation.ps1``
3. The function app should then have full access to storage
"@

    $instructions | Out-File -FilePath $instructionsFile -Encoding UTF8
    Write-Host "📋 Manual instructions generated: $instructionsFile" -ForegroundColor Cyan
    Write-Host "   Send this file to Jonathan to complete role assignments" -ForegroundColor White

    return $instructionsFile
}

# Main execution function
function Configure-ManagedIdentity {
    Write-Host "Starting managed identity configuration..." -ForegroundColor White
    Write-Host ""

    # Step 1: Check existing managed identity
    $principalId = Test-ManagedIdentity

    # Step 2: Enable managed identity if needed
    if (-not $principalId) {
        Write-Host ""
        $principalId = Enable-ManagedIdentity

        if (-not $principalId) {
            Write-Host "❌ Cannot proceed without managed identity" -ForegroundColor Red
            return @{ Success = $false; RequiresManualSteps = $false; PrincipalId = $null }
        }
    }

    Write-Host ""
    Write-Host "Using Principal ID: $principalId" -ForegroundColor White
    Write-Host ""

    # Step 3: Attempt to assign required storage roles
    $blobRoleResult = Grant-StorageRole -PrincipalId $principalId -RoleName "Storage Blob Data Contributor"
    Write-Host ""

    $tableRoleResult = Grant-StorageRole -PrincipalId $principalId -RoleName "Storage Table Data Contributor"
    Write-Host ""

    # Track which roles need manual assignment
    $manualRoles = @()
    if (-not $blobRoleResult.Success) { $manualRoles += "Storage Blob Data Contributor" }
    if (-not $tableRoleResult.Success) { $manualRoles += "Storage Table Data Contributor" }

    # Step 4: Wait for role propagation (if any roles were assigned)
    if ($blobRoleResult.Success -or $tableRoleResult.Success) {
        Wait-RolePropagation
        Write-Host ""
    }

    # Step 5: Verify role assignments
    $roleVerification = Test-RoleAssignments -PrincipalId $principalId
    Write-Host ""

    # Step 6: Generate manual instructions if needed
    if ($roleVerification.MissingRoles.Count -gt 0) {
        Write-Host "⚠️  Some role assignments require manual completion by Jonathan" -ForegroundColor Yellow
        $instructionsFile = Generate-ManualInstructions -PrincipalId $principalId -MissingRoles $roleVerification.MissingRoles
        Write-Host ""
    }

    # Step 7: Test access (optional, may fail if roles not assigned)
    if ($roleVerification.AllRolesAssigned) {
        Test-ManagedIdentityAccess -PrincipalId $principalId
        Write-Host ""
    }
    else {
        Write-Host "⏭️  Skipping access test - roles not fully assigned yet" -ForegroundColor Yellow
        Write-Host ""
    }

    # Determine overall success
    if ($roleVerification.AllRolesAssigned) {
        Write-Host "✅ MANAGED IDENTITY CONFIGURATION COMPLETED" -ForegroundColor Green
        Write-Host "   Principal ID: $principalId" -ForegroundColor White
        Write-Host "   All required roles assigned and verified" -ForegroundColor White

        # Export principal ID for use in other scripts
        $env:PRINCIPAL_ID = $principalId

        return @{ Success = $true; RequiresManualSteps = $false; PrincipalId = $principalId }
    }
    elseif ($principalId) {
        Write-Host "✅ MANAGED IDENTITY PARTIALLY CONFIGURED" -ForegroundColor Yellow
        Write-Host "   Principal ID: $principalId" -ForegroundColor White
        Write-Host "   ⚠️  Some role assignments require manual completion by Jonathan" -ForegroundColor Yellow
        Write-Host "   📋 Check the generated manual instructions file" -ForegroundColor White

        # Export principal ID for use in other scripts
        $env:PRINCIPAL_ID = $principalId

        return @{ Success = $true; RequiresManualSteps = $true; PrincipalId = $principalId; MissingRoles = $roleVerification.MissingRoles }
    }
    else {
        Write-Host "❌ MANAGED IDENTITY CONFIGURATION FAILED" -ForegroundColor Red
        Write-Host "   Could not enable managed identity" -ForegroundColor Yellow
        return @{ Success = $false; RequiresManualSteps = $false; PrincipalId = $null }
    }
}

# Execute if script is run directly
if ($MyInvocation.InvocationName -ne '.') {
    $result = Configure-ManagedIdentity

    if ($result.Success -and -not $result.RequiresManualSteps) {
        Write-Host "✅ Configuration completed successfully" -ForegroundColor Green
        exit 0
    }
    elseif ($result.Success -and $result.RequiresManualSteps) {
        Write-Host "⚠️  Configuration partially completed - manual steps required" -ForegroundColor Yellow
        exit 0  # Still exit 0 so implementation can continue
    }
    else {
        Write-Host "❌ Configuration failed" -ForegroundColor Red
        exit 1
    }
}

# Export function for use in other scripts
$global:ManagedIdentityConfig = @{
    Configure = ${function:Configure-ManagedIdentity}
    Test = ${function:Test-ManagedIdentity}
    Enable = ${function:Enable-ManagedIdentity}
    GrantRole = ${function:Grant-StorageRole}
    TestAccess = ${function:Test-ManagedIdentityAccess}
}
