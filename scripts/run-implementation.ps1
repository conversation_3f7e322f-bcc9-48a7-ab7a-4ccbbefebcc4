# Azure Network Security Implementation - Master Execution Script
# This script orchestrates the complete network security implementation

param(
    [switch]$SkipPermissionCheck,
    [switch]$SkipBackup,
    [switch]$SkipTesting,
    [switch]$DryRun,
    [switch]$Interactive
)

Write-Host "=== AZURE NETWORK SECURITY IMPLEMENTATION ===" -ForegroundColor Cyan
Write-Host "Phase 1: Managed Identity + Storage Network Rules" -ForegroundColor White
Write-Host ""

# Import all required modules
$scriptPath = $PSScriptRoot
. "$scriptPath\azure-config.ps1"
. "$scriptPath\verify-permissions.ps1"
. "$scriptPath\backup-configuration.ps1"
. "$scriptPath\configure-managed-identity.ps1"
. "$scriptPath\configure-storage-network.ps1"
. "$scriptPath\configure-function-app.ps1"
. "$scriptPath\test-implementation.ps1"
. "$scriptPath\emergency-rollback.ps1"

# Implementation tracking
$global:ImplementationStatus = @{
    StartTime = Get-Date
    Steps = @()
    CurrentStep = 0
    TotalSteps = 7
    Success = $false
    BackupDirectory = ""
    RequiresManualSteps = $false
    ManualStepsFiles = @()
}

# Function to log implementation step
function Log-ImplementationStep {
    param(
        [string]$StepName,
        [string]$Status,  # "STARTED", "COMPLETED", "FAILED", "SKIPPED"
        [string]$Message = "",
        [string]$Details = ""
    )
    
    $step = @{
        StepNumber = $global:ImplementationStatus.CurrentStep
        StepName = $StepName
        Status = $Status
        Message = $Message
        Details = $Details
        Timestamp = Get-Date
    }
    
    $global:ImplementationStatus.Steps += $step
    
    $statusColor = switch ($Status) {
        "STARTED" { "Yellow" }
        "COMPLETED" { "Green" }
        "FAILED" { "Red" }
        "SKIPPED" { "Gray" }
        default { "White" }
    }
    
    $prefix = switch ($Status) {
        "STARTED" { "[>]" }
        "COMPLETED" { "[+]" }
        "FAILED" { "[X]" }
        "SKIPPED" { "[-]" }
        default { "[i]" }
    }
    
    Write-Host "$prefix Step $($global:ImplementationStatus.CurrentStep)/$($global:ImplementationStatus.TotalSteps): $StepName - $Status" -ForegroundColor $statusColor
    if ($Message) {
        Write-Host "   $Message" -ForegroundColor White
    }
    if ($Details) {
        Write-Host "   Details: $Details" -ForegroundColor Gray
    }
}

# Function to handle step failure
function Handle-StepFailure {
    param(
        [string]$StepName,
        [string]$ErrorMessage,
        [switch]$Critical
    )
    
    Log-ImplementationStep -StepName $StepName -Status "FAILED" -Message $ErrorMessage
    
    if ($Critical) {
        Write-Host ""
        Write-Host "CRITICAL FAILURE: Implementation cannot continue" -ForegroundColor Red
        Write-Host "   Use emergency rollback if needed: .\emergency-rollback.ps1" -ForegroundColor Yellow
        return $false
    }
    else {
        Write-Host ""
        Write-Host "Non-critical failure: Implementation will continue" -ForegroundColor Yellow
        if ($Interactive) {
            $continue = Read-Host "Continue with implementation? (yes/no)"
            return ($continue -eq "yes")
        }
        return $true
    }
}

# Function to confirm implementation start
function Confirm-ImplementationStart {
    if ($DryRun) {
        Write-Host "[DRY RUN] No changes will be made" -ForegroundColor Yellow
        return $true
    }
    
    Write-Host "WARNING: This will modify Azure resources!" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Target Resources:" -ForegroundColor White
    Write-Host "  Subscription: $($global:AzureConfig.SUBSCRIPTION_ID)" -ForegroundColor White
    Write-Host "  Resource Group: $($global:AzureConfig.RESOURCE_GROUP)" -ForegroundColor White
    Write-Host "  Storage Account: $($global:AzureConfig.STORAGE_ACCOUNT)" -ForegroundColor White
    Write-Host "  Function App: $($global:AzureConfig.FUNCTION_APP)" -ForegroundColor White
    Write-Host ""
    Write-Host "Changes to be made:" -ForegroundColor White
    Write-Host "  [+] Enable managed identity on function app" -ForegroundColor White
    Write-Host "  [+] Assign storage roles to managed identity" -ForegroundColor White
    Write-Host "  [+] Configure storage network rules (Deny default, Allow Azure services)" -ForegroundColor White
    Write-Host "  [+] Update function app to use managed identity authentication" -ForegroundColor White
    Write-Host ""
    
    if ($Interactive) {
        $confirm = Read-Host "Proceed with implementation? (yes/no)"
        return ($confirm -eq "yes")
    }
    
    return $true
}

# Main implementation function
function Start-NetworkSecurityImplementation {
    Write-Host "Starting network security implementation..." -ForegroundColor White
    Write-Host "Estimated time: 15-20 minutes" -ForegroundColor White
    Write-Host ""
    
    # Confirm start
    if (-not (Confirm-ImplementationStart)) {
        Write-Host "Implementation cancelled by user" -ForegroundColor Yellow
        return $false
    }
    
    Write-Host ""
    
    try {
        # Step 1: Initialize Environment
        $global:ImplementationStatus.CurrentStep = 1
        Log-ImplementationStep -StepName "Initialize Environment" -Status "STARTED"
        
        if ($DryRun) {
            Log-ImplementationStep -StepName "Initialize Environment" -Status "SKIPPED" -Message "Dry run mode"
        }
        else {
            $initOk = $global:AzureConfig.Initialize.Invoke()
            if ($initOk) {
                Log-ImplementationStep -StepName "Initialize Environment" -Status "COMPLETED" -Message "Azure environment ready"
            }
            else {
                return Handle-StepFailure -StepName "Initialize Environment" -ErrorMessage "Failed to initialize Azure environment" -Critical
            }
        }
        
        Write-Host ""
        
        # Step 2: Verify Permissions
        $global:ImplementationStatus.CurrentStep = 2
        Log-ImplementationStep -StepName "Verify Permissions" -Status "STARTED"
        
        if ($SkipPermissionCheck) {
            Log-ImplementationStep -StepName "Verify Permissions" -Status "SKIPPED" -Message "Permission check skipped by user"
        }
        elseif ($DryRun) {
            Log-ImplementationStep -StepName "Verify Permissions" -Status "SKIPPED" -Message "Dry run mode"
        }
        else {
            # Run permission verification (this will exit with code 1 if permissions are missing)
            $permissionResult = & "$scriptPath\verify-permissions.ps1"
            if ($LASTEXITCODE -eq 0) {
                Log-ImplementationStep -StepName "Verify Permissions" -Status "COMPLETED" -Message "All required permissions confirmed"
            }
            else {
                return Handle-StepFailure -StepName "Verify Permissions" -ErrorMessage "Missing required Azure permissions" -Critical
            }
        }
        
        Write-Host ""
        
        # Step 3: Backup Configuration
        $global:ImplementationStatus.CurrentStep = 3
        Log-ImplementationStep -StepName "Backup Configuration" -Status "STARTED"
        
        if ($SkipBackup) {
            Log-ImplementationStep -StepName "Backup Configuration" -Status "SKIPPED" -Message "Backup skipped by user"
        }
        elseif ($DryRun) {
            Log-ImplementationStep -StepName "Backup Configuration" -Status "SKIPPED" -Message "Dry run mode"
        }
        else {
            $backupResult = & "$scriptPath\backup-configuration.ps1"
            if ($LASTEXITCODE -eq 0) {
                # Find the backup directory
                $backupDirs = Get-ChildItem -Directory -Name "backup-*" | Sort-Object -Descending
                if ($backupDirs.Count -gt 0) {
                    $global:ImplementationStatus.BackupDirectory = $backupDirs[0]
                }
                Log-ImplementationStep -StepName "Backup Configuration" -Status "COMPLETED" -Message "Configuration backed up successfully" -Details "Backup: $($global:ImplementationStatus.BackupDirectory)"
            }
            else {
                $continue = Handle-StepFailure -StepName "Backup Configuration" -ErrorMessage "Backup failed - proceeding without backup"
                if (-not $continue) { return $false }
            }
        }
        
        Write-Host ""
        
        # Step 4: Configure Managed Identity
        $global:ImplementationStatus.CurrentStep = 4
        Log-ImplementationStep -StepName "Configure Managed Identity" -Status "STARTED"

        if ($DryRun) {
            Log-ImplementationStep -StepName "Configure Managed Identity" -Status "SKIPPED" -Message "Dry run mode"
        }
        else {
            $identityResult = & "$scriptPath\configure-managed-identity.ps1"
            if ($LASTEXITCODE -eq 0) {
                # Check if manual steps are required
                if (Test-Path "manual-role-assignments-for-jonathan.md") {
                    Log-ImplementationStep -StepName "Configure Managed Identity" -Status "COMPLETED" -Message "Managed identity configured - some role assignments require manual completion" -Details "Check manual-role-assignments-for-jonathan.md"
                    $global:ImplementationStatus.RequiresManualSteps = $true
                }
                else {
                    Log-ImplementationStep -StepName "Configure Managed Identity" -Status "COMPLETED" -Message "Managed identity fully configured with storage roles"
                }
            }
            else {
                return Handle-StepFailure -StepName "Configure Managed Identity" -ErrorMessage "Failed to configure managed identity" -Critical
            }
        }
        
        Write-Host ""
        
        # Step 5: Configure Storage Network Rules
        $global:ImplementationStatus.CurrentStep = 5
        Log-ImplementationStep -StepName "Configure Storage Network" -Status "STARTED"
        
        if ($DryRun) {
            Log-ImplementationStep -StepName "Configure Storage Network" -Status "SKIPPED" -Message "Dry run mode"
        }
        else {
            $storageResult = & "$scriptPath\configure-storage-network.ps1"
            if ($LASTEXITCODE -eq 0) {
                Log-ImplementationStep -StepName "Configure Storage Network" -Status "COMPLETED" -Message "Storage network rules configured (Deny default, Allow Azure services)"
            }
            else {
                return Handle-StepFailure -StepName "Configure Storage Network" -ErrorMessage "Failed to configure storage network rules" -Critical
            }
        }
        
        Write-Host ""
        
        # Step 6: Configure Function App
        $global:ImplementationStatus.CurrentStep = 6
        Log-ImplementationStep -StepName "Configure Function App" -Status "STARTED"
        
        if ($DryRun) {
            Log-ImplementationStep -StepName "Configure Function App" -Status "SKIPPED" -Message "Dry run mode"
        }
        else {
            $functionResult = & "$scriptPath\configure-function-app.ps1"
            if ($LASTEXITCODE -eq 0) {
                Log-ImplementationStep -StepName "Configure Function App" -Status "COMPLETED" -Message "Function app configured for managed identity authentication"
            }
            else {
                return Handle-StepFailure -StepName "Configure Function App" -ErrorMessage "Failed to configure function app" -Critical
            }
        }
        
        Write-Host ""
        
        # Step 7: Test Implementation
        $global:ImplementationStatus.CurrentStep = 7
        Log-ImplementationStep -StepName "Test Implementation" -Status "STARTED"
        
        if ($SkipTesting) {
            Log-ImplementationStep -StepName "Test Implementation" -Status "SKIPPED" -Message "Testing skipped by user"
        }
        elseif ($DryRun) {
            Log-ImplementationStep -StepName "Test Implementation" -Status "SKIPPED" -Message "Dry run mode"
        }
        else {
            $testResult = & "$scriptPath\test-implementation.ps1"
            $testExitCode = $LASTEXITCODE
            
            if ($testExitCode -eq 0) {
                Log-ImplementationStep -StepName "Test Implementation" -Status "COMPLETED" -Message "All tests passed - implementation successful"
            }
            elseif ($testExitCode -eq 1) {
                Log-ImplementationStep -StepName "Test Implementation" -Status "COMPLETED" -Message "Implementation mostly successful with some warnings"
            }
            else {
                $continue = Handle-StepFailure -StepName "Test Implementation" -ErrorMessage "Some tests failed - review test report"
                if (-not $continue) { return $false }
            }
        }
        
        $global:ImplementationStatus.Success = $true
        return $true
        
    }
    catch {
        Write-Host ""
        Write-Host "UNEXPECTED ERROR DURING IMPLEMENTATION" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "   Use emergency rollback if needed: .\emergency-rollback.ps1" -ForegroundColor Yellow
        return $false
    }
}

# Function to generate implementation report
function Generate-ImplementationReport {
    $endTime = Get-Date
    $duration = $endTime - $global:ImplementationStatus.StartTime
    
    Write-Host ""
    Write-Host "=== IMPLEMENTATION REPORT ===" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Start Time: $($global:ImplementationStatus.StartTime)" -ForegroundColor White
    Write-Host "End Time: $endTime" -ForegroundColor White
    Write-Host "Duration: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor White
    Write-Host ""
    
    Write-Host "Implementation Steps:" -ForegroundColor White
    foreach ($step in $global:ImplementationStatus.Steps) {
        $statusColor = switch ($step.Status) {
            "COMPLETED" { "Green" }
            "FAILED" { "Red" }
            "SKIPPED" { "Gray" }
            default { "White" }
        }
        
        Write-Host "  $($step.StepNumber). $($step.StepName): $($step.Status)" -ForegroundColor $statusColor
        if ($step.Message) {
            Write-Host "     $($step.Message)" -ForegroundColor White
        }
    }
    
    Write-Host ""
    
    if ($global:ImplementationStatus.Success -and -not $global:ImplementationStatus.RequiresManualSteps) {
        Write-Host "IMPLEMENTATION COMPLETED SUCCESSFULLY!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Next Steps:" -ForegroundColor White
        Write-Host "  1. Monitor function app for 24-48 hours" -ForegroundColor White
        Write-Host "  2. Review any test warnings in the test report" -ForegroundColor White
        Write-Host "  3. Consider implementing Phase 2 (VNet integration) if needed" -ForegroundColor White
        Write-Host "  4. Schedule regular permission audits" -ForegroundColor White

        if ($global:ImplementationStatus.BackupDirectory) {
            Write-Host ""
            Write-Host "Backup Information:" -ForegroundColor White
            Write-Host "  Backup Directory: $($global:ImplementationStatus.BackupDirectory)" -ForegroundColor White
            Write-Host "  Keep backup until implementation is verified stable" -ForegroundColor White
        }
    }
    elseif ($global:ImplementationStatus.Success -and $global:ImplementationStatus.RequiresManualSteps) {
        Write-Host "IMPLEMENTATION MOSTLY COMPLETED!" -ForegroundColor Yellow
        Write-Host "   Some manual steps are required to finish the configuration" -ForegroundColor White
        Write-Host ""
        Write-Host "MANUAL STEPS REQUIRED:" -ForegroundColor Cyan
        Write-Host "  1. Check for 'manual-role-assignments-for-jonathan.md' file" -ForegroundColor White
        Write-Host "  2. Send this file to Jonathan to complete role assignments" -ForegroundColor White
        Write-Host "  3. Wait 5-10 minutes after Jonathan completes the assignments" -ForegroundColor White
        Write-Host "  4. Run the test script to verify: .\test-implementation.ps1" -ForegroundColor White
        Write-Host ""
        Write-Host "What was completed automatically:" -ForegroundColor White
        Write-Host "  [+] Managed identity enabled on function app" -ForegroundColor Green
        Write-Host "  [+] Storage network rules configured (secure access)" -ForegroundColor Green
        Write-Host "  [+] Function app configured for managed identity authentication" -ForegroundColor Green
        Write-Host "  [!] Storage role assignments need manual completion" -ForegroundColor Yellow

        if ($global:ImplementationStatus.BackupDirectory) {
            Write-Host ""
            Write-Host "Backup Information:" -ForegroundColor White
            Write-Host "  Backup Directory: $($global:ImplementationStatus.BackupDirectory)" -ForegroundColor White
            Write-Host "  Keep backup until implementation is verified stable" -ForegroundColor White
        }
    }
    else {
        Write-Host "IMPLEMENTATION FAILED OR INCOMPLETE" -ForegroundColor Red
        Write-Host ""
        Write-Host "Recovery Options:" -ForegroundColor White
        Write-Host "  1. Review error messages above" -ForegroundColor White
        Write-Host "  2. Use emergency rollback: .\emergency-rollback.ps1" -ForegroundColor White
        Write-Host "  3. Restore from backup if available" -ForegroundColor White
        Write-Host "  4. Contact support with implementation report" -ForegroundColor White
    }
    
    # Save report to file
    $reportFile = "implementation-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
    $global:ImplementationStatus | ConvertTo-Json -Depth 10 | Out-File -FilePath $reportFile -Encoding UTF8
    Write-Host ""
    Write-Host "Implementation report saved to: $reportFile" -ForegroundColor White
}

# Main execution
if ($MyInvocation.InvocationName -ne '.') {
    Write-Host "Azure Network Security Implementation" -ForegroundColor Cyan
    Write-Host "Based on NetworkSecurityImplementationPlan.md" -ForegroundColor White
    Write-Host ""
    
    if ($DryRun) {
        Write-Host "[DRY RUN MODE ENABLED]" -ForegroundColor Yellow
        Write-Host "   No actual changes will be made to Azure resources" -ForegroundColor White
        Write-Host ""
    }
    
    $success = Start-NetworkSecurityImplementation
    
    Generate-ImplementationReport
    
    if ($success) {
        exit 0
    }
    else {
        exit 1
    }
}
