# Azure Network Security Implementation Scripts

This directory contains a complete set of PowerShell scripts for implementing network security changes according to the NetworkSecurityImplementationPlan.md.

## Overview

The implementation consists of 9 specialized scripts that work together to:
1. Enable managed identity on the Azure Function App
2. Assign required storage roles to the managed identity
3. Configure storage account network rules (Deny default, Allow Azure services)
4. Update function app settings for managed identity authentication
5. Provide comprehensive testing and rollback capabilities

## Prerequisites

Before running these scripts, ensure:
- Azure CLI is installed and authenticated
- You have the required Azure permissions (see Permission Requirements below)
- PowerShell 5.1 or later is available

## Permission Requirements

### Required Permissions (Critical)
- **Contributor** on Resource Group: `Data_API` ✅
- **Storage Account Contributor** on Storage Account: `sagedw` ✅

### Optional Permissions (For Full Automation)
- **User Access Administrator** on Resource Group: `Data_API` ❌ (Not granted)

**Note:** The implementation has been modified to work without the "User Access Administrator" role. Role assignments to the managed identity will need to be completed manually by <PERSON> after the automated implementation finishes.

## Script Descriptions

### Core Implementation Scripts

1. **`azure-config.ps1`** - Configuration and environment setup
   - Sets up all Azure resource variables
   - Validates Azure CLI installation and authentication
   - Verifies target resources exist

2. **`verify-permissions.ps1`** - Permission verification
   - Checks all required Azure RBAC permissions
   - Generates commands for Jonathan to grant missing permissions
   - Must pass before implementation can proceed

3. **`backup-configuration.ps1`** - Configuration backup
   - Backs up current storage account and function app settings
   - Creates restoration scripts for rollback
   - Essential safety measure before making changes

4. **`configure-managed-identity.ps1`** - Managed identity setup
   - Enables system-assigned managed identity on function app
   - Assigns Storage Blob Data Contributor and Storage Table Data Contributor roles
   - Waits for role propagation and verifies assignments

5. **`configure-storage-network.ps1`** - Storage network rules
   - Sets storage account default action to Deny
   - Enables trusted Microsoft services bypass
   - Validates network configuration

6. **`configure-function-app.ps1`** - Function app configuration
   - Removes old connection string authentication
   - Sets managed identity configuration settings
   - Restarts function app and verifies health

### Testing and Validation

7. **`test-implementation.ps1`** - Comprehensive testing
   - Tests managed identity configuration
   - Validates storage network rules
   - Verifies function app settings
   - Tests storage access with managed identity
   - Tests function endpoint functionality
   - Validates security configuration
   - Generates detailed test report

### Emergency Procedures

8. **`emergency-rollback.ps1`** - Emergency rollback capabilities
   - Immediate storage access restoration (sets default action to Allow)
   - Function app settings restoration
   - Complete rollback of all changes
   - Interactive emergency menu

### Master Orchestration

9. **`run-implementation.ps1`** - Master execution script
   - Orchestrates all implementation steps in correct sequence
   - Provides progress tracking and error handling
   - Supports dry-run mode for testing
   - Generates comprehensive implementation report

## Usage Instructions

### Quick Start (Recommended)

1. **Verify permissions first:**
   ```powershell
   .\verify-permissions.ps1
   ```
   This should now pass with warnings about the missing "User Access Administrator" role.

2. **Run the complete implementation:**
   ```powershell
   .\run-implementation.ps1 -Interactive
   ```

3. **Complete manual steps (if required):**
   - Check for `manual-role-assignments-for-jonathan.md` file
   - Send this file to Jonathan to complete role assignments
   - Wait 5-10 minutes after Jonathan completes the assignments
   - Run `.\test-implementation.ps1` to verify everything works

4. **If issues occur, use emergency rollback:**
   ```powershell
   .\emergency-rollback.ps1
   ```

### Advanced Usage

#### Dry Run (Test without making changes)
```powershell
.\run-implementation.ps1 -DryRun
```

#### Skip certain steps
```powershell
.\run-implementation.ps1 -SkipPermissionCheck -SkipBackup
```

#### Run individual scripts
```powershell
# Just backup configuration
.\backup-configuration.ps1

# Just configure managed identity
.\configure-managed-identity.ps1

# Just test current state
.\test-implementation.ps1
```

#### Emergency scenarios
```powershell
# Immediate storage access restoration
.\emergency-rollback.ps1 -StorageOnly

# Complete rollback
.\emergency-rollback.ps1 -FullRollback

# Interactive emergency menu
.\emergency-rollback.ps1
```

## Implementation Flow

The master script follows this sequence:

1. **Initialize Environment** - Verify Azure CLI and resources
2. **Verify Permissions** - Check critical permissions (warnings for missing optional ones)
3. **Backup Configuration** - Create safety backup of current settings
4. **Configure Managed Identity** - Enable identity and attempt role assignments
5. **Configure Storage Network** - Set network rules for security
6. **Configure Function App** - Update app settings for managed identity
7. **Test Implementation** - Comprehensive validation of all changes

**Note:** If role assignments fail due to insufficient permissions, the implementation will continue and generate manual instructions for Jonathan to complete the missing steps.

## Safety Features

- **Comprehensive Backup**: All current configurations are backed up before changes
- **Permission Validation**: Scripts verify permissions before making changes
- **Step-by-Step Logging**: Detailed logging of each implementation step
- **Emergency Rollback**: Immediate rollback capabilities if issues occur
- **Dry Run Mode**: Test the implementation without making actual changes
- **Interactive Confirmations**: Optional user confirmations for critical steps

## Output Files

The scripts generate several output files:

- `backup-YYYYMMDD-HHMMSS/` - Backup directory with current configurations
- `test-report-YYYYMMDD-HHMMSS.json` - Detailed test results
- `implementation-report-YYYYMMDD-HHMMSS.json` - Complete implementation log
- `jonathan_permission_commands.sh` - Commands for Jonathan to grant permissions (if needed)
- `manual-role-assignments-for-jonathan.md` - Manual role assignment instructions (if needed)

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Run `.\verify-permissions.ps1` to check permissions
   - Contact Jonathan to grant missing permissions

2. **Function App Access Issues**
   - Use `.\test-implementation.ps1` to diagnose
   - Check if managed identity roles are properly assigned
   - Verify storage network rules allow Azure services

3. **Storage Access Blocked**
   - Use `.\emergency-rollback.ps1 -StorageOnly` for immediate access
   - Check if default action is properly set to Deny with Azure services bypass

### Emergency Procedures

If the implementation fails or causes issues:

1. **Immediate Access Restoration:**
   ```powershell
   .\emergency-rollback.ps1 -StorageOnly
   ```

2. **Complete Rollback:**
   ```powershell
   .\emergency-rollback.ps1 -FullRollback
   ```

3. **Restore from Backup:**
   ```powershell
   .\emergency-rollback.ps1 -BackupDirectory="backup-YYYYMMDD-HHMMSS"
   ```

## Success Criteria

The implementation is successful when:
- ✅ Function app has system-assigned managed identity enabled
- ✅ Managed identity has required storage roles assigned
- ✅ Storage account allows trusted Microsoft services
- ✅ Storage account default action is "Deny"
- ✅ Function app configuration uses managed identity
- ✅ Function execution completes successfully
- ✅ Files are uploaded to storage containers
- ✅ No authentication errors in function logs

## Support

- Review the generated test reports for detailed diagnostics
- Check the implementation report for step-by-step execution details
- Use emergency rollback procedures if immediate restoration is needed
- Keep backup directories until implementation is verified stable

## Next Steps After Successful Implementation

1. Monitor function app for 24-48 hours
2. Review any warnings in test reports
3. Schedule regular permission audits
4. Consider Phase 2 (VNet integration) if enhanced security is required
5. Implement configuration management using the same managed identity
