# Azure Network Security Implementation - Testing and Validation Script
# This script performs comprehensive testing of the network security implementation

# Import configuration
. "$PSScriptRoot\azure-config.ps1"

Write-Host "=== Network Security Implementation Testing ===" -ForegroundColor Cyan

# Initialize environment
if (-not $global:AzureConfig.Initialize.Invoke()) {
    Write-Host "❌ Failed to initialize Azure environment. Exiting." -ForegroundColor Red
    exit 1
}

# Test results tracking
$global:TestResults = @{
    TotalTests = 0
    PassedTests = 0
    FailedTests = 0
    WarningTests = 0
    Results = @()
}

# Function to record test result
function Record-TestResult {
    param(
        [string]$TestName,
        [string]$Status,  # "PASS", "FAIL", "WARNING"
        [string]$Message,
        [string]$Details = ""
    )
    
    $global:TestResults.TotalTests++
    
    switch ($Status) {
        "PASS" { 
            $global:TestResults.PassedTests++
            Write-Host "✅ $TestName - $Message" -ForegroundColor Green
        }
        "FAIL" { 
            $global:TestResults.FailedTests++
            Write-Host "❌ $TestName - $Message" -ForegroundColor Red
        }
        "WARNING" { 
            $global:TestResults.WarningTests++
            Write-Host "⚠️  $TestName - $Message" -ForegroundColor Yellow
        }
    }
    
    if ($Details) {
        Write-Host "   $Details" -ForegroundColor White
    }
    
    $global:TestResults.Results += @{
        TestName = $TestName
        Status = $Status
        Message = $Message
        Details = $Details
        Timestamp = Get-Date
    }
}

# Test 1: Managed Identity Configuration
function Test-ManagedIdentityConfiguration {
    Write-Host "Testing Managed Identity Configuration..." -ForegroundColor Yellow

    try {
        # Check if managed identity exists
        $identity = az functionapp identity show --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json 2>$null | ConvertFrom-Json

        if ($identity -and $identity.principalId -and $identity.principalId -ne "null") {
            Record-TestResult -TestName "Managed Identity Exists" -Status "PASS" -Message "System-assigned managed identity is enabled" -Details "Principal ID: $($identity.principalId)"

            # Check role assignments
            $storageScope = "/subscriptions/$($global:AzureConfig.SUBSCRIPTION_ID)/resourceGroups/$($global:AzureConfig.RESOURCE_GROUP)/providers/Microsoft.Storage/storageAccounts/$($global:AzureConfig.STORAGE_ACCOUNT)"

            try {
                $roleAssignments = az role assignment list --assignee $identity.principalId --scope $storageScope --output json | ConvertFrom-Json

                $requiredRoles = @("Storage Blob Data Contributor", "Storage Table Data Contributor")
                $assignedRoles = $roleAssignments | ForEach-Object { $_.roleDefinitionName }

                $missingRoles = @()
                foreach ($role in $requiredRoles) {
                    if ($assignedRoles -contains $role) {
                        Record-TestResult -TestName "Role Assignment: $role" -Status "PASS" -Message "Role is properly assigned"
                    }
                    else {
                        Record-TestResult -TestName "Role Assignment: $role" -Status "WARNING" -Message "Required role is missing - may need manual assignment by Jonathan" -Details "Check manual-role-assignments-for-jonathan.md"
                        $missingRoles += $role
                    }
                }

                # Overall role assignment status
                if ($missingRoles.Count -eq 0) {
                    Record-TestResult -TestName "All Required Roles" -Status "PASS" -Message "All required storage roles are assigned"
                }
                elseif ($missingRoles.Count -eq $requiredRoles.Count) {
                    Record-TestResult -TestName "All Required Roles" -Status "WARNING" -Message "No storage roles assigned yet - manual assignment required" -Details "Implementation can continue, but storage access will fail until roles are assigned"
                }
                else {
                    Record-TestResult -TestName "All Required Roles" -Status "WARNING" -Message "Some storage roles missing - partial manual assignment required" -Details "Missing: $($missingRoles -join ', ')"
                }
            }
            catch {
                Record-TestResult -TestName "Role Assignment Check" -Status "WARNING" -Message "Could not verify role assignments" -Details "This may be normal if roles haven't been assigned yet"
            }
        }
        else {
            Record-TestResult -TestName "Managed Identity Exists" -Status "FAIL" -Message "Managed identity is not configured"
        }
    }
    catch {
        Record-TestResult -TestName "Managed Identity Configuration" -Status "FAIL" -Message "Failed to check managed identity" -Details $_.Exception.Message
    }
}

# Test 2: Storage Network Configuration
function Test-StorageNetworkConfiguration {
    Write-Host "Testing Storage Network Configuration..." -ForegroundColor Yellow
    
    try {
        $networkConfig = az storage account show --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --query "networkRuleSet" --output json | ConvertFrom-Json
        
        # Test default action
        if ($networkConfig.defaultAction -eq "Deny") {
            Record-TestResult -TestName "Storage Default Action" -Status "PASS" -Message "Default action is set to Deny"
        }
        else {
            Record-TestResult -TestName "Storage Default Action" -Status "FAIL" -Message "Default action is not Deny" -Details "Current: $($networkConfig.defaultAction)"
        }
        
        # Test bypass setting
        if ($networkConfig.bypass -eq "AzureServices") {
            Record-TestResult -TestName "Trusted Services Bypass" -Status "PASS" -Message "Azure services bypass is enabled"
        }
        else {
            Record-TestResult -TestName "Trusted Services Bypass" -Status "FAIL" -Message "Azure services bypass is not properly configured" -Details "Current: $($networkConfig.bypass)"
        }
        
        # Check for IP rules (should be minimal for security)
        if ($networkConfig.ipRules.Count -eq 0) {
            Record-TestResult -TestName "IP Rules Configuration" -Status "PASS" -Message "No IP rules configured (secure)"
        }
        else {
            Record-TestResult -TestName "IP Rules Configuration" -Status "WARNING" -Message "$($networkConfig.ipRules.Count) IP rules configured" -Details "Review if all IP rules are necessary"
        }
        
        # Check for VNet rules
        if ($networkConfig.virtualNetworkRules.Count -eq 0) {
            Record-TestResult -TestName "VNet Rules Configuration" -Status "PASS" -Message "No VNet rules configured (as expected for Phase 1)"
        }
        else {
            Record-TestResult -TestName "VNet Rules Configuration" -Status "WARNING" -Message "$($networkConfig.virtualNetworkRules.Count) VNet rules configured" -Details "Unexpected for Phase 1 implementation"
        }
    }
    catch {
        Record-TestResult -TestName "Storage Network Configuration" -Status "FAIL" -Message "Failed to check storage network configuration" -Details $_.Exception.Message
    }
}

# Test 3: Function App Configuration
function Test-FunctionAppConfiguration {
    Write-Host "Testing Function App Configuration..." -ForegroundColor Yellow
    
    try {
        $appSettings = az functionapp config appsettings list --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json | ConvertFrom-Json
        
        # Check for managed identity settings
        $requiredSettings = @(
            "AzureWebJobsStorage__accountName",
            "AzureWebJobsStorage__blobServiceUri",
            "AzureWebJobsStorage__tableServiceUri"
        )
        
        foreach ($setting in $requiredSettings) {
            $appSetting = $appSettings | Where-Object { $_.name -eq $setting }
            if ($appSetting) {
                Record-TestResult -TestName "App Setting: $setting" -Status "PASS" -Message "Managed identity setting is configured" -Details $appSetting.value
            }
            else {
                Record-TestResult -TestName "App Setting: $setting" -Status "FAIL" -Message "Required managed identity setting is missing"
            }
        }
        
        # Check that old connection string is not present
        $oldConnectionString = $appSettings | Where-Object { $_.name -eq "AzureWebJobsStorage" -and -not $_.name.Contains("__") }
        if (-not $oldConnectionString) {
            Record-TestResult -TestName "Old Connection String Removal" -Status "PASS" -Message "Old connection string properly removed"
        }
        else {
            Record-TestResult -TestName "Old Connection String Removal" -Status "WARNING" -Message "Old connection string still present" -Details "May override managed identity settings"
        }
        
        # Check function app state
        $appState = az functionapp show --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --query "state" --output tsv
        if ($appState -eq "Running") {
            Record-TestResult -TestName "Function App State" -Status "PASS" -Message "Function app is running"
        }
        else {
            Record-TestResult -TestName "Function App State" -Status "WARNING" -Message "Function app is not running" -Details "State: $appState"
        }
    }
    catch {
        Record-TestResult -TestName "Function App Configuration" -Status "FAIL" -Message "Failed to check function app configuration" -Details $_.Exception.Message
    }
}

# Test 4: Storage Access with Managed Identity
function Test-StorageAccess {
    Write-Host "Testing Storage Access with Managed Identity..." -ForegroundColor Yellow

    # First check if managed identity has required roles
    $identity = az functionapp identity show --name $global:AzureConfig.FUNCTION_APP --resource-group $global:AzureConfig.RESOURCE_GROUP --output json 2>$null | ConvertFrom-Json

    if (-not ($identity -and $identity.principalId)) {
        Record-TestResult -TestName "Storage Access Prerequisites" -Status "FAIL" -Message "No managed identity found - cannot test storage access"
        return
    }

    # Check if roles are assigned
    $storageScope = "/subscriptions/$($global:AzureConfig.SUBSCRIPTION_ID)/resourceGroups/$($global:AzureConfig.RESOURCE_GROUP)/providers/Microsoft.Storage/storageAccounts/$($global:AzureConfig.STORAGE_ACCOUNT)"

    try {
        $roleAssignments = az role assignment list --assignee $identity.principalId --scope $storageScope --output json | ConvertFrom-Json
        $assignedRoles = $roleAssignments | ForEach-Object { $_.roleDefinitionName }

        $hasBlobRole = $assignedRoles -contains "Storage Blob Data Contributor"
        $hasTableRole = $assignedRoles -contains "Storage Table Data Contributor"

        if (-not $hasBlobRole -and -not $hasTableRole) {
            Record-TestResult -TestName "Storage Access Prerequisites" -Status "WARNING" -Message "No storage roles assigned yet - access tests will likely fail" -Details "This is expected if manual role assignment is still pending"
        }
    }
    catch {
        Record-TestResult -TestName "Storage Access Prerequisites" -Status "WARNING" -Message "Could not verify role assignments" -Details "Proceeding with access tests anyway"
    }

    try {
        # Test blob access
        Write-Host "  Testing blob access..." -ForegroundColor White
        $blobTest = az storage blob list --account-name $global:AzureConfig.STORAGE_ACCOUNT --container-name "fimastsales-compressed" --auth-mode login --query "[0].name" --output tsv 2>$null

        if ($LASTEXITCODE -eq 0) {
            if ($blobTest) {
                Record-TestResult -TestName "Blob Storage Access" -Status "PASS" -Message "Can access blob storage with managed identity" -Details "Found blob: $blobTest"
            }
            else {
                Record-TestResult -TestName "Blob Storage Access" -Status "PASS" -Message "Can access blob storage with managed identity" -Details "Container is empty but accessible"
            }
        }
        else {
            # Check if it's likely a permission issue
            if (-not $hasBlobRole) {
                Record-TestResult -TestName "Blob Storage Access" -Status "WARNING" -Message "Cannot access blob storage - likely due to missing role assignment" -Details "Storage Blob Data Contributor role needs to be assigned by Jonathan"
            }
            else {
                Record-TestResult -TestName "Blob Storage Access" -Status "FAIL" -Message "Cannot access blob storage despite having required role" -Details "May need to wait for role propagation or check network rules"
            }
        }

        # Test table access
        Write-Host "  Testing table access..." -ForegroundColor White
        $tableTest = az storage table list --account-name $global:AzureConfig.STORAGE_ACCOUNT --auth-mode login --query "[0].name" --output tsv 2>$null

        if ($LASTEXITCODE -eq 0) {
            if ($tableTest) {
                Record-TestResult -TestName "Table Storage Access" -Status "PASS" -Message "Can access table storage with managed identity" -Details "Found table: $tableTest"
            }
            else {
                Record-TestResult -TestName "Table Storage Access" -Status "PASS" -Message "Can access table storage with managed identity" -Details "No tables exist but storage is accessible"
            }
        }
        else {
            # Check if it's likely a permission issue
            if (-not $hasTableRole) {
                Record-TestResult -TestName "Table Storage Access" -Status "WARNING" -Message "Cannot access table storage - likely due to missing role assignment" -Details "Storage Table Data Contributor role needs to be assigned by Jonathan"
            }
            else {
                Record-TestResult -TestName "Table Storage Access" -Status "FAIL" -Message "Cannot access table storage despite having required role" -Details "May need to wait for role propagation or check network rules"
            }
        }
    }
    catch {
        Record-TestResult -TestName "Storage Access" -Status "WARNING" -Message "Failed to test storage access" -Details $_.Exception.Message
    }
}

# Test 5: Function Endpoint Functionality
function Test-FunctionEndpoint {
    Write-Host "Testing Function Endpoint..." -ForegroundColor Yellow
    
    try {
        $functionUrl = "https://$($global:AzureConfig.FUNCTION_APP).azurewebsites.net/api/ftpetlpipeline?code=duSM205SkJe2R8cjWyw69fo5l1u6I4Gt55n9Rdlt2mJ_AzFuwyEMIQ=="
        
        Write-Host "  Testing endpoint: $functionUrl" -ForegroundColor White
        
        $response = Invoke-WebRequest -Uri $functionUrl -Method GET -TimeoutSec 60 -ErrorAction Stop
        
        if ($response.StatusCode -eq 200) {
            Record-TestResult -TestName "Function Endpoint Response" -Status "PASS" -Message "Function endpoint responded successfully" -Details "Status: $($response.StatusCode)"
            
            # Check response content for success indicators
            $content = $response.Content
            if ($content -match "compressed_uploaded.*[1-9]" -or $content -match "decompressed_uploaded.*[1-9]") {
                Record-TestResult -TestName "Function Data Processing" -Status "PASS" -Message "Function successfully processed data" -Details "Files were uploaded"
            }
            elseif ($content -match "error" -or $content -match "failed") {
                Record-TestResult -TestName "Function Data Processing" -Status "FAIL" -Message "Function reported errors" -Details "Check function logs for details"
            }
            else {
                Record-TestResult -TestName "Function Data Processing" -Status "WARNING" -Message "Function response unclear" -Details "Manual verification recommended"
            }
        }
        else {
            Record-TestResult -TestName "Function Endpoint Response" -Status "FAIL" -Message "Function endpoint returned error" -Details "Status: $($response.StatusCode)"
        }
    }
    catch {
        Record-TestResult -TestName "Function Endpoint" -Status "FAIL" -Message "Function endpoint test failed" -Details $_.Exception.Message
    }
}

# Test 6: Security Validation
function Test-SecurityConfiguration {
    Write-Host "Testing Security Configuration..." -ForegroundColor Yellow
    
    try {
        # Test that storage is not publicly accessible
        Write-Host "  Testing storage public access..." -ForegroundColor White
        
        # Try to access storage without authentication (should fail)
        $publicTestUrl = "https://$($global:AzureConfig.STORAGE_ACCOUNT).blob.core.windows.net/fimastsales-compressed"
        
        try {
            $publicResponse = Invoke-WebRequest -Uri $publicTestUrl -Method GET -TimeoutSec 10 -ErrorAction Stop
            Record-TestResult -TestName "Storage Public Access Block" -Status "FAIL" -Message "Storage is publicly accessible" -Details "This is a security risk"
        }
        catch {
            if ($_.Exception.Response.StatusCode -eq 403 -or $_.Exception.Response.StatusCode -eq 404) {
                Record-TestResult -TestName "Storage Public Access Block" -Status "PASS" -Message "Storage properly blocks public access" -Details "Returns 403/404 as expected"
            }
            else {
                Record-TestResult -TestName "Storage Public Access Block" -Status "WARNING" -Message "Unexpected response to public access test" -Details $_.Exception.Message
            }
        }
        
        # Check storage account security settings
        $storageInfo = az storage account show --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --output json | ConvertFrom-Json
        
        if ($storageInfo.supportsHttpsTrafficOnly) {
            Record-TestResult -TestName "HTTPS Only Enforcement" -Status "PASS" -Message "Storage account enforces HTTPS only"
        }
        else {
            Record-TestResult -TestName "HTTPS Only Enforcement" -Status "WARNING" -Message "Storage account allows HTTP traffic" -Details "Consider enabling HTTPS only"
        }
        
        if ($storageInfo.minimumTlsVersion -eq "TLS1_2") {
            Record-TestResult -TestName "TLS Version" -Status "PASS" -Message "Minimum TLS version is 1.2"
        }
        else {
            Record-TestResult -TestName "TLS Version" -Status "WARNING" -Message "TLS version may not be optimal" -Details "Current: $($storageInfo.minimumTlsVersion)"
        }
    }
    catch {
        Record-TestResult -TestName "Security Configuration" -Status "FAIL" -Message "Failed to test security configuration" -Details $_.Exception.Message
    }
}

# Function to generate test report
function Generate-TestReport {
    Write-Host ""
    Write-Host "=== TEST RESULTS SUMMARY ===" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "Total Tests: $($global:TestResults.TotalTests)" -ForegroundColor White
    Write-Host "Passed: $($global:TestResults.PassedTests)" -ForegroundColor Green
    Write-Host "Failed: $($global:TestResults.FailedTests)" -ForegroundColor Red
    Write-Host "Warnings: $($global:TestResults.WarningTests)" -ForegroundColor Yellow
    
    $successRate = if ($global:TestResults.TotalTests -gt 0) { 
        [math]::Round(($global:TestResults.PassedTests / $global:TestResults.TotalTests) * 100, 1) 
    } else { 0 }
    
    Write-Host "Success Rate: $successRate%" -ForegroundColor White
    Write-Host ""
    
    # Overall assessment - adjusted for scenarios where manual steps are pending
    if ($global:TestResults.FailedTests -eq 0 -and $global:TestResults.WarningTests -le 2) {
        Write-Host "🎉 IMPLEMENTATION SUCCESSFUL" -ForegroundColor Green
        Write-Host "   Network security implementation is working correctly" -ForegroundColor White
        $overallStatus = "SUCCESS"
    }
    elseif ($global:TestResults.FailedTests -eq 0 -and $global:TestResults.WarningTests -le 6) {
        Write-Host "✅ IMPLEMENTATION MOSTLY SUCCESSFUL" -ForegroundColor Yellow
        Write-Host "   Core implementation complete, some manual steps may be pending" -ForegroundColor White
        Write-Host "   Check for role assignment warnings - Jonathan may need to complete these" -ForegroundColor White
        $overallStatus = "SUCCESS_WITH_WARNINGS"
    }
    elseif ($global:TestResults.FailedTests -le 2) {
        Write-Host "⚠️  IMPLEMENTATION PARTIALLY SUCCESSFUL" -ForegroundColor Yellow
        Write-Host "   Core infrastructure configured, some functionality pending manual steps" -ForegroundColor White
        $overallStatus = "PARTIAL_SUCCESS"
    }
    else {
        Write-Host "❌ IMPLEMENTATION FAILED" -ForegroundColor Red
        Write-Host "   Multiple critical issues need to be resolved" -ForegroundColor White
        $overallStatus = "FAILED"
    }

    # Additional guidance for manual steps
    if ($global:TestResults.WarningTests -gt 2) {
        Write-Host ""
        Write-Host "📋 MANUAL STEPS GUIDANCE:" -ForegroundColor Cyan
        Write-Host "   If you see warnings about missing role assignments:" -ForegroundColor White
        Write-Host "   1. Check for 'manual-role-assignments-for-jonathan.md' file" -ForegroundColor White
        Write-Host "   2. Send this file to Jonathan to complete role assignments" -ForegroundColor White
        Write-Host "   3. Wait 5-10 minutes after Jonathan completes the assignments" -ForegroundColor White
        Write-Host "   4. Re-run this test script to verify everything is working" -ForegroundColor White
    }
    
    # Save detailed report
    $reportFile = "test-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
    $report = @{
        timestamp = Get-Date
        overallStatus = $overallStatus
        summary = $global:TestResults
        environment = @{
            subscriptionId = $global:AzureConfig.SUBSCRIPTION_ID
            resourceGroup = $global:AzureConfig.RESOURCE_GROUP
            storageAccount = $global:AzureConfig.STORAGE_ACCOUNT
            functionApp = $global:AzureConfig.FUNCTION_APP
        }
    }
    
    $report | ConvertTo-Json -Depth 10 | Out-File -FilePath $reportFile -Encoding UTF8
    Write-Host ""
    Write-Host "Detailed test report saved to: $reportFile" -ForegroundColor White
    
    return $overallStatus
}

# Main testing function
function Start-ImplementationTesting {
    Write-Host "Starting comprehensive implementation testing..." -ForegroundColor White
    Write-Host ""
    
    # Run all tests
    Test-ManagedIdentityConfiguration
    Write-Host ""
    
    Test-StorageNetworkConfiguration
    Write-Host ""
    
    Test-FunctionAppConfiguration
    Write-Host ""
    
    Test-StorageAccess
    Write-Host ""
    
    Test-FunctionEndpoint
    Write-Host ""
    
    Test-SecurityConfiguration
    Write-Host ""
    
    # Generate final report
    $overallStatus = Generate-TestReport
    
    return $overallStatus
}

# Execute if script is run directly
if ($MyInvocation.InvocationName -ne '.') {
    $result = Start-ImplementationTesting
    
    switch ($result) {
        "SUCCESS" { exit 0 }
        "SUCCESS_WITH_WARNINGS" { exit 0 }
        "PARTIAL_SUCCESS" { exit 1 }
        "FAILED" { exit 2 }
        default { exit 3 }
    }
}

# Export functions for use in other scripts
$global:TestingFramework = @{
    RunAllTests = ${function:Start-ImplementationTesting}
    TestManagedIdentity = ${function:Test-ManagedIdentityConfiguration}
    TestStorageNetwork = ${function:Test-StorageNetworkConfiguration}
    TestFunctionApp = ${function:Test-FunctionAppConfiguration}
    TestStorageAccess = ${function:Test-StorageAccess}
    TestEndpoint = ${function:Test-FunctionEndpoint}
    TestSecurity = ${function:Test-SecurityConfiguration}
    GenerateReport = ${function:Generate-TestReport}
}
