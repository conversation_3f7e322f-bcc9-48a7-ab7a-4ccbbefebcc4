﻿#!/bin/bash
# Commands for <PERSON> to grant Azure permissions
# Generated on 08/26/2025 17:04:36

# Set variables
SUBSCRIPTION_ID="70285d5c-842e-47a9-8c4e-e714fa1fd6c1"
RESOURCE_GROUP="Data_API"
STORAGE_ACCOUNT="sagedw"
USER_EMAIL="<EMAIL>"

echo "Granting permissions to $USER_EMAIL..."

# Grant Contributor role on Resource Group
echo "Granting Contributor role on Resource Group..."
az role assignment create \
  --assignee "$USER_EMAIL" \
  --role "Contributor" \
  --scope "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP"

# Grant User Access Administrator role on Resource Group
echo "Granting User Access Administrator role on Resource Group..."
az role assignment create \
  --assignee "$USER_EMAIL" \
  --role "User Access Administrator" \
  --scope "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP"

# Grant Storage Account Contributor role
echo "Granting Storage Account Contributor role..."
az role assignment create \
  --assignee "$USER_EMAIL" \
  --role "Storage Account Contributor" \
  --scope "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Storage/storageAccounts/$STORAGE_ACCOUNT"

echo "Permissions granted successfully!"

# Verification
echo "Verifying permissions..."
az role assignment list \
  --assignee "$USER_EMAIL" \
  --query "[].{Role:roleDefinitionName, Scope:scope}" \
  --output table
