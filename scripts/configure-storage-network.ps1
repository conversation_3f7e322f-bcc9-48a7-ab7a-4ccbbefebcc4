# Azure Network Security Implementation - Storage Network Configuration Script
# This script configures storage account network rules for secure access

# Import configuration
. "$PSScriptRoot\azure-config.ps1"

Write-Host "=== Storage Network Configuration ===" -ForegroundColor Cyan

# Initialize environment
if (-not $global:AzureConfig.Initialize.Invoke()) {
    Write-Host "❌ Failed to initialize Azure environment. Exiting." -ForegroundColor Red
    exit 1
}

# Function to get current network configuration
function Get-StorageNetworkConfig {
    Write-Host "Getting current storage network configuration..." -ForegroundColor Yellow
    
    try {
        $config = az storage account show --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --query "networkRuleSet" --output json | ConvertFrom-Json
        
        Write-Host "Current Network Configuration:" -ForegroundColor White
        Write-Host "  Default Action: $($config.defaultAction)" -ForegroundColor White
        Write-Host "  Bypass: $($config.bypass)" -ForegroundColor White
        Write-Host "  IP Rules Count: $($config.ipRules.Count)" -ForegroundColor White
        Write-Host "  Virtual Network Rules Count: $($config.virtualNetworkRules.Count)" -ForegroundColor White
        
        return $config
    }
    catch {
        Write-Host "❌ Failed to get storage network configuration" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $null
    }
}

# Function to enable trusted Microsoft services bypass
function Enable-TrustedServicesAccess {
    Write-Host "Enabling trusted Microsoft services bypass..." -ForegroundColor Yellow
    
    try {
        az storage account update --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --bypass AzureServices --output none
        
        # Verify the change
        $config = az storage account show --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --query "networkRuleSet.bypass" --output tsv
        
        if ($config -eq "AzureServices") {
            Write-Host "✅ Trusted Microsoft services bypass enabled" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "❌ Failed to enable trusted services bypass" -ForegroundColor Red
            Write-Host "   Current bypass setting: $config" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "❌ Failed to enable trusted services bypass" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to set default action to Deny
function Set-DefaultActionDeny {
    Write-Host "Setting default network action to Deny..." -ForegroundColor Yellow
    
    try {
        az storage account update --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --default-action Deny --output none
        
        # Verify the change
        $config = az storage account show --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --query "networkRuleSet.defaultAction" --output tsv
        
        if ($config -eq "Deny") {
            Write-Host "✅ Default action set to Deny" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "❌ Failed to set default action to Deny" -ForegroundColor Red
            Write-Host "   Current default action: $config" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "❌ Failed to set default action to Deny" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to verify storage account supports network rules
function Test-NetworkRuleSupport {
    Write-Host "Verifying storage account supports network rules..." -ForegroundColor Yellow
    
    try {
        $storageInfo = az storage account show --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --query "{kind:kind, sku:sku.name}" --output json | ConvertFrom-Json
        
        Write-Host "Storage Account Details:" -ForegroundColor White
        Write-Host "  Kind: $($storageInfo.kind)" -ForegroundColor White
        Write-Host "  SKU: $($storageInfo.sku)" -ForegroundColor White
        
        # Check if storage account kind supports network rules
        $supportedKinds = @("StorageV2", "BlobStorage", "BlockBlobStorage", "FileStorage")
        
        if ($storageInfo.kind -in $supportedKinds) {
            Write-Host "✅ Storage account supports network rules" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "❌ Storage account kind '$($storageInfo.kind)' may not support network rules" -ForegroundColor Red
            Write-Host "   Supported kinds: $($supportedKinds -join ', ')" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "❌ Failed to verify storage account support" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to test network configuration
function Test-NetworkConfiguration {
    Write-Host "Testing network configuration..." -ForegroundColor Yellow
    
    try {
        # Get current configuration
        $config = az storage account show --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --query "networkRuleSet.{defaultAction:defaultAction, bypass:bypass}" --output json | ConvertFrom-Json
        
        $configOk = $true
        
        # Check default action
        if ($config.defaultAction -eq "Deny") {
            Write-Host "✅ Default action is Deny" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Default action is not Deny: $($config.defaultAction)" -ForegroundColor Red
            $configOk = $false
        }
        
        # Check bypass setting
        if ($config.bypass -eq "AzureServices") {
            Write-Host "✅ Trusted Microsoft services bypass enabled" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Trusted Microsoft services bypass not properly configured: $($config.bypass)" -ForegroundColor Red
            $configOk = $false
        }
        
        return $configOk
    }
    catch {
        Write-Host "❌ Failed to test network configuration" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to test function app access after network changes
function Test-FunctionAppAccess {
    Write-Host "Testing Function App access to storage..." -ForegroundColor Yellow
    
    try {
        # Test using managed identity authentication
        Write-Host "Testing blob access with managed identity..." -ForegroundColor White
        
        # Try to list blobs in the main container
        $blobTest = az storage blob list --account-name $global:AzureConfig.STORAGE_ACCOUNT --container-name "fimastsales-compressed" --auth-mode login --query "[0].name" --output tsv 2>$null
        
        if ($blobTest) {
            Write-Host "✅ Function App can access blob storage" -ForegroundColor Green
        }
        elseif ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Function App can access blob storage (container empty)" -ForegroundColor Green
        }
        else {
            Write-Host "⚠️  Blob access test inconclusive" -ForegroundColor Yellow
            Write-Host "   This may be normal if containers don't exist yet" -ForegroundColor White
        }
        
        # Test table access
        Write-Host "Testing table access with managed identity..." -ForegroundColor White
        $tableTest = az storage table list --account-name $global:AzureConfig.STORAGE_ACCOUNT --auth-mode login --query "[0].name" --output tsv 2>$null
        
        if ($tableTest) {
            Write-Host "✅ Function App can access table storage" -ForegroundColor Green
        }
        elseif ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Function App can access table storage (no tables exist)" -ForegroundColor Green
        }
        else {
            Write-Host "⚠️  Table access test inconclusive" -ForegroundColor Yellow
            Write-Host "   This may be normal if no tables exist yet" -ForegroundColor White
        }
        
        return $true
    }
    catch {
        Write-Host "⚠️  Access test failed - this may be normal during initial setup" -ForegroundColor Yellow
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to create emergency access restoration
function Enable-EmergencyAccess {
    Write-Host "⚠️  EMERGENCY: Enabling full storage access" -ForegroundColor Yellow
    Write-Host "   This will temporarily allow all connections" -ForegroundColor Yellow
    
    try {
        az storage account update --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --default-action Allow --output none
        
        $config = az storage account show --name $global:AzureConfig.STORAGE_ACCOUNT --resource-group $global:AzureConfig.RESOURCE_GROUP --query "networkRuleSet.defaultAction" --output tsv
        
        if ($config -eq "Allow") {
            Write-Host "✅ Emergency access enabled - storage allows all connections" -ForegroundColor Green
            Write-Host "   ⚠️  Remember to re-secure after troubleshooting!" -ForegroundColor Yellow
            return $true
        }
        else {
            Write-Host "❌ Failed to enable emergency access" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Failed to enable emergency access" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Main configuration function
function Configure-StorageNetwork {
    Write-Host "Starting storage network configuration..." -ForegroundColor White
    Write-Host ""
    
    # Step 1: Verify storage account supports network rules
    $supportOk = Test-NetworkRuleSupport
    if (-not $supportOk) {
        Write-Host "❌ Storage account may not support network rules" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    
    # Step 2: Get current configuration
    $currentConfig = Get-StorageNetworkConfig
    if (-not $currentConfig) {
        Write-Host "❌ Cannot proceed without current configuration" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    
    # Step 3: Enable trusted Microsoft services bypass
    $bypassOk = Enable-TrustedServicesAccess
    if (-not $bypassOk) {
        Write-Host "❌ Failed to enable trusted services bypass" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    
    # Step 4: Set default action to Deny
    $denyOk = Set-DefaultActionDeny
    if (-not $denyOk) {
        Write-Host "❌ Failed to set default action to Deny" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    
    # Step 5: Test final configuration
    $configOk = Test-NetworkConfiguration
    if (-not $configOk) {
        Write-Host "❌ Network configuration validation failed" -ForegroundColor Red
        return $false
    }
    
    Write-Host ""
    
    # Step 6: Test function app access
    Test-FunctionAppAccess
    
    Write-Host ""
    Write-Host "✅ STORAGE NETWORK CONFIGURATION COMPLETED" -ForegroundColor Green
    Write-Host "   Default Action: Deny" -ForegroundColor White
    Write-Host "   Bypass: AzureServices (trusted Microsoft services)" -ForegroundColor White
    Write-Host "   Function App should have access via managed identity" -ForegroundColor White
    
    return $true
}

# Execute if script is run directly
if ($MyInvocation.InvocationName -ne '.') {
    $success = Configure-StorageNetwork
    if ($success) {
        exit 0
    }
    else {
        Write-Host ""
        Write-Host "❌ CONFIGURATION FAILED" -ForegroundColor Red
        Write-Host "   Use Enable-EmergencyAccess if immediate access restoration is needed" -ForegroundColor Yellow
        exit 1
    }
}

# Export functions for use in other scripts
$global:StorageNetworkConfig = @{
    Configure = ${function:Configure-StorageNetwork}
    GetConfig = ${function:Get-StorageNetworkConfig}
    EnableBypass = ${function:Enable-TrustedServicesAccess}
    SetDeny = ${function:Set-DefaultActionDeny}
    TestConfig = ${function:Test-NetworkConfiguration}
    TestAccess = ${function:Test-FunctionAppAccess}
    EmergencyAccess = ${function:Enable-EmergencyAccess}
}
