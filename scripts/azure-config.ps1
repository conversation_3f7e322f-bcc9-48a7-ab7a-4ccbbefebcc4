# Azure Network Security Implementation - Configuration Script
# This script sets up all environment variables and validates Azure CLI setup

Write-Host "=== Azure Network Security Implementation - Configuration Setup ===" -ForegroundColor Cyan

# Azure Resource Configuration (from NetworkSecurityImplementationPlan.md)
$SUBSCRIPTION_ID = "70285d5c-842e-47a9-8c4e-e714fa1fd6c1"
$RESOURCE_GROUP = "Data_API"
$STORAGE_ACCOUNT = "sagedw"
$FUNCTION_APP = "func-swickard-ftp-etl"

# Export variables for use in other scripts
$env:SUBSCRIPTION_ID = $SUBSCRIPTION_ID
$env:RESOURCE_GROUP = $RESOURCE_GROUP
$env:STORAGE_ACCOUNT = $STORAGE_ACCOUNT
$env:FUNCTION_APP = $FUNCTION_APP

Write-Host "Configuration Variables Set:" -ForegroundColor Green
Write-Host "  Subscription ID: $SUBSCRIPTION_ID" -ForegroundColor White
Write-Host "  Resource Group: $RESOURCE_GROUP" -ForegroundColor White
Write-Host "  Storage Account: $STORAGE_ACCOUNT" -ForegroundColor White
Write-Host "  Function App: $FUNCTION_APP" -ForegroundColor White
Write-Host ""

# Function to check Azure CLI installation
function Test-AzureCLI {
    Write-Host "Checking Azure CLI installation..." -ForegroundColor Yellow
    try {
        $azVersion = az version --output json | ConvertFrom-Json
        Write-Host "✅ Azure CLI installed - Version: $($azVersion.'azure-cli')" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Azure CLI not found. Please install Azure CLI first." -ForegroundColor Red
        Write-Host "   Download from: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli" -ForegroundColor Yellow
        return $false
    }
}

# Function to check Azure authentication
function Test-AzureAuth {
    Write-Host "Checking Azure authentication..." -ForegroundColor Yellow
    try {
        $account = az account show --output json | ConvertFrom-Json
        if ($account.id -eq $SUBSCRIPTION_ID) {
            Write-Host "✅ Authenticated to correct subscription: $($account.name)" -ForegroundColor Green
            Write-Host "   User: $($account.user.name)" -ForegroundColor White
            return $true
        }
        else {
            Write-Host "⚠️  Authenticated but wrong subscription" -ForegroundColor Yellow
            Write-Host "   Current: $($account.id)" -ForegroundColor White
            Write-Host "   Required: $SUBSCRIPTION_ID" -ForegroundColor White
            Write-Host "   Setting correct subscription..." -ForegroundColor Yellow
            az account set --subscription $SUBSCRIPTION_ID
            return $true
        }
    }
    catch {
        Write-Host "❌ Not authenticated to Azure. Please run 'az login' first." -ForegroundColor Red
        return $false
    }
}

# Function to validate resource existence
function Test-AzureResources {
    Write-Host "Validating Azure resources exist..." -ForegroundColor Yellow
    
    # Check Resource Group
    try {
        $rg = az group show --name $RESOURCE_GROUP --output json | ConvertFrom-Json
        Write-Host "✅ Resource Group '$RESOURCE_GROUP' exists" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Resource Group '$RESOURCE_GROUP' not found" -ForegroundColor Red
        return $false
    }
    
    # Check Storage Account
    try {
        $sa = az storage account show --name $STORAGE_ACCOUNT --resource-group $RESOURCE_GROUP --output json | ConvertFrom-Json
        Write-Host "✅ Storage Account '$STORAGE_ACCOUNT' exists" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Storage Account '$STORAGE_ACCOUNT' not found" -ForegroundColor Red
        return $false
    }
    
    # Check Function App
    try {
        $fa = az functionapp show --name $FUNCTION_APP --resource-group $RESOURCE_GROUP --output json | ConvertFrom-Json
        Write-Host "✅ Function App '$FUNCTION_APP' exists" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Function App '$FUNCTION_APP' not found" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# Main validation function
function Initialize-AzureEnvironment {
    Write-Host "Initializing Azure Environment..." -ForegroundColor Cyan
    
    $cliOk = Test-AzureCLI
    if (-not $cliOk) { return $false }
    
    $authOk = Test-AzureAuth
    if (-not $authOk) { return $false }
    
    $resourcesOk = Test-AzureResources
    if (-not $resourcesOk) { return $false }
    
    Write-Host "✅ Azure environment initialized successfully!" -ForegroundColor Green
    return $true
}

# Export functions for use in other scripts
$global:AzureConfig = @{
    SUBSCRIPTION_ID = $SUBSCRIPTION_ID
    RESOURCE_GROUP = $RESOURCE_GROUP
    STORAGE_ACCOUNT = $STORAGE_ACCOUNT
    FUNCTION_APP = $FUNCTION_APP
    Initialize = ${function:Initialize-AzureEnvironment}
    TestCLI = ${function:Test-AzureCLI}
    TestAuth = ${function:Test-AzureAuth}
    TestResources = ${function:Test-AzureResources}
}

# Auto-initialize if script is run directly
if ($MyInvocation.InvocationName -ne '.') {
    Initialize-AzureEnvironment
}
