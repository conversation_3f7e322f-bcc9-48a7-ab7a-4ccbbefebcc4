# Swickard ETL Script - Project Overview

## Project Purpose

This project is a **modular ETL (Extract, Transform, Load) pipeline** designed to automatically retrieve, process, and store CSV data files from a secure SFTP server. The system is built to handle both local execution and cloud deployment on Microsoft Azure, making it suitable for enterprise data integration workflows.

### What This System Does
- **Extracts** compressed CSV files from a remote SFTP server
- **Transforms** the data by decompressing password-protected ZIP files
- **Loads** the processed files into Azure Blob Storage for further analysis

## Architecture Overview

The project consists of two main deployment modes:

### 1. Local Execution Mode
A standalone Python application that can be run on any machine with the required dependencies.

### 2. Azure Cloud Mode
An Azure Function App that runs the same ETL logic in a serverless cloud environment, providing automatic scaling and managed infrastructure.

## Key Technologies Used

### Core Python Libraries
- **paramiko**: Secure SSH/SFTP client for connecting to remote servers
- **pyzipper**: Advanced ZIP file handling with AES encryption support
- **python-dotenv**: Environment variable management for secure configuration

### Azure Services
- **Azure Functions**: Serverless compute platform for running the ETL pipeline
- **Azure Blob Storage**: Cloud object storage for processed data files
- **Azure Managed Identity**: Secure authentication without storing credentials
- **Azure Key Vault** (planned): Secure storage for sensitive configuration data

## Project Structure

```
Swickard-ETL-Script/
├── README.md                    # Basic project documentation
├── config.py                    # Local configuration management
├── orchestrator.py              # Main pipeline coordinator (local)
├── sftp_downloader.py          # SFTP file download logic (local)
├── file_decompressor.py        # ZIP file extraction logic (local)
├── logger.py                   # Logging utilities (local)
├── requirements.txt            # Python dependencies (local)
├── test_*.py                   # Testing and validation scripts
└── azure-ftp-etl/             # Azure Function App deployment
    ├── FTPETLPipeline/         # Function App entry point
    ├── shared/                 # Shared Azure-specific modules
    ├── host.json              # Azure Functions configuration
    └── requirements.txt       # Azure-specific dependencies
```

## File-by-File Breakdown

### Local Execution Files

#### `config.py`
**Purpose**: Centralized configuration management for local execution
- Loads SFTP credentials from environment variables using `python-dotenv`
- Defines file naming patterns and processing parameters
- Sets up local directory paths for compressed and decompressed files

**Key Configuration**:
```python
SFTP_CONFIG = {
    'hostname': os.getenv('SFTP_HOST'),
    'username': os.getenv('SFTP_USERNAME'), 
    'password': os.getenv('SFTP_PASSWORD'),
    'port': int(os.getenv('SFTP_PORT', 22))
}
FILE_PREFIXES = ['FIMASTSALES', 'FIMASTSALES2TEST']
```

#### `orchestrator.py`
**Purpose**: Main pipeline coordinator that executes ETL steps in sequence
- Coordinates the download and decompression processes
- Provides simple console output for monitoring progress
- Serves as the entry point for local execution

**Workflow**:
1. Downloads files from SFTP server
2. Extracts compressed files
3. Reports completion status

#### `sftp_downloader.py`
**Purpose**: Handles secure file transfer from remote SFTP server
- Uses `paramiko` library for secure SSH/SFTP connections
- Implements file pattern matching to download specific files
- Includes error handling and logging for each download operation

**Key Features**:
- Automatic host key acceptance for new servers
- Pattern-based file filtering (`{prefix}_{date}_*.csv.zip`)
- Individual file logging for audit trails

#### `file_decompressor.py`
**Purpose**: Extracts files from password-protected ZIP archives
- Supports both standard ZIP and AES-encrypted ZIP files
- Uses `pyzipper` for advanced encryption support
- Fallback mechanism for different ZIP formats

**Security Features**:
- Handles password-protected archives
- Supports AES encryption (more secure than traditional ZIP encryption)
- Graceful fallback to standard ZIP extraction

#### `logger.py`
**Purpose**: Centralized logging system for tracking operations
- Creates separate log files for each file prefix
- Provides standardized success and error logging
- Includes timestamps and detailed error information

### Azure Function App Files

#### `azure-ftp-etl/FTPETLPipeline/__init__.py`
**Purpose**: Azure Function entry point with comprehensive error handling
- HTTP-triggered function that can be called via REST API
- Step-by-step testing and validation of all components
- Detailed JSON response with operation status and results

**Testing Approach**:
1. Validates imports and dependencies
2. Tests SFTP configuration
3. Verifies Azure Storage connectivity
4. Executes the full pipeline

#### `azure-ftp-etl/shared/azure_config.py`
**Purpose**: Azure-specific configuration with cloud services integration
- Hardcoded SFTP credentials (temporary - should use Key Vault)
- Azure Blob Storage configuration using Managed Identity
- Container definitions for organized data storage

**Azure Integration**:
```python
STORAGE_ACCOUNT_URL = "https://sagedw.blob.core.windows.net"
credential = DefaultAzureCredential()  # Uses Managed Identity
blob_service_client = BlobServiceClient(account_url=STORAGE_ACCOUNT_URL, credential=credential)
```

#### `azure-ftp-etl/shared/orchestrator.py`
**Purpose**: Cloud-optimized pipeline orchestrator with blob storage integration
- Uses temporary directories for processing (serverless-friendly)
- Uploads both compressed and decompressed files to separate blob containers
- Provides detailed execution metrics and status reporting

**Enhanced Workflow**:
1. Downloads files from SFTP to temporary storage
2. Uploads compressed files to `fimastsales-compressed` container
3. Decompresses files locally
4. Uploads decompressed files to `fimastsales-decompressed` container
5. Returns comprehensive execution statistics

### Testing and Validation Files

#### `test_function_components.py`
**Purpose**: Comprehensive testing script for Azure Function components
- Tests SFTP connectivity and file detection
- Validates Azure Storage authentication and container access
- Provides clear pass/fail status for deployment readiness

## Data Flow and Processing Logic

### 1. File Discovery
The system looks for files matching this pattern:
```
{PREFIX}_{DATE}_*.csv.zip
```
Where:
- `PREFIX`: Either "FIMASTSALES" or "FIMASTSALES2TEST"
- `DATE`: Currently hardcoded as "08-18-25" (demo date)
- Files must have `.csv.zip` extension (case-insensitive)

### 2. Secure Download
- Establishes SSH connection to `reyreydaf.swickard.com:2222`
- Uses username/password authentication
- Downloads matching files to local/temporary storage

### 3. File Processing
- Attempts AES-encrypted ZIP extraction first (more secure)
- Falls back to standard ZIP extraction if needed
- Uses password: `21NQN7LXW6IVDIO9GG60M0S986LW84FLL6M96JIA`

### 4. Cloud Storage (Azure Mode)
- Compressed files → `fimastsales-compressed` container
- Decompressed files → `fimastsales-decompressed` container
- Uses Azure Managed Identity for secure, credential-free access

## Setup and Usage Instructions

### Local Setup

1. **Install Dependencies**:
```bash
pip install -r requirements.txt
```

2. **Create Environment File**:
Create a `.env` file with your SFTP credentials:
```
SFTP_HOST=your-sftp-server.com
SFTP_USERNAME=your-username
SFTP_PASSWORD=your-password
SFTP_PORT=22
ZIP_PASSWORD=your-zip-password
FILE_PREFIXES=FIMASTSALES,FIMASTSALES2TEST
BASE_PATH=/path/to/your/data/directory
```

3. **Run the Pipeline**:
```bash
python orchestrator.py
```

### Azure Deployment

1. **Deploy Function App**:
   - Use the `azure-ftp-etl` folder as your function app code
   - Configure Managed Identity for the Function App
   - Grant Storage Blob Data Contributor role to the identity

2. **Configure Storage Account**:
   - Ensure `sagedw` storage account exists
   - Create containers: `fimastsales-compressed` and `fimastsales-decompressed`

3. **Trigger Execution**:
   - HTTP POST to the function endpoint
   - Monitor execution through Azure portal logs

## Security Considerations

### Current Implementation
- SFTP credentials are hardcoded (temporary solution)
- ZIP passwords are stored in plain text
- Uses Azure Managed Identity for storage access

### Recommended Improvements
- Move all secrets to Azure Key Vault
- Implement certificate-based SFTP authentication
- Add network security groups for restricted access
- Enable Azure Storage encryption at rest

## Error Handling and Logging

### Local Mode
- Individual log files per file prefix (e.g., `FIMASTSALES.log`)
- Detailed error messages with timestamps
- Success/failure tracking for each operation

### Azure Mode
- Azure Functions built-in logging
- JSON response format with step-by-step status
- Comprehensive error details for troubleshooting

## Monitoring and Maintenance

### Key Metrics to Monitor
- Number of files downloaded per execution
- File processing success/failure rates
- Azure storage upload statistics
- Function execution duration and memory usage

### Regular Maintenance Tasks
- Review and rotate SFTP credentials
- Monitor storage account usage and costs
- Update file date patterns as needed
- Review and clean up old log files

## Future Enhancements

### Planned Improvements
1. **Security**: Migrate to Azure Key Vault for all secrets
2. **Scheduling**: Add Azure Logic Apps or Timer triggers
3. **Monitoring**: Implement Azure Application Insights
4. **Scalability**: Add support for multiple SFTP sources
5. **Data Validation**: Add CSV content validation and schema checking

This ETL pipeline provides a robust foundation for automated data integration between SFTP sources and Azure cloud storage, with both local development capabilities and enterprise-ready cloud deployment options.
