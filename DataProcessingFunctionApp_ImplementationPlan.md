# Data Processing Function App - Comprehensive Implementation Plan

## 1. Project Structure Overview

Following the same architectural pattern as the existing Data Ingesting function app:

```
azure-csv-processing/
├── host.json                           # Azure Functions configuration
├── local.settings.json                 # Local development settings
├── requirements.txt                    # Python dependencies
├── CSVProcessingPipeline/              # Function App entry point
│   ├── __init__.py                     # HTTP-triggered function
│   └── function.json                  # Function binding configuration
└── shared/                             # Shared modules (mirrors existing pattern)
    ├── azure_config.py                 # Azure services configuration
    ├── orchestrator.py                 # Pipeline coordination
    ├── csv_processor.py                # CSV reading and validation
    ├── file_consolidator.py            # Multi-file consolidation logic
    ├── data_transformer.py             # Surrogate key and audit columns
    └── blob_manager.py                 # Blob storage operations
```

## 2. Module Breakdown and Responsibilities

### 2.1 `CSVProcessingPipeline/__init__.py` (Entry Point)
**Purpose**: HTTP-triggered Azure Function with step-by-step validation
- Mirrors the existing function's comprehensive error handling approach
- Step-by-step testing and validation of all components
- Detailed JSON response with operation status and results

**Key Features**:
```python
def main(req: func.HttpRequest) -> func.HttpResponse:
    # Step 1: Test imports and dependencies
    # Step 2: Test blob storage connectivity
    # Step 3: Validate CSV file availability
    # Step 4: Execute processing pipeline
    # Step 5: Return comprehensive results
```

### 2.2 `shared/azure_config.py` (Configuration Management)
**Purpose**: Centralized configuration for Azure services and processing parameters
- Blob storage client configuration using Managed Identity
- Container definitions and file patterns
- Processing parameters and column configurations

**Configuration Structure**:
```python
# Blob Storage Configuration
STORAGE_ACCOUNT_URL = "https://sagedw.blob.core.windows.net"
credential = DefaultAzureCredential()
blob_service_client = BlobServiceClient(account_url=STORAGE_ACCOUNT_URL, credential=credential)

# Container Configuration
SOURCE_CONTAINER = "fimastsales-decompressed"      # Input from Data Ingesting
OUTPUT_CONTAINER = "fimastsales-decompressed"      # Temporary - will change later

# Processing Configuration
FILE_PATTERNS = ['FIMASTSALES', 'FIMASTSALES2TEST']
SURROGATE_KEY_COLUMN = "Surrogate Key"
AUDIT_COLUMNS = ["Source Date/Time", "Created Date/Time", "Modified Date/Time"]
```

### 2.3 `shared/orchestrator.py` (Pipeline Coordinator)
**Purpose**: Main pipeline orchestrator following the existing pattern
- Coordinates all processing steps in sequence
- Provides comprehensive logging and status reporting
- Manages temporary file operations and cleanup

**Processing Workflow**:
```python
def run_processing_pipeline():
    """Execute complete CSV processing pipeline"""
    # Step 1: Download CSV files from blob storage
    # Step 2: Process and validate CSV structure
    # Step 3: Apply consolidation logic (if multiple files)
    # Step 4: Add surrogate key column (leftmost position)
    # Step 5: Append audit columns (rightmost positions)
    # Step 6: Upload processed file back to blob storage
    # Step 7: Return processing statistics
```

### 2.4 `shared/csv_processor.py` (CSV Operations)
**Purpose**: Core CSV reading, validation, and structure management
- Read CSV files with proper encoding detection
- Validate CSV structure and data integrity
- Handle various CSV formats and edge cases

**Key Functions**:
```python
def read_csv_from_blob(blob_name, container_name)
def validate_csv_structure(df)
def detect_numeric_columns(df)
def save_csv_to_blob(df, blob_name, container_name)
```

### 2.5 `shared/file_consolidator.py` (Consolidation Logic - GREEN BOX)
**Purpose**: Implements file consolidation decision logic with comprehensive logging
- Determines if single file or multiple files exist
- Performs file consolidation when multiple files are present
- Comprehensive logging for audit trail

**Consolidation Logic**:
```python
def consolidate_files(csv_files):
    """
    Consolidation Logic (GREEN BOX - Logging Required):
    - IF 1 file: Log decision and proceed with single file
    - IF multiple files: Log consolidation process and merge files
    """
    if len(csv_files) == 1:
        logging.info(f"✓ Single CSV file detected: {csv_files[0]}")
        logging.info("Decision: Proceeding with single file processing")
        return csv_files[0]
    else:
        logging.info(f"✓ Multiple CSV files detected: {len(csv_files)} files")
        logging.info("Decision: Consolidating multiple files into single dataset")
        # Consolidation logic implementation
        return consolidated_file
```

### 2.6 `shared/data_transformer.py` (Data Transformation)
**Purpose**: Handles surrogate key addition and audit column appending
- Adds surrogate key as the FIRST column (leftmost position)
- Appends audit columns to the rightmost positions
- Maintains data integrity throughout transformations

**Key Transformations**:
```python
def add_surrogate_key(df):
    """Add Surrogate Key as the FIRST column (leftmost)"""
    # Placeholder logic: sum of first two numeric columns
    # TODO: Replace with actual business logic when requirements are finalized
    
def append_audit_columns(df, source_filename):
    """Add audit columns to the rightmost positions"""
    # Source Date/Time: Extract from filename/metadata
    # Created Date/Time: Current timestamp
    # Modified Date/Time: Same as Created Date/Time
```

### 2.7 `shared/blob_manager.py` (Blob Storage Operations)
**Purpose**: Centralized blob storage operations
- Download files from source container
- Upload processed files to destination container
- Handle blob metadata and properties

## 3. Data Flow Diagram

```mermaid
graph TD
    A[HTTP Trigger] --> B[Validate Dependencies]
    B --> C[Connect to Blob Storage]
    C --> D[List CSV Files in Source Container]
    D --> E{File Count Check}
    
    E -->|Single File| F[Log: Single File Processing]
    E -->|Multiple Files| G[Log: File Consolidation Required]
    
    F --> H[Load Single CSV]
    G --> I[Consolidate Multiple CSVs]
    I --> H
    
    H --> J[Validate CSV Structure]
    J --> K[Add Surrogate Key - LEFTMOST Column]
    K --> L[Log: Raw CSV Enhanced]
    L --> M[Append Audit Columns - RIGHTMOST]
    M --> N[Log: Transformation Completion]
    N --> O[Upload to Destination Container]
    O --> P[Return Processing Results]
    
    style F fill:#90EE90
    style G fill:#90EE90
    style L fill:#90EE90
    style N fill:#90EE90
```

## 4. Error Handling and Logging Strategy

### 4.1 Comprehensive Logging (Green Box Components)
Following the existing pattern with enhanced logging for green-boxed components:

```python
# File Consolidation Logic (GREEN BOX)
logging.info("=== FILE CONSOLIDATION LOGIC START ===")
logging.info(f"Files found: {file_count}")
logging.info(f"Consolidation decision: {'Single file processing' if file_count == 1 else 'Multi-file consolidation'}")
logging.info("=== FILE CONSOLIDATION LOGIC END ===")

# Raw CSV Enhanced (GREEN BOX)
logging.info("=== RAW CSV ENHANCED START ===")
logging.info(f"Surrogate key column added successfully: {SURROGATE_KEY_COLUMN}")
logging.info(f"Total columns after enhancement: {len(df.columns)}")
logging.info("=== RAW CSV ENHANCED END ===")

# Transformation Completion (GREEN BOX)
logging.info("=== TRANSFORMATION COMPLETION START ===")
logging.info("Landed Data to Bronze Completed")
logging.info(f"Final dataset shape: {df.shape}")
logging.info("=== TRANSFORMATION COMPLETION END ===")
```

### 4.2 Error Handling Strategy
Mirroring the existing function's robust error handling:

```python
def main(req: func.HttpRequest) -> func.HttpResponse:
    result = {"status": "processing", "steps": []}
    
    try:
        # Step 1: Test imports
        try:
            from orchestrator import run_processing_pipeline
            result["steps"].append("✓ Imports successful")
        except Exception as e:
            result["steps"].append(f"❌ Import failed: {str(e)}")
            return func.HttpResponse(json.dumps(result), status_code=200)
        
        # Continue with step-by-step validation...
        
    except Exception as e:
        logging.error(f"Function failed: {str(e)}")
        return func.HttpResponse(
            json.dumps({"status": "error", "message": str(e)}),
            status_code=500
        )
```

## 5. Configuration Management Approach

### 5.1 Environment-Specific Configuration
Following the existing pattern with Azure-specific and local configurations:

```python
# azure_config.py
import os
from azure.storage.blob import BlobServiceClient
from azure.identity import DefaultAzureCredential

# Processing Configuration
PROCESSING_CONFIG = {
    'source_container': 'fimastsales-decompressed',
    'output_container': 'fimastsales-decompressed',  # Temporary
    'file_patterns': ['FIMASTSALES', 'FIMASTSALES2TEST'],
    'surrogate_key_logic': 'placeholder',  # TBD
    'consolidation_method': 'concat'  # pandas.concat for multiple files
}

# Blob Storage Configuration
STORAGE_ACCOUNT_URL = "https://sagedw.blob.core.windows.net"
credential = DefaultAzureCredential()
blob_service_client = BlobServiceClient(account_url=STORAGE_ACCOUNT_URL, credential=credential)
```

### 5.2 Future Configuration Flexibility
Design for easy configuration changes:

```python
# Easy destination container change
def get_output_container():
    """Returns output container - easily configurable for future changes"""
    return os.getenv('OUTPUT_CONTAINER', PROCESSING_CONFIG['output_container'])

# Flexible surrogate key logic
def get_surrogate_key_columns():
    """Returns columns for surrogate key calculation - TBD implementation"""
    return os.getenv('SURROGATE_KEY_COLUMNS', 'placeholder_logic')
```

## 6. Testing Strategy

### 6.1 Component Testing
Create comprehensive test scripts following the existing pattern:

```python
# test_csv_processing_components.py
def test_blob_connectivity():
    """Test Azure Blob Storage connection and container access"""

def test_csv_reading():
    """Test CSV file reading from blob storage"""

def test_consolidation_logic():
    """Test file consolidation decision making"""

def test_surrogate_key_addition():
    """Test surrogate key column addition as leftmost column"""

def test_audit_columns():
    """Test audit column appending"""

def test_end_to_end_processing():
    """Test complete processing pipeline"""
```

### 6.2 Integration Testing
Test integration with the existing Data Ingesting function app:

```python
# test_integration.py
def test_data_handoff():
    """Test reading files produced by Data Ingesting function"""

def test_blob_container_integration():
    """Test reading from fimastsales-decompressed container"""
```

## 7. Deployment Considerations

### 7.1 Azure Function App Configuration
Following the existing deployment pattern:

```json
// host.json
{
  "version": "2.0",
  "functionTimeout": "00:10:00",
  "extensions": {
    "http": {
      "routePrefix": "api"
    }
  }
}
```

### 7.2 Dependencies and Requirements
```txt
# requirements.txt
azure-functions
azure-storage-blob
azure-identity
pandas
numpy
python-dateutil
```

### 7.3 Managed Identity Configuration
- Configure Managed Identity for the new Function App
- Grant Storage Blob Data Contributor role for the `sagedw` storage account
- Ensure access to both source and destination containers

## 8. Implementation Phases

### Phase 1: Core Infrastructure
1. Create Azure Function App structure
2. Implement basic blob storage connectivity
3. Create CSV reading and validation functionality

### Phase 2: Processing Logic
1. Implement file consolidation logic with comprehensive logging
2. Add surrogate key column functionality (leftmost position)
3. Implement audit column appending

### Phase 3: Integration and Testing
1. End-to-end pipeline testing
2. Integration testing with Data Ingesting function app
3. Performance optimization and error handling refinement

### Phase 4: Deployment and Monitoring
1. Deploy to Azure with proper Managed Identity configuration
2. Implement monitoring and alerting
3. Documentation and operational procedures

## 9. Key Design Decisions

### 9.1 Architectural Consistency
- **HTTP-triggered function**: Maintains consistency with existing Data Ingesting app
- **Modular design**: Same shared module pattern for maintainability
- **Comprehensive logging**: Especially for green-boxed components
- **Step-by-step validation**: Mirrors existing error handling approach

### 9.2 Data Processing Approach
- **Pandas-based processing**: Efficient for CSV manipulation and column operations
- **Temporary file management**: Serverless-friendly approach using temporary directories
- **Column positioning**: Explicit handling of leftmost (surrogate key) and rightmost (audit) column placement

### 9.3 Future Extensibility
- **Configurable destinations**: Easy to change output container in future iterations
- **Placeholder logic**: Surrogate key calculation ready for business rule implementation
- **Modular transformations**: Easy to add additional processing steps

## 10. Success Criteria

### 10.1 Functional Requirements
- ✅ Successfully read CSV files from `fimastsales-decompressed` container
- ✅ Implement file consolidation logic with proper logging
- ✅ Add surrogate key as leftmost column
- ✅ Append audit columns as rightmost columns
- ✅ Upload processed files to destination container

### 10.2 Non-Functional Requirements
- ✅ Comprehensive logging for all green-boxed components
- ✅ Robust error handling and step-by-step validation
- ✅ Consistent architectural patterns with existing function app
- ✅ Scalable and maintainable code structure
- ✅ Ready for future configuration changes

## 11. Processing Requirements Summary

### Input Source
- **Container**: `fimastsales-decompressed` (Azure Blob Storage)
- **File Types**: Unzipped CSV files from Data Ingesting pipeline
- **File Patterns**: `FIMASTSALES_*` and `FIMASTSALES2TEST_*`

### Processing Steps (Sequential)
1. **Process CSV** - Read and validate CSV structure
2. **File Consolidation Logic** (GREEN BOX) - Single file vs. multi-file decision
3. **Add Surrogate Key** - Insert as FIRST column (leftmost position)
4. **Raw CSV Enhanced** (GREEN BOX) - Log successful enhancement
5. **Append Audit Columns** - Add 3 columns to rightmost positions:
   - Source Date/Time (from filename/metadata)
   - Created Date/Time (current timestamp)
   - Modified Date/Time (same as Created Date/Time)
6. **Transformation Completion** (GREEN BOX) - Log "Landed Data to Bronze Completed"

### Output Destination
- **Container**: `fimastsales-decompressed` (temporary - will change in future)
- **File Format**: Enhanced CSV with surrogate key and audit columns

### Critical Implementation Notes
- **Surrogate Key Position**: Must be FIRST column (extreme left), not typical rightmost
- **Surrogate Key Logic**: TBD - use placeholder (sum of first two numeric columns)
- **Green Box Logging**: Comprehensive logging required for consolidation logic, enhancement status, and completion
- **Future Flexibility**: Design for easy destination container changes

This implementation plan provides a comprehensive roadmap for creating the Data Processing function app that seamlessly integrates with the existing Data Ingesting function app while following the same architectural patterns and design principles.
