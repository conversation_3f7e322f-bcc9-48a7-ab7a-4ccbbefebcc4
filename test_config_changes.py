#!/usr/bin/env python3
"""
Test script to validate the configuration changes for the new folder structure
"""

import sys
import os

# Add the shared directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'azure-csv-processing', 'shared'))

def test_configuration():
    """Test the updated configuration"""
    print("=== Testing Configuration Changes ===")
    
    try:
        # Import the updated configuration
        from azure_config import (
            SOURCE_CONTAINER, 
            OUTPUT_CONTAINER,
            SOURCE_FOLDER_PATH,
            OUTPUT_FOLDER_PATH,
            get_source_blob_path,
            get_output_blob_path,
            get_source_folder_path,
            get_output_folder_path
        )
        
        print("✓ Configuration imports successful")
        
        # Test container configuration
        print(f"Source Container: {SOURCE_CONTAINER}")
        print(f"Output Container: {OUTPUT_CONTAINER}")
        
        # Test folder paths
        print(f"Source Folder Path: {SOURCE_FOLDER_PATH}")
        print(f"Output Folder Path: {OUTPUT_FOLDER_PATH}")
        
        # Test helper functions
        test_filename = "FIMASTSALES_2025-01-01.csv"
        source_blob_path = get_source_blob_path(test_filename)
        output_blob_path = get_output_blob_path(test_filename)
        
        print(f"Test filename: {test_filename}")
        print(f"Source blob path: {source_blob_path}")
        print(f"Output blob path: {output_blob_path}")
        
        # Validate expected paths
        expected_source = "FIMAST-SALES/fimastsales-decompressed/FIMASTSALES_2025-01-01.csv"
        expected_output = "FIMAST-SALES/fimastsales-enhanced/FIMASTSALES_2025-01-01.csv"
        
        if source_blob_path == expected_source:
            print("✓ Source blob path is correct")
        else:
            print(f"❌ Source blob path mismatch. Expected: {expected_source}, Got: {source_blob_path}")
            
        if output_blob_path == expected_output:
            print("✓ Output blob path is correct")
        else:
            print(f"❌ Output blob path mismatch. Expected: {expected_output}, Got: {output_blob_path}")
            
        # Test folder path functions
        if get_source_folder_path() == SOURCE_FOLDER_PATH:
            print("✓ Source folder path function works correctly")
        else:
            print("❌ Source folder path function error")
            
        if get_output_folder_path() == OUTPUT_FOLDER_PATH:
            print("✓ Output folder path function works correctly")
        else:
            print("❌ Output folder path function error")
            
        print("\n=== Configuration Test Summary ===")
        print("✓ All configuration changes appear to be working correctly")
        print("✓ Container updated to 'sftp-landing'")
        print("✓ Source folder: FIMAST-SALES/fimastsales-decompressed")
        print("✓ Destination folder: FIMAST-SALES/fimastsales-enhanced")
        print("✓ Helper functions working properly")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_blob_manager_imports():
    """Test that blob manager imports work with new functions"""
    print("\n=== Testing Blob Manager Imports ===")
    
    try:
        from blob_manager import (
            list_csv_files_in_container,
            list_parquet_files_in_container,
            upload_csv_to_blob,
            upload_parquet_to_blob,
            ensure_destination_folder_exists
        )
        
        print("✓ Blob manager imports successful")
        print("✓ Updated functions available:")
        print("  - list_csv_files_in_container (with folder path support)")
        print("  - list_parquet_files_in_container (with folder path support)")
        print("  - upload_csv_to_blob (with destination folder auto-creation)")
        print("  - upload_parquet_to_blob (with destination folder auto-creation)")
        print("  - ensure_destination_folder_exists (new function)")
        
        return True
        
    except ImportError as e:
        print(f"❌ Blob manager import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Blob manager test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing updated configuration for new folder structure...")
    print("Expected structure:")
    print("  Source: sagedw/sftp-landing/FIMAST-SALES/fimastsales-decompressed")
    print("  Destination: sagedw/sftp-landing/FIMAST-SALES/fimastsales-enhanced")
    print()
    
    config_success = test_configuration()
    blob_success = test_blob_manager_imports()
    
    if config_success and blob_success:
        print("\n🎉 All tests passed! Configuration is ready for deployment.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the configuration.")
        sys.exit(1)
